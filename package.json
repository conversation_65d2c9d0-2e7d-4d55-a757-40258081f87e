{"name": "loky-backend", "version": "1.0.0", "description": "Rest APIs for Loky's Terminal", "main": "app.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rm -rf dist && tsc", "start": "node dist/app.js", "start:dev": "npm run build && npm run start", "start:watch": "nodemon --watch src -e ts --exec npm run start:dev", "migration:generate": "sh scripts/generate_migration.sh", "seeder:generate": "sh scripts/generate_seeder.sh", "db:create": "npx sequelize-cli db:create --env $ENVIRONMENT", "db:migrate": "npx sequelize-cli db:migrate --env $ENVIRONMENT", "db:seed": "npx sequelize-cli db:seed:all --env $ENVIRONMENT", "cron:publishAIPost": "node dist/services/cron/PublishAIPost.js", "onetimer:flushCache": "node dist/services/oneTimer/FlushCache.js", "onetimer:SyncColumnWithExtended": "node dist/services/oneTimer/SyncTokenDetailColumnWithExtended.js", "onetimer:PopulateCategoryMaps": "node dist/services/oneTimer/PopulateCategoryMaps.js", "onetimer:PopulateDevWalletAge": "node dist/services/oneTimer/PopulateDevWalletAge.js", "onetimer:PopulateTokenLaunchDate": "node dist/services/oneTimer/PopulateTokenLaunchDate.js", "onetimer:PopulateMissingTokenData": "node dist/services/oneTimer/PopulateMissingTokenData.js", "onetimer:BackfillTokenPreGraduateAddressMap": "node dist/services/oneTimer/BackfillTokenPreGraduateAddressMap.js", "onetimer:PopulateTokenLiquidity": "node dist/services/oneTimer/PopulateTokenLiquidity.js", "onetimer:walletLoginAndWhitelist": "node dist/services/oneTimer/WalletLoginAndWhitelist.js", "cron:publishAIReplies": "node dist/services/cron/PublishAIReplies.js", "cron:refreshTokenDetails": "node dist/services/cron/RefreshTokenDetailsData.js", "cron:tokenMarketDetails": "node dist/services/cron/TokenMarketDetailsCron.js", "cron:publishTokenSummary": "node dist/services/cron/PublishTokenSummaryCron.js", "cron:populateCandleData": "node dist/services/cron/PopulateCandleDataCron.js", "cron:publishSignalCron": "node dist/services/cron/PublishSignalCron.js", "cron:populateMindshareData": "node dist/services/cron/PopulateMindshareDataCron.js", "cron:populateRugScannerDataCron": "node dist/services/cron/PopulateRugScannerDataCron.js", "cron:gameReplyAgent": "node dist/services/gameReplyAgent/index.js", "cron:populateTokenDetails": "node dist/services/cron/PopulateTokenDetailsCron.js", "cron:populateAdvanceLQA": "node dist/services/cron/PopulateAdvanceLQA.js", "cron:PopulateDevBundleAndTopHoldersData": "node dist/services/cron/PopulateDevBundleAndTopHoldersData.js", "cron:PopulateExtendedTAData": "node dist/services/cron/PopulateExtendedTACron.js", "cron:populateTokenStakedData": "node dist/services/cron/PopulateTokenStakedDataCron.js", "cron:simulateAPIUsage": "node dist/services/cron/SimulateAPIUsageCron.js", "cron:PopulateGenesisTokensData": "node dist/services/cron/PopulateGenesisAgentsFromVirtualsCron.js", "cron:UpdateGenesisLQAData": "node dist/services/cron/UpdateGenesisLQADataCron.js", "cron:populateTokenPools": "node dist/services/cron/PopulateTokenPoolsCron.js", "cron:populateWalletSafetyScore": "node dist/services/cron/PopulateWalletSafetyScore.js"}, "repository": {"type": "git", "url": "git+https://github.com/dapplooker/loky-backend.git"}, "keywords": ["loky", "ai-agent", "blockchain-automation", "web3-automation"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/dapplooker/loky-backend/issues"}, "homepage": "https://github.com/dapplooker/loky-backend#readme", "dependencies": {"@azure/identity": "4.5.0", "@azure/openai": "2.0.0", "@hitesh23k/cache": "1.0.8-beta.9", "@solana/spl-token": "0.4.12", "@solana/web3.js": "1.98.0", "@types/web3": "1.0.20", "@virtuals-protocol/game": "0.1.11", "@virtuals-protocol/game-twitter-plugin": "0.1.11", "aws-sdk": "2.1637.0", "bignumber.js": "4.0.4", "bs58": "6.0.0", "cookie-parser": "1.4.6", "cors": "2.8.5", "csurf": "1.11.0", "ethers": "6.13.4", "express": "4.19.2", "express-sanitized": "0.5.1", "express-session": "1.18.1", "helmet": "7.1.0", "html-to-text": "9.0.5", "millify": "6.1.0", "morgan": "1.10.0", "node-fetch": "2.6.1", "nodemon": "3.1.2", "openai": "4.72.0", "pg": "8.11.5", "puppeteer": "24.4.0", "puppeteer-extra": "3.3.6", "puppeteer-extra-plugin-stealth": "2.11.2", "sanitize-html": "2.13.0", "sequelize": "6.37.3", "siwe": "2.3.2", "telegraf": "4.16.3", "ts-node": "10.9.2", "tweetnacl": "1.0.3", "twitter-api-v2": "1.18.2", "web3": "4.16.0", "web3-utils": "4.3.0", "winston": "3.13.0", "words-to-numbers": "1.5.1"}, "devDependencies": {"@types/cookie-parser": "1.4.7", "@types/cors": "2.8.17", "@types/csurf": "1.11.5", "@types/express": "4.17.21", "@types/express-session": "1.18.0", "@types/html-to-text": "9.0.4", "@types/morgan": "1.9.9", "@types/node": "20.14.7", "@types/node-fetch": "2.6.11", "@types/puppeteer": "5.4.7", "@types/sanitize-html": "2.11.0", "@types/uuid": "10.0.0", "sequelize-cli": "6.6.2", "typescript": "5.4.5"}}