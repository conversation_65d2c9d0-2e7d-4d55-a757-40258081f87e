import AIAgentConstant from "../../lib/constant/AIAgentConstant";
import ResponseHelper from "../../lib/helper/ResponseHelper";
import Logger from "../../lib/Logger";
import { ErrorResponse, SignalModelAttributes, SuccessResponse } from "../../lib/Types";
import ServiceBase from "../Base";
import Postgres from "../../lib/Postgres";
import fetch from 'node-fetch';
import { CoinGeckoHelper } from "../../lib/helper/CoinGeckoHelper";
import Signal from "../../models/Signal";
import TokenDetails from "../../models/TokenDetails";
import Basic from "../../lib/helper/Basic";
import TokenPools from "../../models/TokenPools";
import MarketStatisticsData from "../MarketStatisticsData";
import TokenCandleData from "../../models/TokenCandleData";
import SignalConstant from "../../lib/constant/SignalConstant";
import TokenConstants from "../../lib/constant/TokenConstants";
import VirtualTokenDetailsByTokenIds from "../../lib/cache/VirtualTokenDetailsByTokenIds";
import TokenDetailsByTokenAddresses from "../../lib/cache/TokenDetailsByTokenAddresses";
import TokenCategoryMap from "../../models/TokenCategoryMap";
import TokenAddressMap from "../../models/TokenAddressMap";
import ErrorLogConstant from "../../lib/constant/ErrorLog";
import GeneralConstant from "../../config/GeneralConstant";
import TokenDetailsByTickers from "../../lib/cache/TokenDetailsByTickers";
import CategoriesByTokenIds from "../../lib/cache/CategoriesByTokenIds";
import { LokyTerminalHelper } from "../../lib/helper/LokyTerminalHelper";
import { EtherscanHelper } from "../../lib/helper/EtherScanHelper";
import VirtualTokenDetailsByTokenAddresses from "../../lib/cache/VirtualTokenDetailsByTokenAddresses";
import GenesisLqaData from "../../models/GenesisLqaData";
import UserWatchlistGenesis from "../../models/UserWatchListGenesis";
/**
 * Service for delete stale token data and populate latest
 */
export default class RefreshTokenDetails extends ServiceBase {

    private processPrototype: Boolean = true;

    private signalModel: Signal;

    private tokenDetailsModel: TokenDetails;

    private tokenPoolsModel: TokenPools;

    private tokenCandleDataModel: TokenCandleData;

    private tokenCategoryMapModel: TokenCategoryMap;

    private tokenAddressMapModel: TokenAddressMap;

    private genesisLqaDataModel: GenesisLqaData;

    private userWatchlistGenesisModel: UserWatchlistGenesis;

    private tokenIdVsAddressesMap: Record<string, any>

    private updatedTokenIds: string[] = [];

    private updatedTokenAddresses: string[] = [];

    private newlyAddedTokenTickers: string[] = [];

    private newlyAddedTokenIds: string[] = [];

    private populateVirtualsMarketData: MarketStatisticsData;

    constructor(params: { processPrototype: Boolean }) {
        super(params);
        const oThis = this;
        oThis.processPrototype = params.processPrototype;
        oThis.tokenIdVsAddressesMap = {}
        oThis.populateVirtualsMarketData = new MarketStatisticsData({ entityType: AIAgentConstant.entityType.VIRTUAL_ECOSYSTEM_TOTAL_MARKET_CAP });
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        oThis.signalModel = (await Postgres.getDbModels()).signal;
        oThis.tokenPoolsModel = (await Postgres.getDbModels()).tokenPools;
        oThis.tokenDetailsModel = (await Postgres.getDbModels()).tokenDetails;
        oThis.tokenCandleDataModel = (await Postgres.getDbModels()).tokenCandleData;
        oThis.tokenAddressMapModel = (await Postgres.getDbModels()).tokenAddressMap;
        oThis.tokenCategoryMapModel = (await Postgres.getDbModels()).tokenCategoryMap;

        await oThis.populateVirtualsMarketData.perform();

        // Required only for virtual sentient tokens
        try {
            await oThis.syncVirtualEcosystemTokenIdsAndDataSource();
        } catch (error) {
            Logger.error(`RefreshTokenDetails::servicePerform::Syncing failed for data source and tokens id | Error: ${JSON.stringify(error)}`);
        }

        await oThis.fetchAndPopulateNewTokens();

        await oThis.flushCache();

        return oThis.prepareResponse();
    }

    private async fetchAndPopulateNewTokens(): Promise<void> {
        const oThis = this;
        const batchSize = 500;

        for (const [category, endpoint] of Object.entries(AIAgentConstant.cryptoAPI)) {

            if (category == AIAgentConstant.categoryVirtualEcosystemPrototype && !oThis.processPrototype) {
                Logger.warn(`RefreshTokenDetails::fetchAndPopulateNewTokens::Skipping process prototype tokens. | processPrototype: ${oThis.processPrototype}`);
                continue;
            }

            const newlyAddedTokens: any[] = [];

            let tokenDataFromAPI = await oThis.fetchTokenData(category, endpoint);

            const existingTokens = await oThis.tokenDetailsModel.getExistingTokens(category);
            const existingTokenIds = existingTokens.tokenIds;
            const existingTokenAddresses = existingTokens.tokenAddresses;

            for (const token of tokenDataFromAPI) {
                if (existingTokenAddresses[token.token_address]) {
                    Logger.warn(`RefreshTokenDetails::fetchAndPopulateNewTokens::Token address already exists '${token.token_address}' for category '${category}'`);
                    Logger.info(`RefreshTokenDetails::fetchAndPopulateNewTokens::Updating token id '${token.token_id}' with token address '${token.token_address}'`);
                    try {
                        await oThis.tokenDetailsModel.update(
                            { token_id: token.token_id },
                            { token_address: token.token_address }
                        );
                        oThis.updatedTokenIds.push(token.token_id);
                        oThis.updatedTokenAddresses.push(token.token_address);
                    } catch (e) {
                        Logger.error(`RefreshTokenDetails::fetchAndPopulateNewTokens::Update failed for token id '${token.token_id}'`);
                    }
                    continue;
                }
                if (!existingTokenIds[token.token_id]) {
                    newlyAddedTokens.push(token);
                    oThis.newlyAddedTokenTickers.push(token.token);
                    oThis.newlyAddedTokenIds.push(token.token_id);
                }
            }

            if (newlyAddedTokens.length > 0) {
                Logger.info(`RefreshTokenDetails::collectAndPopulateLatestTokens::Inserting ${newlyAddedTokens.length} data rows`);
                for (let i = 0; i < newlyAddedTokens.length; i += batchSize) {
                    const tokensBatch = newlyAddedTokens.slice(i, i + batchSize);
                    try {
                        await oThis.tokenDetailsModel.bulkCreate(tokensBatch);
                        await oThis.populateNewlyAddedTokenMaps(tokensBatch);
                        if (category != AIAgentConstant.categoryVirtualEcosystemPrototype) {
                            await oThis.updateNewlyAddedTokenMarketData(tokensBatch);
                        } else {
                            // For prototype tokens, only update the launch date
                            await oThis.updatePrototypeTokensLaunchDate(tokensBatch);
                        }

                        Logger.info(`RefreshTokenDetails::collectAndPopulateLatestTokens::Inserted batch ${i / batchSize + 1} successfully.`);
                    } catch (error: any) {
                        Logger.error(`RefreshTokenDetails::collectAndPopulateLatestTokens::Error inserting batch ${i / batchSize + 1} ${JSON.stringify(error)}`)
                        const errorLogModel = (await Postgres.getDbModels()).errorLog;
                        const errorObjectWithStack = ResponseHelper.error(['somethingWentWrong'], {
                            params: { category, endpoint },
                            error: error.toString(),
                            stack: error.stack
                        });
                        await errorLogModel.create({
                            id: undefined,
                            appType: ErrorLogConstant.invertedAppTypes[ErrorLogConstant.appType],
                            severity: ErrorLogConstant.invertedSeverities[ErrorLogConstant.mediumSeverity],
                            status: ErrorLogConstant.invertedStatuses[ErrorLogConstant.pendingStatus],
                            machineIp: GeneralConstant.machineIp,
                            data: JSON.stringify(errorObjectWithStack),
                        });
                    }
                }
            } else {
                Logger.warn(`RefreshTokenDetails::collectAndPopulateLatestTokens::No new tokens found to process for category ${category} - length ${newlyAddedTokens.length} `);
            }
        }
    }

    private async populateNewlyAddedTokenMaps(tokensBatch: any) {
        const oThis = this;
        const formattedTokenCategoryMapData = [];
        try {
            for (const token of tokensBatch) {
                const tokenId = token.token_id;
                const tokenAddress = token.token_address;
                const categoryId = token.category;

                const tokenAddressMapRecord = await oThis.tokenAddressMapModel.getByTokenId(tokenId);
                if (!tokenAddressMapRecord) {
                    Logger.info(`RefreshTokenDetails::fetchTokenData::Inserting token address map for token id: ${tokenId}`);
                    let addressMap = {};

                    if (token?.network == AIAgentConstant.network.ETH) {
                        addressMap = { ethereumAddress: tokenAddress }
                    } else if (AIAgentConstant.ecosystemCategoriesMap[AIAgentConstant.categoryBaseEcosystem].includes(categoryId)) {
                        addressMap = { baseAddress: tokenAddress }
                    } else if (AIAgentConstant.ecosystemCategoriesMap[AIAgentConstant.categorySolanaEcosystem].includes(categoryId)) {
                        addressMap = { solanaAddress: tokenAddress }
                    }
                    await oThis.tokenAddressMapModel.create(
                        {
                            tokenId: tokenId,
                            ...addressMap
                        }
                    )
                } else {
                    Logger.warn(`RefreshTokenDetails::fetchTokenData::Token address map is already exist for token id: ${tokenId}`);
                }

                await Promise.all([
                    oThis.genesisLqaDataModel.update(
                        { tokenAddress: tokenAddress },
                        { tokenId: tokenId }
                    ),
                    oThis.userWatchlistGenesisModel.update(
                        { tokenAddress: tokenAddress },
                        { tokenId: tokenId }
                    )
                ]);

                formattedTokenCategoryMapData.push({
                    tokenId: tokenId,
                    category: categoryId
                });
            }
            Logger.info(`RefreshTokenDetails::fetchTokenData::Inserting in bulk token category map data of length: ${formattedTokenCategoryMapData.length}`);
            await oThis.tokenCategoryMapModel.bulkCreate(formattedTokenCategoryMapData);
        } catch (error: any) {
            Logger.error(`RefreshTokenDetails::fetchTokenData::Error in populate new token maps ${JSON.stringify(error.message)}`);
            throw new Error(`RefreshTokenDetails::fetchTokenData::Error in populate new token maps ${JSON.stringify(error.message)}`);
        }
    }

    private async updateNewlyAddedTokenMarketData(tokensBatch: any) {
        const oThis = this;
        const coingeckoTokenIds = [];
        const tokenMarketDataMapCG: { [key: string]: any } = {};
        const tokenMarketDataMapDL: { [key: string]: any } = {};

        const tokenExtendedDetails = await oThis.getLaunchDateFromExtendedDetails(tokensBatch.map((token: any) => token.token_id));
        const tokenExtendedDetailsMap = new Map<string, any>();
        tokenExtendedDetails.forEach((item: any) => {
            tokenExtendedDetailsMap.set(item.token_id, new Date(item.token_launch_date) || null);
        });

        try {
            for (const token of tokensBatch) {
                const tokenId = token.token_id;

                if (token.category == AIAgentConstant.categorySolanaEcosystem
                    || token.category == AIAgentConstant.categorySolanaMeme
                    || token.category == AIAgentConstant.categoryBaseEcosystem
                ) {
                    coingeckoTokenIds.push(tokenId);
                    continue;
                }

                // TODO: Get token in bulk
                const tokenDetails = await oThis.tokenDetailsModel.getByTokenId(tokenId);
                const dataSource = tokenDetails?.data_population_source;

                // Collecting Coingecko token ids
                if (dataSource == AIAgentConstant.populateDataSourceMap.COINGECKO) {
                    coingeckoTokenIds.push(tokenId);
                } else {
                    // Collecting data from DappLooker API (GeckoTerminal)
                    const res = await fetch(`${AIAgentConstant.sentientVirtualTokenMarketDataUrl}?api_key=${AIAgentConstant.aiUserAPIKey}&output_format=json&filterParams={"token_id":"${tokenId}"}`);
                    if (!res.ok) {
                        Logger.error(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Failed to fetch data: ${res.status} ${res.statusText}`);
                        continue;
                    }
                    const data = await res.json() as any;
                    if (data && data[0] && data[0].token_id == tokenId) {
                        tokenMarketDataMapDL[tokenId] = data[0]
                    }
                }
                await Basic.sleep(500);
            }
        } catch (error: any) {
            Logger.error(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Error | ${JSON.stringify(error.message)}`);
            throw new Error(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Error | ${JSON.stringify(error.message)}`);
        }

        try {
            // Collecting data from CoinGecko API
            const cgEndpoint = `api/v3/coins/markets?vs_currency=usd&ids=${coingeckoTokenIds.join(',')}&price_change_percentage=1h,24h,7d,30d`;
            let tokensMarketData: any[] = await new CoinGeckoHelper().getCoinDataByTokenIds(cgEndpoint, true);
            tokensMarketData.forEach(token => {
                tokenMarketDataMapCG[token.id] = token;
            });

            for (const token of tokensBatch) {
                let tokenDetails = null;
                const tokenId = token.token_id;
                const tokenDataCG = tokenMarketDataMapCG[tokenId];
                const tokenDataDL = tokenMarketDataMapDL[tokenId];

                const dappLookerMetrics = oThis.extractMetricsFromDappLooker(tokenDataDL, tokenId);

                if (tokenDataCG) {
                    tokenDetails = oThis.formatTokenDetailsFromCoinGecko(tokenId, tokenDataCG, dappLookerMetrics, tokenExtendedDetailsMap);
                } else if (tokenDataDL?.token_id == tokenId) {
                    tokenDetails = oThis.formatTokenDetailsFromDappLooker(tokenId, tokenDataDL, dappLookerMetrics, tokenExtendedDetailsMap);
                }

                if (tokenDetails) {
                    try {
                        Logger.info(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Updating token: ${token.token_id}, source: ${token.data_population_source}`);
                        await oThis.tokenDetailsModel.update(
                            {
                                ...tokenDetails,
                            },
                            {
                                token_id: token.token_id
                            }
                        );

                        oThis.updatedTokenIds.push(token.token_id);
                        const tokenAddress = AIAgentConstant.getFormattedAddress(token.token_address, token.network);
                        oThis.updatedTokenAddresses.push(tokenAddress);
                    } catch (e: any) {
                        Logger.error(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Error updating record. Exception ${JSON.stringify(e)}`);
                        throw e;
                    }
                } else {
                    Logger.info(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Token market data not found token: ${token.token_id}, source: ${token.data_population_source}`);

                    // Even if market data is not found, update the launch date if it's available in tokenExtendedDetailsMap
                    const launchDate = tokenExtendedDetailsMap.get(tokenId);
                    if (launchDate) {
                        try {
                            Logger.info(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Updating only launch date for token ${tokenId}`);

                            const updateResult = await oThis.tokenDetailsModel.update(
                                { launch_date: launchDate },
                                { token_id: tokenId }
                            );

                            if (updateResult && updateResult[0] > 0) {
                                oThis.updatedTokenIds.push(tokenId);
                                const tokenAddress = AIAgentConstant.getFormattedAddress(token.token_address, token.network);
                                oThis.updatedTokenAddresses.push(tokenAddress);
                            }
                        } catch (e) {
                            Logger.error(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Error updating launch date for token ${tokenId}: ${e}`);
                        }
                    }
                }

                // Always attempt to insert token pools for all newly added tokens
                const tokenAddress = token.token_address;
                const network = token.network;
                const category = token.category;
                if (tokenAddress && network && category != AIAgentConstant.categoryVirtualEcosystemPrototype) {
                    try {
                        await oThis.insertTokenPools(tokenAddress, tokenId, network);
                    } catch (e) {
                        Logger.error(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Error inserting token pools for token ${tokenId}: ${e}`);
                    }
                }
            }
        } catch (error: any) {
            Logger.error(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Error | ${JSON.stringify(error.message)}`);
            throw new Error(`RefreshTokenDetails::updateNewlyAddedTokenMarketData::Error | ${JSON.stringify(error.message)}`);
        }
    }

    private async fetchTokenData(category: string, endpoint: string) {
        const oThis = this;
        const collectedData: any = [];
        try {
            Logger.info(`RefreshTokenDetails::fetchTokenData::Collecting new token data for category: ${category}`);
            const networkId = AIAgentConstant.network[AIAgentConstant.categoryVsNetworkMap[category]];
            Logger.info(`RefreshTokenDetails::fetchTokenData::Fetching data from endpoint: ${endpoint} | category: ${category}`);

            let data: any[] = [];
            if (category == AIAgentConstant.categoryVirtualEcosystemPrototype) {
                let page = 0;
                const limit = 2000;
                while (true) {
                    Logger.info(`Fetching prototype tokens for page ${page}`);
                    const response = await fetch(`${endpoint}&filterParams={"page":${page}}`);
                    if (!response.ok) {
                        Logger.error(`RefreshTokenDetails::fetchTokenData::Failed to fetch data from ${endpoint}, page: ${page} :: ${response.statusText}`);
                    }
                    const res = await response.json();
                    Logger.info(`Fetching data length '${res?.length}'`);
                    data = data.concat(res);
                    if ((res?.length || 0) < limit) {
                        break;
                    }
                    page++;
                }
            } else {
                const response = await fetch(endpoint);
                if (!response.ok) {
                    Logger.error(`RefreshTokenDetails::fetchTokenData::Failed to fetch data from ${endpoint}: ${response.statusText}`);
                }
                data = await response.json();
            }

            const categories = category == AIAgentConstant.categorySolanaEcosystem
                ? [AIAgentConstant.categorySolanaEcosystem, AIAgentConstant.categorySolanaMeme]
                : [category];

            const tokenDetailsByTokenIds = await oThis.tokenDetailsModel.getByCategories(categories);

            for (let i = 0; i < data.length; i++) {
                const token = data[i];
                const tokenDetails = tokenDetailsByTokenIds[token.token_id];
                const imageUrl = tokenDetails?.image ? tokenDetails?.image : null;
                const description = tokenDetails?.description ? tokenDetails?.description : null;
                // Based on token category we can have default address and network
                // 1. Base for Virtual
                // 2. Solana for Solana eco and meme
                // 3. Ethereum for AI Meme, Meme, Crypto category
                // In case of duplicate, lets keep order of below list
                // Category priority list: Virtual, Solana Meme, Solana Eco, Crypto, Meme
                if (!tokenDetails) {
                    const devWallet = oThis.tokenIdVsAddressesMap[token.token_id]?.devWallet || null;
                    const td = {
                        token: token.token_symbol,
                        token_id: token.token_id,
                        token_name: token.token_name,
                        category: category,
                        image: token.image_url || imageUrl,
                        description: description,
                        type: 1, // Added from chart
                        token_address: AIAgentConstant.getFormattedAddress(token.token_address, networkId),
                        dev_wallet: devWallet,
                        usd_price: token?.usd_price,
                        mcap: token?.market_cap,
                        volume_24h: token?.total_volume || 0,
                        network: token?.chain || networkId,
                        data_population_source: (category == AIAgentConstant.categoryVirtualEcosystemPrototype)
                            ? AIAgentConstant.populateDataSourceMap.DAPPLOOKER
                            : AIAgentConstant.populateDataSourceMap.COINGECKO,
                        dev_wallet_funded_on: devWallet ? await EtherscanHelper.getWalletFundedDate(devWallet) : null,
                    }
                    collectedData.push(td);
                } else {
                    const tokenAddress = tokenDetails?.token_address;
                    if (!tokenAddress) {
                        const ta = token.token_address ? AIAgentConstant.getFormattedAddress(token.token_address, networkId) : null;
                        if (ta) {
                            Logger.info(`RefreshTokenDetails::fetchTokenData::Updating token address '${token.token_address}' where token id '${token.token_id}'`);
                            await oThis.tokenDetailsModel.update(
                                { token_address: ta },
                                { token_id: token.token_id }
                            );
                            oThis.updatedTokenIds.push(token.token_id);
                            oThis.updatedTokenAddresses.push(token.token_address);
                        }
                    }
                    if (category == AIAgentConstant.categoryVirtualEcosystem) {
                        if (tokenDetails.category == AIAgentConstant.categoryVirtualEcosystemPrototype) {
                            Logger.info(`RefreshTokenDetails::fetchTokenData::Updateing existing prototype token id '${token.token_id}' to sentient.`);
                            await oThis.tokenDetailsModel.update(
                                {
                                    token_address: AIAgentConstant.getFormattedAddress(token.token_address, networkId),
                                    category: AIAgentConstant.categoryVirtualEcosystem,
                                },
                                { token_id: token.token_id }
                            );
                        }
                    }
                }
            }
        } catch (error: any) {
            Logger.error(`RefreshTokenDetails::collectTokenData::Error processing ${endpoint}`, JSON.stringify(error));
            throw new Error(`RefreshTokenDetails::collectTokenData::Failed to fetch data from APIs with error ${JSON.stringify(error.message)}`);
        }
        Logger.debug(`RefreshTokenDetails::collectTokenData:: Collected new token for refresh: ${JSON.stringify(collectedData)}`);
        return collectedData;
    }

    private async syncVirtualEcosystemTokenIdsAndDataSource() {
        const oThis = this;
        const duplicateTokenIds = []
        const coinGeckoListedTokenIds = []

        try {
            const existingTokenIds = await oThis.tokenDetailsModel.getTokenIdsByCategory([AIAgentConstant.categoryVirtualEcosystem, AIAgentConstant.categoryVirtualEcosystemGenesis]);
            const extendedTokenData: any[] = await oThis.fetchDataFromTokenExtendedDetailsChart();

            const tempTokenIdVsTokenDataMapFromExtendedData = new Map<string, typeof extendedTokenData[0]>();

            for (let i = 0; i < extendedTokenData.length; i++) {
                const token = extendedTokenData[i];
                if (token.coingecko_token_id !== null && token.coingecko_token_id !== '') {
                    tempTokenIdVsTokenDataMapFromExtendedData.set(token.temp_token_id, token);
                }
                oThis.tokenIdVsAddressesMap[token.token_id] = {
                    tokenAddress: token.token_address,
                    devWallet: token.dev_wallet
                };
            }

            Logger.debug(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: tokenIdToTokenAddress: ${JSON.stringify(oThis.tokenIdVsAddressesMap)} `);

            const cgTokenIdsInExtendedData = new Set(
                extendedTokenData
                    .filter((item: any) => item.coingecko_token_id !== null && item.coingecko_token_id !== '')
                    .map((item: any) => item.coingecko_token_id)
            );
            Logger.debug(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: coinGeckoTokenIds: ${JSON.stringify(Array.from(cgTokenIdsInExtendedData))} `);

            const syncedTokenIdsInTokenDetails: any[] = [];
            const notSyncedTokenIdsInTokenDetails: any[] = [];

            for (const tokenId of existingTokenIds) {
                if (cgTokenIdsInExtendedData.has(tokenId)) {
                    syncedTokenIdsInTokenDetails.push(tokenId);
                } else {
                    notSyncedTokenIdsInTokenDetails.push(tokenId);
                }
            }

            Logger.debug(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: syncedTokenIdsInTokenDetails: ${JSON.stringify(syncedTokenIdsInTokenDetails)} `);
            Logger.debug(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: notSyncedTokenIdsInTokenDetails: ${JSON.stringify(notSyncedTokenIdsInTokenDetails)} `);

            for (const tokenId of notSyncedTokenIdsInTokenDetails) {
                const token = tempTokenIdVsTokenDataMapFromExtendedData.get(tokenId);
                if (token) {
                    if (syncedTokenIdsInTokenDetails.includes(token.coingecko_token_id)) {
                        // Same token has been inserted twice, once from DL, then from CoinGecko. But ids were different as DL was using temp_token_id and CoinGecko was using coingecko_token_id
                        duplicateTokenIds.push(tokenId)
                    } else {
                        Logger.debug(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: Updating tokenId from ${tokenId} to ${token.coingecko_token_id} `);
                        try {
                            await oThis.tokenDetailsModel.update(
                                {
                                    token_id: token.coingecko_token_id,
                                    data_population_source: AIAgentConstant.populateDataSourceMap.COINGECKO
                                },
                                { token_id: tokenId }
                            );

                            await Promise.all([
                                oThis.tokenPoolsModel.update(
                                    { tokenId: token.coingecko_token_id },
                                    { tokenId: tokenId }
                                ),
                                oThis.tokenCandleDataModel.update(
                                    { tokenId: token.coingecko_token_id },
                                    { tokenId: tokenId }
                                )
                            ]);

                            await Promise.all([
                                oThis.genesisLqaDataModel.update(
                                    { tokenId: token.coingecko_token_id },
                                    { tokenId: tokenId }
                                ),
                                oThis.userWatchlistGenesisModel.update(
                                    { tokenId: token.coingecko_token_id },
                                    { tokenId: tokenId }
                                )
                            ]);
                        } catch (error) {
                            Logger.error(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds::Error updating token data from ${tokenId} to ${token.coingecko_token_id}: ${error} `);
                            throw error;
                        }
                        coinGeckoListedTokenIds.push(token.coingecko_token_id) // This is the token that is not synced, and will be used to create the coin gecko listed signal
                    }
                    const tokenAddress = AIAgentConstant.getFormattedAddress(token.token_address, token.network);
                    oThis.updatedTokenAddresses.push(tokenAddress);
                } else {
                    Logger.debug(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: Token not available on CoinGecko: ${tokenId} `);
                    try {
                        await oThis.tokenDetailsModel.update(
                            {
                                data_population_source: AIAgentConstant.populateDataSourceMap.DAPPLOOKER
                            },
                            {
                                token_id: tokenId
                            })
                    } catch (error) {
                        Logger.error(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds::Error updating data_population_source for tokenId ${tokenId}: ${error} `);
                        throw error;
                    }
                }
                oThis.updatedTokenIds.push(tokenId);
            }

            Logger.debug(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: coinGeckoListedTokenIds: ${JSON.stringify(coinGeckoListedTokenIds)} `);
            Logger.debug(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: duplicateTokenIds: ${JSON.stringify(duplicateTokenIds)} `);
            if (coinGeckoListedTokenIds.length > 0) {
                await oThis.createCoinGeckoListedSignalForVirtualEcosystem(coinGeckoListedTokenIds);
            }
            if (duplicateTokenIds.length > 0) {
                await oThis.tokenDetailsModel.deleteRecordsByTokenIds(duplicateTokenIds);
            }
        } catch (error) {
            Logger.error(`RefreshTokenDetails:: syncVirtualEcosystemTokenIds:: Error in sync process: ${JSON.stringify(error)} `);
            throw error;
        }
    }

    private async createCoinGeckoListedSignalForVirtualEcosystem(newTokenIds: any[]) {
        const oThis = this;
        const tokenDetailsByTokenIds = await new VirtualTokenDetailsByTokenIds({
            tokenIds: newTokenIds
        }).fetch();
        Logger.info(`RefreshTokenDetails:: createCoinGeckoListedSignalForVirtualEcosystem:: tokenDetailsByTokenIds::${JSON.stringify(tokenDetailsByTokenIds.data)} `);
        if (!tokenDetailsByTokenIds.success) {
            return oThis.unauthorizedResponse('invalidParams', 's_s_t_rtd_cls_0');
        }
        const tokenDetailsByTokenIdMap = tokenDetailsByTokenIds.data;
        const tokenDetails: any[] = Object.values(tokenDetailsByTokenIdMap);

        for (const token of tokenDetails) {
            const category = TokenConstants.invertedTokenCategoryIdToStrId[TokenConstants.virtualSentientCategoryStrId];
            const subCategory = SignalConstant.invertedSubCategoryIdToStrId[SignalConstant.coinGeckoListedStrId];
            const tokenName = token.token_name;
            const ticker = token.token;
            const text = AIAgentConstant.coinGeckoListedSignalText(tokenName, ticker);
            Logger.info(`RefreshTokenDetails:: createCoinGeckoListedSignalForVirtualEcosystemTokens::Creating coin gecko listed signal for token: ${ticker} `);
            const signalObject: SignalModelAttributes = {
                text,
                category,
                subCategory,
                tokenIds: [token.token_id],
                network: token.network
            }
            await LokyTerminalHelper.sendSignal(oThis.signalModel, signalObject);
        }
    }

    private async fetchDataFromTokenExtendedDetailsChart() {
        const oThis = this;
        const tokenEndpoint = AIAgentConstant.DLSyncDataFromTokenExtendedDetailsChart;

        const res = await fetch(
            `${tokenEndpoint}?api_key=${AIAgentConstant.aiUserAPIKey}&output_format=json`
        );

        if (!res.ok) {
            Logger.error(`RefreshTokenDetails:: fetchDataFromTokenExtendedDetailsChart::Failed to fetch data from DL chart API | Status: ${res.status}, StatusText: ${res.statusText} `);
            throw new Error(`Failed to fetch data: ${res.status} ${res.statusText} `);
        }
        const data = await res.json() as any;
        return data;
    }

    private async getLaunchDateFromExtendedDetails(tokenIds: string[]): Promise<any | null> {
        try {
            const tokenExtendedchartAPI = AIAgentConstant.DLTokenExtendedDetailsChart;
            const result = await fetch(
                `${tokenExtendedchartAPI}?api_key=${AIAgentConstant.aiUserAPIKey}&output_format=json&filterParams={"token_id":"${tokenIds.join(',')}"}`
            );
            if (!result.ok) {
                Logger.error(`RefreshTokenDetails:: collectTokenData::Failed to fetch extended token data from DL chart API | Status: ${result.status}, StatusText: ${result.statusText} `);
                throw new Error(`Failed to fetch data: ${result.status} ${result.statusText} `);
            }
            const data = await result.json() as any;
            return data;
        } catch (error) {
            Logger.error(`RefreshTokenDetails:: getLaunchDateFromExtendedDetails::Error fetching launch date for token ${tokenIds}: ${error} `);
            return null;
        }
    }

    private async insertTokenPools(tokenAddress: string, tokenId: string, network: number) {
        const oThis = this;
        try {
            Logger.info(`RefreshTokenDetails:: insertTokenPools::Fetching pool data for token: ${tokenId} `);
            const poolData = await CoinGeckoHelper.fetchTokenPools(AIAgentConstant.networkMap[network], tokenAddress);

            const poolDataToInsert = poolData.map((pool: any) => {
                return {
                    tokenId: tokenId,
                    poolAddress: pool.attributes.address,
                    poolName: pool.attributes.name,
                    network: network
                }
            });
            Logger.debug(`RefreshTokenDetails:: insertTokenPools:: poolDataToInsert: ${JSON.stringify(poolDataToInsert)} `);

            await oThis.tokenPoolsModel.bulkCreate(poolDataToInsert);
            Logger.debug(`RefreshTokenDetails:: insertTokenPools::Pool data inserted for token id: ${tokenId} `);
        } catch (error) {
            Logger.error(`RefreshTokenDetails:: insertTokenPools::Error inserting token pools: ${error} `);
        }
    }

    private formatTokenDetailsFromCoinGecko(tokenId: string, tokenDataCG: any, dappLookerMetrics: any, tokenExtendedDetailsMap: Map<string, Date>): any {
        const oThis = this;
        return {
            token: tokenDataCG?.symbol?.toUpperCase(),
            token_name: tokenDataCG?.name,
            mcap: tokenDataCG?.market_cap,
            mcap_change_percentage_24h: tokenDataCG?.market_cap_change_percentage_24h,
            fully_diluted_valuation: tokenDataCG?.fully_diluted_valuation,
            volume_24h: tokenDataCG?.total_volume,
            usd_price: tokenDataCG?.current_price,
            price_change_percentage_1h: tokenDataCG?.price_change_percentage_1h_in_currency,
            price_change_percentage_24h: tokenDataCG?.price_change_percentage_24h_in_currency,
            price_change_percentage_7d: tokenDataCG?.price_change_percentage_7d_in_currency,
            price_change_percentage_30d: tokenDataCG?.price_change_percentage_30d_in_currency,
            price_high_24h: tokenDataCG?.high_24h,
            price_ath: tokenDataCG?.ath,
            circulating_supply: tokenDataCG?.circulating_supply,
            total_supply: tokenDataCG?.total_supply,
            volume_change_percentage_7d: dappLookerMetrics.volume_change_percentage_7d,
            volume_change_percentage_30d: dappLookerMetrics.volume_change_percentage_30d,
            mcap_change_percentage_7d: dappLookerMetrics.mcap_change_percentage_7d,
            mcap_change_percentage_30d: dappLookerMetrics.mcap_change_percentage_30d,
            launch_date: tokenExtendedDetailsMap?.get(tokenId) || null
        };
    }

    private formatTokenDetailsFromDappLooker(tokenId: string, tokenDataDL: any, dappLookerMetrics: any, tokenExtendedDetailsMap: Map<string, Date>): any {
        const oThis = this;
        return {
            token_name: tokenDataDL?.token_name,
            mcap: tokenDataDL?.market_cap,
            mcap_change_percentage_24h: tokenDataDL?.last_24_hrs_market_cap_change,
            fully_diluted_valuation: null,
            volume_24h: tokenDataDL?.total_volume,
            usd_price: tokenDataDL?.usd_price,
            price_change_percentage_1h: null,
            price_change_percentage_24h: tokenDataDL?.last_24_hrs,
            price_change_percentage_7d: tokenDataDL?.last_7_day_price_change,
            price_change_percentage_30d: tokenDataDL?.last_30_day_price_change,
            price_high_24h: null,
            price_ath: null,
            circulating_supply: null,
            // total_supply: null,
            volume_change_percentage_7d: dappLookerMetrics.volume_change_percentage_7d,
            volume_change_percentage_30d: dappLookerMetrics.volume_change_percentage_30d,
            mcap_change_percentage_7d: dappLookerMetrics.mcap_change_percentage_7d,
            mcap_change_percentage_30d: dappLookerMetrics.mcap_change_percentage_30d,
            launch_date: tokenExtendedDetailsMap?.get(tokenId) || null
        };
    }

    private extractMetricsFromDappLooker(tokenDataDL: any, tokenId: string): any {
        let metrics = {
            volume_change_percentage_7d: null,
            volume_change_percentage_30d: null,
            mcap_change_percentage_7d: null,
            mcap_change_percentage_30d: null,
            token_address: null
        };

        if (tokenDataDL?.token_id == tokenId) {
            metrics = {
                volume_change_percentage_7d: tokenDataDL?.last_7_day_volume_change,
                volume_change_percentage_30d: tokenDataDL?.last_30_day_volume_change,
                mcap_change_percentage_7d: tokenDataDL?.last_7_day_market_cap_change,
                mcap_change_percentage_30d: tokenDataDL?.last_30_day_market_cap_change,
                token_address: tokenDataDL?.token_address
            };
        }

        return metrics;
    }

    private async updatePrototypeTokensLaunchDate(tokensBatch: any): Promise<void> {
        const oThis = this;
        try {
            Logger.info(`RefreshTokenDetails::updatePrototypeTokensLaunchDate::Processing launch dates for ${tokensBatch.length} prototype tokens`);
            const tokenExtendedDetails = await oThis.getLaunchDateFromExtendedDetails(tokensBatch.map((token: any) => token.token_id));
            const launchDateByTokenIds = new Map<string, string | null>();

            tokenExtendedDetails.forEach((item: any) => {
                const launchDate = item.token_launch_date ? new Date(item.token_launch_date)?.toISOString() : null;
                launchDateByTokenIds.set(item.token_id, launchDate);
            });

            for (const token of tokensBatch) {
                const tokenId = token.token_id;
                const launchDate = launchDateByTokenIds.get(tokenId);

                if (launchDate) {
                    try {
                        Logger.info(`RefreshTokenDetails::updatePrototypeTokensLaunchDate::Updating launch date for token: ${tokenId}`);
                        await oThis.tokenDetailsModel.update(
                            { launch_date: launchDate },
                            { token_id: tokenId }
                        );

                        oThis.updatedTokenIds.push(tokenId);
                        const tokenAddress = AIAgentConstant.getFormattedAddress(token.token_address, token.network);
                        oThis.updatedTokenAddresses.push(tokenAddress);
                    } catch (e: any) {
                        Logger.error(`RefreshTokenDetails::updatePrototypeTokensLaunchDate::Error updating launch date. Exception ${JSON.stringify(e)}`);
                    }
                }
            }
        } catch (error: any) {
            Logger.error(`RefreshTokenDetails::updatePrototypeTokensLaunchDate::Error | ${JSON.stringify(error.message)}`);
        }
    }

    private async flushCache() {
        const oThis = this;
        const uniqueUpdatedTokenIds = Array.from(new Set(oThis.updatedTokenIds));
        const uniqueUpdatedTokenAddresses = Array.from(new Set(oThis.updatedTokenAddresses));
        const uniqueNewlyAddedTokenTickers = Array.from(new Set(oThis.newlyAddedTokenTickers));
        const uniqueNewlyAddedTokenIds = Array.from(new Set(oThis.newlyAddedTokenIds));

        const filteredIdList: string[] = uniqueUpdatedTokenIds.filter((item): item is string => item != null);
        Logger.info(`RefreshTokenDetails:: flushCache::Flushing token cache | tokenIds :: ${JSON.stringify(filteredIdList)} | length :: ${filteredIdList?.length} `);
        await (new VirtualTokenDetailsByTokenIds({ tokenIds: filteredIdList })).clear();

        const filteredAddressList: string[] = uniqueUpdatedTokenAddresses.filter((item): item is string => item != null);
        Logger.info(`RefreshTokenDetails:: flushCache:: Flushing token cache | tokenAddresses :: ${JSON.stringify(filteredAddressList)} | length :: ${filteredAddressList?.length} `);
        await (new TokenDetailsByTokenAddresses({ tokenAddresses: filteredAddressList, category: AIAgentConstant.categoryVirtualEcosystem })).clear();
        await (new VirtualTokenDetailsByTokenAddresses({ tokenAddresses: filteredAddressList })).clear();

        const filteredTickerList: string[] = uniqueNewlyAddedTokenTickers.filter((item): item is string => item != null);
        Logger.info(`RefreshTokenDetails:: flushCache:: Flushing token ticker cache | tokenTickers :: ${JSON.stringify(filteredTickerList)} | length :: ${filteredTickerList?.length} `);
        await (new TokenDetailsByTickers({ tickers: filteredTickerList })).clear();

        const filteredNewlyAddedIdList: string[] = uniqueNewlyAddedTokenIds.filter((item): item is string => item != null);
        Logger.info(`RefreshTokenDetails:: flushCache:: Flushing categories by token ids cache | tokenIds :: ${JSON.stringify(filteredNewlyAddedIdList)} | length :: ${filteredNewlyAddedIdList?.length} `);
        await (new CategoriesByTokenIds({ tokenIds: filteredNewlyAddedIdList })).clear();
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`RefreshTokenDetails:: prepareResponse:: Service execution completed`);
        return ResponseHelper.success({});
    }
}
