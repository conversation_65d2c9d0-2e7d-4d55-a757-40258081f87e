import AIAgentConstant from "../../lib/constant/AIAgentConstant";
import Container from "../../lib/Container";
import ResponseHelper from "../../lib/helper/ResponseHelper";
import Logger from "../../lib/Logger";
import { ErrorResponse, SuccessResponse, LQAHistoricalMetricModelAttributes } from "../../lib/Types";
import ServiceBase from "../Base";
import BasicHelper from "../../lib/helper/Basic";
import LQAHelper from "./LQAHelper";
import { Op } from "sequelize";
import moment from "moment";

export default class PopulateAdvanceLQAMetrics extends ServiceBase {

    constructor(params: {}) {
        super(params);
        const oThis = this;
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;

        await oThis.populateConfidenceMetrics(AIAgentConstant.categoryVirtualEcosystem);

        await oThis.populateConfidenceMetrics(AIAgentConstant.categoryVirtualEcosystemGenesis);

        await oThis.populateConfidenceMetrics(AIAgentConstant.categoryVirtualEcosystemPrototype);

        await oThis.cleanupOlderData();

        return oThis.prepareResponse();
    }

    private async populateConfidenceMetrics(category: string) {
        const oThis = this;
        const limit = 100;
        let skip = 0;
        let batch = 1;
        const tokenDetailsModel = (await Container.get()).models.tokenDetails;
        const lqaHistoricalMetricModel = (await Container.get()).models.lqaHistoricalMetric;
        const extraWhere = { mcap: { [Op.gt]: 10_000 } }
        while (true) {
            const records: LQAHistoricalMetricModelAttributes[] = [];
            Logger.info(`PopulateAdvanceLQAMetrics::populateConfidenceMetrics::Batch ${batch} - Fetching tokens to process for category: '${category}'`);

            const tokens: any[] = await tokenDetailsModel.getTokenDetails(limit, skip, [category], extraWhere);
            Logger.info(`PopulateLQAAnalysis::populateConfidenceMetrics::Processing tokens: ${tokens.length}`);

            const tokenIds = tokens.map(token => token?.token_id);
            const confidenceDatabyTokenIds = await lqaHistoricalMetricModel.getConfidenceChangeDatabyTokenIds(tokenIds);

            for (let i = 0; i < tokens.length; i++) {
                const token = tokens[i];
                const tokenId = token?.token_id;
                const holderChangePercent = token?.bundle_wallet_supply_percentage || 0;
                const devWalletSupplyPercent = token?.bundle_wallet_supply_percentage || 0;
                const mindshareChange = token?.mindshare_3d || 0;
                const volume = token?.volume_24h || 0;
                const priceChange1h = token?.price_change_percentage_1h || 0;
                const priceChange24h = token?.price_change_percentage_24h || 0;
                const mcap = token?.mcap || 0;
                // const mcapChange15m = token?.mcap_change_percentage_15m || 0;
                // const mcapChange1h = token?.mcap_change_percentage_1h || 0;
                // const mcapChange6h = token?.mcap_change_percentage_6h || 0;
                // const mcapChange24h = token?.mcap_change_percentage_24h || 0;

                const tokenMetrics = {
                    holderChangePercent,
                    devWalletSupplyPercent,
                    mindshareChange,
                    volume
                };

                const confidence_15m = LQAHelper.calculateConfidence({ ...tokenMetrics, marketCap: mcap, priceChangePercent: priceChange1h });
                const confidence_1h = LQAHelper.calculateConfidence({ ...tokenMetrics, marketCap: mcap, priceChangePercent: priceChange1h });
                const confidence_6h = LQAHelper.calculateConfidence({ ...tokenMetrics, marketCap: mcap, priceChangePercent: priceChange1h });
                const confidence_24h = LQAHelper.calculateConfidence({ ...tokenMetrics, marketCap: mcap, priceChangePercent: priceChange24h });
                const confidence_change_15m = confidenceDatabyTokenIds?.[tokenId]?.changePercentage15m || 0;
                const confidence_change_1h = confidenceDatabyTokenIds?.[tokenId]?.changePercentage1h || 0;
                const confidence_change_6h = confidenceDatabyTokenIds?.[tokenId]?.changePercentage6h || 0;
                const confidence_change_24h = confidenceDatabyTokenIds?.[tokenId]?.changePercentage24h || 0;
                const reasoningSummary = confidenceDatabyTokenIds?.[tokenId]?.reasoningSummary || null;
                const summaryCreatedAt = confidenceDatabyTokenIds?.[tokenId]?.summaryCreatedAt || null;

                const data = {
                    tokenId,
                    confidence_15m,
                    confidence_1h,
                    confidence_6h,
                    confidence_24h,
                    confidence_change_15m,
                    confidence_change_1h,
                    confidence_change_6h,
                    confidence_change_24h,
                    reasoningSummary,
                    summaryCreatedAt
                }
                Logger.info(`Calculated Data: ${JSON.stringify(data)}`);
                records.push(data);
            }

            if (records.length > 0) {
                Logger.debug(`PopulateLQAAnalysis::populateConfidenceMetrics::Inserting '${records.length}' records.`);
                await lqaHistoricalMetricModel.bulkCreate(records);
            } else {
                Logger.warn(`PopulateLQAAnalysis::populateConfidenceMetrics::Nothing to bulk insert.`);
            }

            if (tokens.length < limit) {
                break;
            };
            skip += limit;
            batch += 1;
            await BasicHelper.sleep(100);
        }
    }

    private async cleanupOlderData() {
        const olderThenDate = moment().subtract(49, 'hours').toDate();
        Logger.info(`PopulateLQAAnalysis::cleanupOlderData::Deleting records older then: ${olderThenDate}`);
        const lqaHistoricalMetricModel = (await Container.get()).models.lqaHistoricalMetric;
        await lqaHistoricalMetricModel.delete({
            createdAt: {
                [Op.lt]: olderThenDate
            }
        });
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`PopulateLQAAnalysis::prepareResponse::Service execution completed`);
        return ResponseHelper.success({});
    }
}
