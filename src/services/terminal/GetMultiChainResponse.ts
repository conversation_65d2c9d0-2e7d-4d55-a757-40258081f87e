import { LokyTerminalBase } from './LokyTerminalBase';
import { VirtualEcosystemTokenDetails } from '../../lib/Types';
import Logger from '../../lib/Logger';
import ResponseHelper from '../../lib/helper/ResponseHelper';
import VirtualProtocolEcosystemPrompts from '../../lib/constant/prompt/terminal/VirtualProtocolEcosystemPrompts';
import NearVirtualProtocolEcosystemPrompts from '../../lib/constant/prompt/terminal/nearAI/VirtualProtocolEcosystemPrompts';
import AIAgentConstant from '../../lib/constant/AIAgentConstant';
import Utils from '../../lib/Utils';
import { LokyTerminalHelper } from '../../lib/helper/LokyTerminalHelper';

export class GetMultiChainResponse extends LokyTerminalBase {
    private tokenExtendedDetails: any;

    protected async handleTokenDataProcessing(tokens: string[]): Promise<void> {
        const oThis = this;
        Logger.info(`GetMultiChainResponse::handleTokenDataProcessing::Processing base ecosystem tokens`);

        let tokenDetailsList: any[] = [];
        let chartTokenDetailsList: any[] = [];
        let chartData: { [key: string]: any } = {};
        let invalidTokens: string[] = [];

        try {
            await oThis.getTokenExtendedDetailsFromDLChart(tokens);
            await oThis.processQuestionTokenData(tokens, chartData, tokenDetailsList, chartTokenDetailsList, invalidTokens);

            if (invalidTokens.length === tokens.length) {
                Logger.info(`GetMultiChainResponse::handleTokenDataProcessing::No valid tokens found, generating Loky Response...`);
                await oThis.handleResponseIfTokenDataNotAvailable(invalidTokens);
                return;
            }

            const tokensForPrompt: any[] = tokenDetailsList.map((token) => oThis.formatDataForPrompt(token));
            const userContext = oThis.source === AIAgentConstant.questionSource.API ? undefined : await oThis.getUserContext(); // Do not fetch user context for API calls

            let aiModelResponse: string;

            try {
                const nearAPIPrompt = NearVirtualProtocolEcosystemPrompts.getUserPrompt(
                    oThis.question,
                    tokensForPrompt as VirtualEcosystemTokenDetails[],
                    userContext
                );
                aiModelResponse = await oThis.getAIResponseFromNearAI(nearAPIPrompt);
            } catch (error) {
                Logger.error(`GetMultiChainResponse::handleTokenDataProcessing::Error while fetching AI response from Near AI: ${JSON.stringify(error)}`);
                const openAISystemPrompt = VirtualProtocolEcosystemPrompts.getSystemPrompt;
                const openAIUserPrompt = VirtualProtocolEcosystemPrompts.getUserPrompt(
                    oThis.question,
                    tokensForPrompt as VirtualEcosystemTokenDetails[],
                    userContext
                );
                aiModelResponse = await oThis.getAIResponse(openAISystemPrompt, openAIUserPrompt);
            }

            const answerTickers = LokyTerminalHelper.extractTokenTickers(aiModelResponse);
            const additionalTokens = answerTickers.filter((ticker) => !tokens.includes(ticker));

            for (const token of additionalTokens) {
                await LokyTerminalHelper.processChartTokenData(token, chartData, chartTokenDetailsList, oThis.question, oThis.category);
            }
            await oThis.saveQuestionAndAnswer(oThis.question, aiModelResponse, tokens, answerTickers, oThis.chartMetric);
            oThis.setLokyResponseData(aiModelResponse, chartData, chartTokenDetailsList, oThis.chartMetric);
        } catch (error) {
            oThis.handleProcessingError(error);
        }
    }

    protected async getTokenExtendedDetails(tokenDetail: any): Promise<any> {
        if (this.tokenExtendedDetails) {
            return this.tokenExtendedDetails?.[tokenDetail?.["token_id"]] || null;
        } else {
            const tokenAddresses = await this.tokenAddressMapModel.getByTokenId(tokenDetail.token_id);
            return tokenAddresses.dataValues || null;
        }
    }

    private async getTokenExtendedDetailsFromDLChart(tokens: string[]): Promise<void> {
        const oThis = this;
        Logger.info(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Fetching extended details for tokens: ${JSON.stringify(tokens)}`);
        let validTokenDetails: any[] = [];
        let tokenIdsList;
        try {
            validTokenDetails = await Promise.all(
                tokens.map(token => LokyTerminalHelper.getTokenDetailByTickerAndCategory(token, oThis.category))
            );

            tokenIdsList = validTokenDetails
                .filter(detail => detail !== null)
                .map(detail => detail.token_id);

            Logger.debug(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Fetched token IDs for tokens: ${JSON.stringify(tokens)} | Token IDs: ${JSON.stringify(tokenIdsList)}`);
        } catch (error) {
            Logger.error(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Error while fetching token IDs for tokens: ${JSON.stringify(tokens)} | ${error}`);
            throw error;
        }

        const tokenCategory = validTokenDetails[0]?.category;
        Logger.info(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Token category: ${tokenCategory}`);
        if (tokenCategory === AIAgentConstant.categorySolanaEcosystem || tokenCategory === AIAgentConstant.categorySolanaMeme) {
            Logger.info(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Token category is solana or solana meme, skipping token extended details fetch`);
            oThis.tokenExtendedDetails = null;
            return;
        }

        const tokensString = Utils.convertListToCommaSeparatedString(tokenIdsList);
        try {
            const tokenEndpoint = AIAgentConstant.DLTokenExtendedDetailsChart;

            const res = await fetch(
                `${tokenEndpoint}?api_key=${AIAgentConstant.aiUserAPIKey}&output_format=json&filterParams={"token_id":"${tokensString}"}`
            );

            if (!res.ok) {
                Logger.error(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Failed to fetch data from DL chart API | Status: ${res.status}, StatusText: ${res.statusText}`);
                throw new Error(`Failed to fetch token extended details data: ${res.status} ${res.statusText}`);
            }

            const data: any = await res.json();

            if (data.length) {
                const result = data.reduce((acc: any, item: any) => {
                    acc[item.token_id] = item;
                    return acc;
                }, {});
                Logger.info(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Token extended details successfully fetched and processed. Total tokens: ${Object.keys(result).length}`);
                Logger.debug(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Processed token extended details: ${JSON.stringify(result)}`);
                oThis.tokenExtendedDetails = result;
            } else {
                Logger.info(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart:No data found for tokens: ${tokensString}`);
            }
        } catch (error) {
            Logger.error(`GetMultiChainResponse::getTokenExtendedDetailsFromDLChart::Error while fetching or processing token extended details: ${JSON.stringify(error)}`);
            throw error;
        }
    }

    protected async handleResponseIfTokenDataNotAvailable(tokenTicker: string[]) {
        const oThis = this;

        const userContext = oThis.source === AIAgentConstant.questionSource.API ? undefined : await oThis.getUserContext(); // Do not fetch user context for API calls
        Logger.debug(`GetMultiChainResponse::handleResponseIfTokenDataNotAvailable::User Context: ${JSON.stringify(userContext)}`);

        let aiModelResponse: string;

        try {
            const nearAPIPrompt = NearVirtualProtocolEcosystemPrompts.getNoTokenDataPrompt(
                oThis.question,
                tokenTicker,
                userContext
            );
            aiModelResponse = await oThis.getAIResponseFromNearAI(nearAPIPrompt);
        } catch (error) {
            Logger.error(`GetMultiChainResponse::handleResponseIfTokenDataNotAvailable::Error while fetching AI response from Near AI: ${JSON.stringify(error)}`);
            const openAISystemPrompt = VirtualProtocolEcosystemPrompts.getSystemPrompt;
            const openAIUserPrompt = VirtualProtocolEcosystemPrompts.getNoTokenDataPrompt(oThis.question, tokenTicker, userContext);
            aiModelResponse = await oThis.getAIResponse(openAISystemPrompt, openAIUserPrompt);
        }

        await oThis.saveQuestionAndAnswer(oThis.question, aiModelResponse, [], [], oThis.chartMetric);

        oThis.setLokyResponseData(aiModelResponse, {}, [], oThis.chartMetric);
    }

    protected formatTokenDetails(tokenData: any, tokenExtendedDetails: any): VirtualEcosystemTokenDetails {
        return {
            token_category: tokenData?.category,
            token_ticker: tokenData?.token?.toLowerCase(),
            token_name: tokenData?.token_name,
            token_id: tokenData?.token_id,
            current_usd_price: tokenData?.usd_price,
            last_24_hrs_price_change_percentage: tokenData?.price_change_percentage_24h,
            market_cap: tokenData?.mcap,
            last_24_hrs_market_cap_change_percentage: tokenData?.mcap_change_percentage_24h,
            last_24_hrs_volume: tokenData?.volume_24h,
            last_7_day_price_change_percentage: tokenData?.price_change_percentage_7d,
            last_7_day_volume_change_percentage: tokenData?.volume_change_percentage_7d,
            last_7_day_market_cap_change_percentage: tokenData?.mcap_change_percentage_7d,
            last_30_day_price_change_percentage: tokenData?.mcap_change_percentage_30d,
            last_30_day_volume_change_percentage: tokenData?.volume_change_percentage_30d,
            last_30_day_market_cap_change_percentage: tokenData?.mcap_change_percentage_30d,
            last_24_hrs_price_high: tokenData?.price_high_24h,
            price_ath: tokenData?.price_ath,
            circulating_supply: tokenData?.circulating_supply,
            total_supply: tokenData?.total_supply,
            launch_date: tokenExtendedDetails?.token_launch_date,
            contract_address: tokenExtendedDetails?.token_address,
            token_twitter_handle: AIAgentConstant.generateXHandleLink(tokenData?.handle),
            token_description: Utils.sliceString(tokenExtendedDetails?.description, 200),
            support: tokenData?.support,
            resistance: tokenData?.resistance,
            rsi: tokenData?.rsi,
            'sma_50h': tokenData?.sma,
            total_liquidity: tokenData?.total_liquidity,
            total_holder_count: tokenData?.total_holder_count,
            holder_count_change_percentage_24h: tokenData?.holder_count_change_percentage_24h,
            dev_wallet: tokenData?.dev_wallet,
            dev_wallet_balance: tokenData?.dev_wallet_balance,
            dev_wallet_total_holding_percentage: tokenData?.dev_wallet_total_holding_percentage,
            dev_wallet_outflow_txs_count_24h: tokenData?.dev_wallet_outflow_txs_count_24h,
            dev_wallet_outflow_amount_24h: tokenData?.dev_wallet_outflow_amount_24h,
            dev_wallet_inflow_amount_24h: tokenData?.dev_wallet_inflow_amount_24h,
            top_25_holder_buy_24h: tokenData?.top_25_holder_buy_24h,
            top_25_holder_sold_24h: tokenData?.top_25_holder_sold_24h,
            fifty_percentage_holding_wallet_count: tokenData?.fifty_percentage_holding_wallet_count,
            virtuals_link: tokenExtendedDetails?.virtual_id ? AIAgentConstant.virtualsLink(tokenExtendedDetails?.virtual_id) : null
        };
    }

    protected formatDataForPrompt(tokenData: any): VirtualEcosystemTokenDetails {
        const oThis = this;
        return {
            token_category: oThis.getTokenCategory(tokenData?.token_category),
            token_ticker: tokenData?.token_ticker || '[NULL]',
            token_name: tokenData?.token_name || '[NULL]',
            token_id: tokenData?.token_id || '[NULL]',
            current_usd_price: oThis.formatNumber(tokenData?.current_usd_price, '$'),
            last_24_hrs_price_change_percentage: oThis.formatNumber(tokenData?.last_24_hrs_price_change_percentage, '', '%'),
            market_cap: oThis.formatNumber(tokenData?.market_cap, '$'),
            last_24_hrs_market_cap_change_percentage: oThis.formatNumber(tokenData?.last_24_hrs_market_cap_change_percentage, '', '%'),
            last_24_hrs_volume: oThis.formatNumber(tokenData?.last_24_hrs_volume, '$'),
            last_7_day_price_change_percentage: oThis.formatNumber(tokenData?.last_7_day_price_change_percentage, '', '%'),
            last_7_day_volume_change_percentage: oThis.formatNumber(tokenData?.last_7_day_volume_change_percentage, '', '%'),
            last_7_day_market_cap_change_percentage: oThis.formatNumber(tokenData?.last_7_day_market_cap_change_percentage, '', '%'),
            last_30_day_price_change_percentage: oThis.formatNumber(tokenData?.last_30_day_price_change_percentage, '', '%'),
            last_30_day_volume_change_percentage: oThis.formatNumber(tokenData?.last_30_day_volume_change_percentage, '', '%'),
            last_30_day_market_cap_change_percentage: oThis.formatNumber(tokenData?.last_30_day_market_cap_change_percentage, '', '%'),
            last_24_hrs_price_high: oThis.formatNumber(tokenData?.last_24_hrs_price_high, '$'),
            price_ath: oThis.formatNumber(tokenData?.price_ath, '$'),
            circulating_supply: oThis.formatNumber(tokenData?.circulating_supply),
            total_supply: oThis.formatNumber(tokenData?.total_supply),
            launch_date: tokenData?.launch_date || '[NULL]',
            contract_address: tokenData?.contract_address || '[NULL]',
            token_twitter_handle: tokenData?.token_twitter_handle || '[NULL]',
            token_description: tokenData?.token_description ? Utils.sliceString(tokenData?.token_description, 200) : '[NULL]',
            support: oThis.formatNumber(tokenData?.support, '$'),
            resistance: oThis.formatNumber(tokenData?.resistance, '$'),
            rsi: oThis.formatNumber(tokenData?.rsi),
            "sma_50h": oThis.formatNumber(tokenData?.["sma_50h"], '$'),
            total_liquidity: oThis.formatNumber(tokenData?.total_liquidity, '$'),
            total_holder_count: oThis.formatNumber(tokenData?.total_holder_count),
            holder_count_change_percentage_24h: tokenData?.holder_count_change_percentage_24h ? oThis.formatNumber(tokenData?.holder_count_change_percentage_24h, '', '%') : '0%',
            dev_wallet: tokenData?.dev_wallet || '[NULL]',
            dev_wallet_total_holding_percentage: tokenData?.dev_wallet_total_holding_percentage ? oThis.formatNumber(tokenData?.dev_wallet_total_holding_percentage, '', '%') : '0%',
            dev_wallet_outflow_txs_count_24h: tokenData?.dev_wallet_outflow_txs_count_24h || '0',
            dev_wallet_outflow_amount_24h: tokenData?.dev_wallet_outflow_amount_24h ? oThis.formatNumber(tokenData?.dev_wallet_outflow_amount_24h, '', '', tokenData?.token_ticker) : '0',
            dev_wallet_inflow_amount_24h: tokenData?.dev_wallet_inflow_amount_24h ? oThis.formatNumber(tokenData?.dev_wallet_inflow_amount_24h, '', '', tokenData?.token_ticker) : '0',
            top_25_holder_buy_24h: oThis.formatNumber(tokenData?.top_25_holder_buy_24h, '', '', tokenData?.token_ticker),
            top_25_holder_sold_24h: oThis.formatNumber(tokenData?.top_25_holder_sold_24h, '', '', tokenData?.token_ticker),
            fifty_percentage_holding_wallet_count: oThis.formatNumber(tokenData?.fifty_percentage_holding_wallet_count),
            virtuals_link: tokenData?.virtuals_link ? tokenData?.virtuals_link : '[NULL]',
            dev_wallet_balance: tokenData?.dev_wallet_balance ? oThis.formatNumber(tokenData?.dev_wallet_balance, '', '', tokenData?.token_ticker) : '0'
        };
    }

    private getTokenCategory(category: string): string {
        const categoryMap: Record<string, string> = {
            [AIAgentConstant.categoryBaseEcosystem]: 'base ecosystem',
            [AIAgentConstant.categoryVirtualEcosystem]: 'sentient',
            [AIAgentConstant.categoryVirtualEcosystemPrototype]: 'prototype',
            [AIAgentConstant.categoryVirtualEcosystemGenesis]: 'genesis',
            [AIAgentConstant.categorySolanaEcosystem]: 'solana',
            [AIAgentConstant.categorySolanaMeme]: 'solana'
        }
        return categoryMap[category];
    }

    protected handleProcessingError(error: any): never {
        if (error instanceof Error) {
            Logger.error(`GetMultiChainResponse::handleProcessingError::Error | Message: ${error.message} | Stack: ${error.stack}`);
        } else {
            Logger.error(`GetMultiChainResponse::handleProcessingError::Error | Unknown error: ${JSON.stringify(error)}`);
        }

        throw ResponseHelper.error(["generalError"], {
            error: "GetMultiChainResponse::handleProcessingError::Failed to handle token-based flow.",
        });
    }
}
