import UserWatchlistByUserIds from '../lib/cache/UserWatchlistByUserIds';
import UserWatchlistGenesisByUserIds from '../lib/cache/UserWatchlistGenesisByUserIds';
import ResponseHelper from '../lib/helper/ResponseHelper';
import Logger from '../lib/Logger';
import Postgres from '../lib/Postgres';
import { ErrorResponse, SuccessResponse, UserModelAttributes } from '../lib/Types';
import ServiceBase from './Base';

export default class RemoveTokenFromWatchlist extends ServiceBase {

    private currentUser: UserModelAttributes;

    private tokenId: string;

    private genesisId: number;

    constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.currentUser = params.internalDecodedParams.currentUser! || {};
        oThis.tokenId = params.tokenId || null;
        oThis.genesisId = params.genesisId || null;
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;

        await oThis.validate();

        if (oThis.tokenId) {
            await oThis.removeTokenFromWatchlist();
        }

        if (oThis.genesisId) {
            await oThis.removeGenesisFromWatchlist();
        }

        await oThis.flushCache();

        return oThis.prepareResponse();
    }

    private async validate() {
        const oThis = this;
        Logger.info(`RemoveTokenFromWatchlist::validate::Validating API params...`);
        if (!oThis.tokenId && !oThis.genesisId) {
            Logger.error(`RemoveTokenFromWatchlist::validate::tokenId or genesisId is required.`);
            return oThis.unauthorizedResponse('invalidParams', 's_s_rtfw_v_1');
        }
    }

    private async removeTokenFromWatchlist(): Promise<void> {
        const oThis = this;
        Logger.info(`RemoveTokenFromWatchlist::removeFromWatchlist::Removing token '${oThis.tokenId}' record from watchlist for user '${oThis.currentUser.id!}'`);
        const userWatchlistModel = (await Postgres.getDbModels()).userWatchlist;
        const userWatchGenesisWatchlistModel = (await Postgres.getDbModels()).userWatchlistGenesis;
        try {
            const result = await userWatchlistModel.deleteByUserAndTokenId(
                oThis.currentUser.id!,
                oThis.tokenId,
            );

            const genesisResult = await userWatchGenesisWatchlistModel.deleteByUserAndTokenId(oThis.currentUser.id!, oThis.tokenId);
            Logger.debug(`RemoveTokenFromWatchlist::removeFromWatchlist::result: ${JSON.stringify(result)}`);
            
            if (!result) {
                Logger.error(`RemoveTokenFromWatchlist::removeFromWatchlist::Token not exist in current user's watchlist.`);
            }

            if (!genesisResult) {
                Logger.error(`RemoveTokenFromWatchlist::removeFromWatchlist::Token not exist in current user's genesis watchlist.`);
            }
        } catch (err: any) {
            Logger.error(`RemoveTokenFromWatchlist::removeFromWatchlist::Error occurred while deleting token record in users_watchlist. userId: ${oThis.currentUser.id} | tokenId: ${oThis.tokenId}, error: ${JSON.stringify(err.message)}`);
            throw err;
        }
    }

    private async removeGenesisFromWatchlist(): Promise<void> {
        const oThis = this;
        Logger.info(`RemoveTokenFromWatchlist::removeGenesisFromWatchlist::Removing genesisId '${oThis.genesisId}' record from watchlist for user '${oThis.currentUser.id!}'`);
        const userWatchGenesisWatchlistModel = (await Postgres.getDbModels()).userWatchlistGenesis;
        try {
            const result = await userWatchGenesisWatchlistModel.deleteByUserAndGenesisId(oThis.currentUser.id!, oThis.genesisId);
            Logger.debug(`RemoveTokenFromWatchlist::removeGenesisFromWatchlist::result: ${JSON.stringify(result)}`);
            if (!result) {
                return oThis.unauthorizedResponse(
                    'somethingWentWrong',
                    's_s_rtfw_rfw_1',
                    {
                        message: `Token not exist in current user's watchlist Genesis.`,
                        genesisId: oThis.genesisId,
                    }
                );
            }
        } catch (err: any) {
            Logger.error(`RemoveTokenFromWatchlist::removeGenesisFromWatchlist::Error occurred while deleting genesis record in users_watchlist. userId: ${oThis.currentUser.id} | genesisId: ${oThis.genesisId}, error: ${JSON.stringify(err)}`);
            throw err;
        }
    }

    private async flushCache() {
        const oThis = this;
        Logger.info(`RemoveTokenFromWatchlist::flushCache::Flushing cache for user '${oThis.currentUser.id!}'`);
        await (new UserWatchlistByUserIds({ userIds: [oThis.currentUser.id!] })).clear();
        await (new UserWatchlistGenesisByUserIds({ userIds: [oThis.currentUser.id!] })).clear();
    }

    private prepareResponse(): SuccessResponse {
        const oThis = this;
        Logger.info(`RemoveTokenFromWatchlist::prepareResponse::Service execution completed`);
        return ResponseHelper.success({
            message: "Removed from watchlist.",
            tokenId: oThis.tokenId
        });
    }
}
