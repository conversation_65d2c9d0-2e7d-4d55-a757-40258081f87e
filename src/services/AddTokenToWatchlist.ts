import ResponseHelper from '../lib/helper/ResponseHelper';
import Logger from '../lib/Logger';
import Postgres from '../lib/Postgres';
import { ErrorResponse, SuccessResponse, UserModelAttributes } from '../lib/Types';
import ServiceBase from './Base';
import Basic from '../lib/helper/Basic';
import UserWatchlistByUserIds from '../lib/cache/UserWatchlistByUserIds';
import UserWatchlistGenesisByUserIds from '../lib/cache/UserWatchlistGenesisByUserIds';

export default class AddTokenToWatchlist extends ServiceBase {

    private currentUser: UserModelAttributes;

    private tokenIds: string[];

    private genesisIds: number[];

    constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.currentUser = params.internalDecodedParams.currentUser! || {};
        oThis.tokenIds = params?.tokenIds ? Basic.commaSeparatedStrToArray(params.tokenIds) : [];
        oThis.genesisIds = params?.genesisIds ? Basic.commaSeparatedStrToNumberArray(params.genesisIds) : [];
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;

        await oThis.validate();

        if (oThis.tokenIds?.length) {
            await oThis.addIntoWatchlist();
        }

        if (oThis.genesisIds?.length) {
            await oThis.addIntoGenesisWatchlist();
        }

        await oThis.flushCache();

        return oThis.prepareResponse();
    }

    private async validate() {
        const oThis = this;
        Logger.info(`AddTokenToWatchlist::validate::Validating API params...`);
        if (oThis.tokenIds?.length < 1 && oThis.genesisIds?.length < 1) {
            Logger.error(`AddTokenToWatchlist::validate::tokenId or genesisId is required.`);
            return oThis.unauthorizedResponse('invalidParams', 's_s_attw_v_1');
        }
    }

    private async addIntoWatchlist(): Promise<void> {
        const oThis = this;
        Logger.info(`AddTokenToWatchlist::addIntoWatchlist::Adding tokens with tokenIds:'${oThis.tokenIds}' and genesisIds:'${oThis.genesisIds}' record to watchlist for user '${oThis.currentUser.id!}'`);
        const userWatchlistModel = (await Postgres.getDbModels()).userWatchlist;
        try {
            const userWatchlistedTokensCache = await new UserWatchlistByUserIds({
                userIds: [oThis.currentUser.id!]
            }).fetch();

            const userWatchlistedTokens: any[] = userWatchlistedTokensCache.data[oThis.currentUser.id!];

            let userWatchlistedTokenIds: string[] = [];
            if (!Basic.isEmptyObject(userWatchlistedTokens)) {
                userWatchlistedTokenIds = userWatchlistedTokens.map((token: any) => token.tokenId);
            }
            const userWatchlist = [];
            for (let i = 0; i < oThis.tokenIds.length; i++) {
                const tokenId = oThis.tokenIds[i];
                if (userWatchlistedTokenIds.includes(tokenId)) {
                    Logger.warn(`AddTokenToWatchlist::addIntoWatchlist::Token '${tokenId}' already exist in current user's watchlist.`);
                    continue;
                }
                userWatchlist.push({
                    userId: oThis.currentUser.id!,
                    tokenId: tokenId,
                });
            }
            const result = await userWatchlistModel.bulkCreate(userWatchlist);
            Logger.debug(`AddTokenToWatchlist::addIntoWatchlist::result: ${JSON.stringify(result)}`);
        } catch (err: any) {
            Logger.error(`AddTokenToWatchlist::addIntoWatchlist::Error occurred while creating token record in users_watchlist. userId: ${oThis.currentUser.id} | tokenIds: ${oThis.tokenIds}, error message: ${JSON.stringify(err.message)}`);
            throw err;
        }
    }

    private async addIntoGenesisWatchlist(): Promise<void> {
        const oThis = this;
        Logger.info(`AddTokenToWatchlist::addIntoGenesisWatchlist::Adding genesisIds:'${oThis.genesisIds}' record to watchlist for user '${oThis.currentUser.id!}'`);
        const userWatchGenesisWatchlistModel = (await Postgres.getDbModels()).userWatchlistGenesis;
        const genesisLQADataModel = (await Postgres.getDbModels()).genesisLqaData;
        try {
            const userWatchlistGenesisByUserIdsCache = await new UserWatchlistGenesisByUserIds({
                userIds: [oThis.currentUser.id!]
            }).fetch();

            const userWatchListedGenesis: any[] = userWatchlistGenesisByUserIdsCache.data[oThis.currentUser.id!];

            let watchlistedGenesisIds: number[] = [];
            if (!Basic.isEmptyObject(userWatchListedGenesis)) {
                watchlistedGenesisIds = userWatchListedGenesis.map((genesis: any) => genesis.genesisId);
            }

            const genesisLQAData = await genesisLQADataModel.getByGenesisIds(oThis.genesisIds);
            const genesisIdTokenIdMap = new Map(genesisLQAData.map((genesis: any) => [genesis.genesisId, genesis.tokenId]));

            const userWatchlistGenesis = [];
            for (let i = 0; i < oThis.genesisIds.length; i++) {
                const genesisId = oThis.genesisIds[i];
                if (watchlistedGenesisIds.includes(genesisId)) {
                    Logger.warn(`AddTokenToWatchlist::addIntoGenesisWatchlist::Genesis '${genesisId}' already exist in current user's watchlist.`);
                    continue;
                }
                const tokenId = genesisIdTokenIdMap.get(genesisId) || null;
                userWatchlistGenesis.push({
                    userId: oThis.currentUser.id!,
                    genesisId: genesisId,
                    tokenId: tokenId,
                });
            }
            const result = await userWatchGenesisWatchlistModel.bulkCreate(userWatchlistGenesis);
            Logger.debug(`AddTokenToWatchlist::addIntoGenesisWatchlist::result: ${JSON.stringify(result)}`);
        } catch (err: any) {
            Logger.error(`AddTokenToWatchlist::addIntoGenesisWatchlist::Error occurred while creating token record in users_watchlist. userId: ${oThis.currentUser.id} | tokenIds: ${oThis.tokenIds}, error message: ${JSON.stringify(err.message)}`);
            throw err;
        }
    }

    private async flushCache() {
        const oThis = this;
        Logger.info(`AddTokenToWatchlist::flushCache::Flushing cache for user '${oThis.currentUser.id!}'`);
        await (new UserWatchlistByUserIds({ userIds: [oThis.currentUser.id!] })).clear();
        await (new UserWatchlistGenesisByUserIds({ userIds: [oThis.currentUser.id!] })).clear();
    }

    private prepareResponse(): SuccessResponse {
        const oThis = this;
        Logger.info(`AddTokenToWatchlist::prepareResponse::Service execution completed`);
        return ResponseHelper.success({
            message: "Added to watchlist.",
            tokenIds: oThis.tokenIds
        });
    }
}
