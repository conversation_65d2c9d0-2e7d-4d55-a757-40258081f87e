import moment from 'moment';
import ServiceBase from '../Base';
import ResponseHelper from '../../lib/helper/ResponseHelper';
import LocalCipher from '../../lib/encryptors/LocalCipher';
import Logger from '../../lib/Logger';
import GeneralValidator from '../../lib/validator/GeneralValidator';
import Postgres from '../../lib/Postgres';
import AIAgentConstant from '../../lib/constant/AIAgentConstant';
import APIName from '../../lib/constant/APIName';
import { APILogsModelAttributes, WalletSafetyScoreModelAttributes } from '../../lib/Types';
import WalletSafetyScoreByGenesisAddresses from '../../lib/cache/WalletSafetyScoreByGenesisAddresses';
import Basic from '../../lib/helper/Basic';
import GenesisLQADataByGenesisIds from '../../lib/cache/GenesisLQADataByGenesisIds';

export default class GetWalletSafetyScore extends ServiceBase {

    private userId: number;

    private apiKeyId: number;

    private userAPIKey: string;

    private genesisContractAddress: string;

    private hostName: string;

    private pendingRequestId: string;

    private formattedResponse: {
        genesisContractAddress: string,
        memberWallets: { address: string, safetyScore: number }[]
    }

    constructor(params: { api_key: string, genesis_contract_address: string, hostName: string }) {
        super(params);
        const oThis = this;
        oThis.genesisContractAddress = params?.genesis_contract_address?.toLowerCase();
        oThis.userAPIKey = params?.api_key;
        oThis.hostName = params.hostName;
    }

    public async servicePerform(): Promise<any> {
        const oThis = this;

        await oThis.validateParams();

        await oThis.validateAPIKey();

        await oThis.addPendingAPICall();

        await oThis.getWalletSafetyScore();

        return await oThis.prepareResponse();
    }

    private async validateParams() {
        const oThis = this;
        if (!oThis.genesisContractAddress) {
            return oThis.badResponse('invalidParams', 's_s_a_gws_vp_0');
        }
    }

    private async validateAPIKey() {
        const oThis = this;
        if (!GeneralValidator.validateNonEmptyString(oThis.userAPIKey)) {
            return oThis.unauthorizedResponse('invalidAPIKey', 's_s_a_gws_vak_1');
        }
        Logger.info(`GetWalletSafetyScore::validateAPIKey::Getting key ${oThis.userAPIKey}`)
        const hashedKey: string = LocalCipher.encrypt(process.env.USER_ENCRYPTION_KEY_API_KEY!, oThis.userAPIKey);
        const userApiKeyModel = (await Postgres.getDbModels()).userAPIKey;
        const userAPIKeyObject = await userApiKeyModel.getByKey(hashedKey);
        Logger.info(`GetWalletSafetyScore::validateAPIKey::userAPIKeyObject ${JSON.stringify(userAPIKeyObject)}`)
        if (!GeneralValidator.validateNonEmptyObject(userAPIKeyObject)) {
            return oThis.unauthorizedResponse('invalidAPIKey', 's_s_a_gws_vak_2');
        }
        oThis.apiKeyId = userAPIKeyObject.id!;
        oThis.userId = userAPIKeyObject.userId;
    }

    private async addPendingAPICall() {
        const oThis = this;
        const apiLogModel = (await Postgres.getDbModels()).apiLog;
        const formattedAPILogDetail: APILogsModelAttributes = {
            userId: oThis.userId,
            apiKeyId: oThis.apiKeyId,
            apiType: AIAgentConstant.apiType[APIName.walletSafetyScore],
            hostname: oThis.hostName,
            status: AIAgentConstant.apiResponseStatusCode[AIAgentConstant.APIStatusPending]
        }
        let successResponse = await apiLogModel.create(formattedAPILogDetail);
        oThis.pendingRequestId = successResponse.id;
        Logger.info(`GetWalletSafetyScore::addPendingAPICall::Creating Record in API Logs Table ${oThis.pendingRequestId}`);
    }

    private async getWalletSafetyScore() {
        const oThis = this;
        const walletSafetyScoreByGenesisAddressCache = await (new WalletSafetyScoreByGenesisAddresses({
            genesisContractAddresses: [oThis.genesisContractAddress]
        })).fetch();

        const walletSafetyScores: WalletSafetyScoreModelAttributes[] = walletSafetyScoreByGenesisAddressCache.data[oThis.genesisContractAddress];
        let memberWallets: { address: string, safetyScore: number }[] = [];
        let genesisId: number = 0;
        if (!Basic.isEmptyObject(walletSafetyScores)) {
            genesisId = walletSafetyScores[0].genesisId;
            memberWallets = walletSafetyScores.map((wallet: WalletSafetyScoreModelAttributes) => ({
                address: wallet.memberWalletAddress,
                title: wallet.walletTitle,
                reasoning: wallet.reasoning,
                safetyScore: wallet.safetyScore,
            }));
        }

        let genesisMeta: any = {};
        if (genesisId) {
            const genesisLqaDataByGenesisIds = await (new GenesisLQADataByGenesisIds({
                genesisIds: [genesisId],
            })).fetch();
            Logger.info(`GetGenesisLQAData::fetchGenesisTokens::genesisLqaDataByGenesisIds::${JSON.stringify(genesisLqaDataByGenesisIds.data)}`);
            const genesis = genesisLqaDataByGenesisIds.data[genesisId.toString()];
            if (genesis) {
                genesisMeta = {
                    genesisId: genesis.genesisId,
                    name: genesis.name,
                    ticker: genesis.token,
                    network: AIAgentConstant.networkMap[genesis.chain]?.toLowerCase(),
                    isVerified: genesis.isVerified ?? false,
                };
            }
        }
        oThis.formattedResponse = {
            genesisContractAddress: oThis.genesisContractAddress,
            ...genesisMeta,
            memberWallets: memberWallets,
        }
    }

    private async prepareResponse() {
        const oThis = this;
        await ResponseHelper.updateUsersAPIUsageStats(oThis.userId, oThis.apiKeyId, moment().utc().startOf('day').toDate());
        return ResponseHelper.success(oThis.formattedResponse);
    }
}
