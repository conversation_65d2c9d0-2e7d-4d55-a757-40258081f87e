import moment from "moment";
import ResponseHelper from "../../lib/helper/ResponseHelper";
import ServiceBase from "../Base";
import LocalCipher from "../../lib/encryptors/LocalCipher";
import Logger from "../../lib/Logger";
import GeneralValidator from "../../lib/validator/GeneralValidator";
import Postgres from "../../lib/Postgres";
import AIAgentConstant from "../../lib/constant/AIAgentConstant";
import { GetSolanaEcosystemResponse } from "../terminal/GetSolanaEcosystemResponse";
import { GetBaseEcosystemResponse } from "../terminal/GetBaseEcosystemResponse";
import { SuccessResponse } from "../../lib/Types";
import { APILogsModelAttributes } from '../../lib/Types';
import APIName from '../../lib/constant/APIName';
import { GetMultiChainResponse } from "../terminal/GetMultiChainResponse";
import GetKosherChatResponse from "./kosherCapital/GetKosherChatResponse";

export default class GetAskLokyResponse extends ServiceBase {

    private question: string;

    private chain: string;

    private client: string;

    private fundId: string;

    private clientUserId: number;

    private userId: number;

    private userAPIKey: string;

    private apiKeyId: number;

    private result: any;

    private responseHandler: GetBaseEcosystemResponse | GetSolanaEcosystemResponse | GetMultiChainResponse | GetKosherChatResponse | null;

    private parameters: any;

    private pendingRequestId: number;

    private hostName: string;

    private requestId: string;

    constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.question = params.question;
        oThis.chain = params.chain;
        oThis.client = params.client;
        oThis.fundId = params.fund_id;
        oThis.clientUserId = params.client_user_id;
        oThis.userAPIKey = params.api_key;
        oThis.result = [];
        oThis.hostName = params.hostName;
        oThis.requestId = params.requestId || 'unknown';
        oThis.parameters = params;
    }

    public async servicePerform(): Promise<any> {
        const oThis = this;
        await oThis.validateAPIKey();
        await oThis.validateParams();
        oThis.responseHandler = oThis.getResponseHandler(oThis.parameters);
        if (!oThis.responseHandler) {
            return oThis.badResponse('invalidEcosystem', 's_s_a_galr_sp_1');
        }
        await oThis.addPendingAPICall();
        try {
            const result = await oThis.responseHandler.servicePerform();
            if (!result.success) {
                return result;
            }
            oThis.result = oThis.formatResponse((result as SuccessResponse).data);
        } catch (error: any) {
            Logger.error(`GetAskLokyResponse::Error fetching agent AI response | Error: ${JSON.stringify(error.message)}`);
            throw ResponseHelper.error(['generalError'], {
                error: 'GetAskLokyResponse::Failed to fetch AI response.',
                apiLogId: oThis.pendingRequestId
            });
        }
        return await oThis.prepareResponse();
    }

    private async validateAPIKey() {
        const oThis = this;
        if (!GeneralValidator.validateNonEmptyString(oThis.userAPIKey)) {
            return oThis.unauthorizedResponse('invalidAPIKey', 's_s_a_galr_vak_1');
        }
        Logger.info(`GetTokens::validateAPIKey::Getting key ${oThis.userAPIKey}`)
        const hashedKey: string = LocalCipher.encrypt(process.env.USER_ENCRYPTION_KEY_API_KEY!, oThis.userAPIKey);
        const userApiKeyModel = (await Postgres.getDbModels()).userAPIKey;
        const userAPIKeyObject = await userApiKeyModel.getByKey(hashedKey);
        Logger.info(`GetTokens::validateAPIKey::userAPIKeyObject ${JSON.stringify(userAPIKeyObject)}`)

        if (!GeneralValidator.validateNonEmptyObject(userAPIKeyObject)) {
            return oThis.unauthorizedResponse('invalidAPIKey', 's_s_a_galr_vak_2');
        }

        oThis.apiKeyId = userAPIKeyObject.id!;
        oThis.userId = userAPIKeyObject.userId;
    }

    private async validateParams() {
        const oThis = this;

        const supportedChains = AIAgentConstant.supportedChainsForLokyAPIs;
        if (oThis.chain && !supportedChains.includes(oThis.chain?.toUpperCase())) {
            return oThis.badResponse('invalidChain', 's_s_a_galr_vp_0');
        }

        if (!GeneralValidator.validateNonEmptyString(oThis.question)) {
            return oThis.badResponse('invalidQuestion', 's_s_a_galr_vp_1');
        }

        if (oThis.client) {
            const supportedClients = AIAgentConstant.supportedClients;
            if (!supportedClients.includes(oThis.client?.toLowerCase())) {
                return oThis.badResponse('invalidClient', 's_s_a_galr_vp_2');
            }

            // If client is present, fundId is required
            if (!GeneralValidator.validateNonEmptyString(oThis.fundId)) {
                return oThis.badResponse('invalidFundId', 's_s_a_galr_vp_3');
            }

            if (!oThis.clientUserId || !GeneralValidator.validateNumber(oThis.clientUserId)) {
                return oThis.badResponse('invalidClientUserId', 's_s_a_galr_vp_4');
            }
        }
    }

    private getResponseHandler(params: any): GetBaseEcosystemResponse | GetSolanaEcosystemResponse | GetMultiChainResponse | GetKosherChatResponse | null {
        const oThis = this;
        Logger.info(`GetAskLokyResponse::getResponseHandler::Creating response handler for client: ${params.client} and ecosystem: ${params.chain}`);

        // Check if this is a client-based request
        if (oThis.client) {
            return oThis.clientBasedResponseHandler(params);
        }

        // Otherwise, use network-based response handler
        return oThis.networkBasedResponseHandler(params);
    }

    private clientBasedResponseHandler(params: any): GetKosherChatResponse | null {
        const oThis = this;
        Logger.info(`GetAskLokyResponse::clientBasedResponseHandler::Creating client-based response handler for client: ${params.client}`);

        switch (oThis.client.toLowerCase()) {
            case AIAgentConstant.kosherClient:
                params.userId = oThis.userId;
                return new GetKosherChatResponse(params);
            default:
                Logger.error(`GetAskLokyResponse::clientBasedResponseHandler::Unsupported client: ${oThis.client}`);
                return null;
        }
    }

    private networkBasedResponseHandler(params: any): GetBaseEcosystemResponse | GetSolanaEcosystemResponse | GetMultiChainResponse | null {
        const oThis = this;
        Logger.info(`GetAskLokyResponse::networkBasedResponseHandler::Creating network-based response handler for ecosystem: ${params.chain}`);

        const network = oThis.chain ? oThis.chain?.toUpperCase() : AIAgentConstant.allNetworks;
        params.source = AIAgentConstant.questionSource.API;
        params.userId = oThis.userId;
        params.category = AIAgentConstant.networkCategoryMap[network];

        switch (network) {
            case AIAgentConstant.networkBase:
                return new GetBaseEcosystemResponse(params);
            case AIAgentConstant.networkSolana:
                return new GetSolanaEcosystemResponse(params);
            case AIAgentConstant.allNetworks:
                return new GetMultiChainResponse(params);
            default:
                Logger.error(`GetAskLokyResponse::networkBasedResponseHandler::Invalid chain provided: ${params.category}`);
                return null;
        }
    }

    private async addPendingAPICall() {
        const oThis = this;
        const apiLogModel = (await Postgres.getDbModels()).apiLog;
        const formattedAPILogDetail: APILogsModelAttributes = {
            userId: oThis.userId,
            apiKeyId: oThis.apiKeyId,
            apiType: AIAgentConstant.apiType[APIName.askLoky],
            hostname: oThis.hostName,
            status: AIAgentConstant.apiResponseStatusCode[AIAgentConstant.APIStatusPending]
        }
        let successResponse = await apiLogModel.create(formattedAPILogDetail);
        oThis.pendingRequestId = successResponse.id;
        Logger.info(`GetAskLokyResponse::addPendingAPICall::Creating Record in API Logs Table ${oThis.pendingRequestId}`);
    }

    private formatResponse(result: any) {
        const oThis = this;

        // Handle Kosher client response separately
        if (oThis.client && oThis.client.toLowerCase() === 'kosher') {
            return {
                questionId: result.questionId,
                answerId: result.answerId,
                nlpResponse: result.nlpResponse,
                token_tickers: result.token_tickers,
                token_addresses: result.token_addresses,
                network: result.network
            };
        }

        // Handle other responses (Solana, Base, Multi-chain)
        return {
            questionId: result.questionId,
            answerId: result.answerId,
            nlpResponse: result.text,
            tokenDetails: result.tokenDetails
        };
    }

    private async prepareResponse() {
        const oThis = this;
        await ResponseHelper.updateUsersAPIUsageStats(oThis.userId, oThis.apiKeyId, moment().utc().startOf('day').toDate());
        return ResponseHelper.success({
            result: oThis.result,
            apiLogId: oThis.pendingRequestId,
        });
    }
}
