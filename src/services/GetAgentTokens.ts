import ServiceBase from "./Base";
import ResponseHelper from "../lib/helper/ResponseHelper";
import { ErrorResponse, LQAHistoricalMetricModelAttributes, SuccessResponse, UserModelAttributes } from "../lib/Types";
import Logger from "../lib/Logger";
import Postgres from "../lib/Postgres";
import ExplorerConstant from "../lib/constant/ExplorerConstant";
import Utils from "../lib/Utils";
import VirtualTokenDetailsByTokenIds from "../lib/cache/VirtualTokenDetailsByTokenIds";
import AIAgentConstant from "../lib/constant/AIAgentConstant";
import UserWatchlistByUserIds from "../lib/cache/UserWatchlistByUserIds";
import UserWatchlistGenesisByUserIds from "../lib/cache/UserWatchlistGenesisByUserIds";
import Basic from "../lib/helper/Basic";

export default class GetAgentTokens extends ServiceBase {

    private currentUser: UserModelAttributes;

    private page: number;

    private sortBy: string;

    private sortOrder: string;

    private search: string;

    private agents: any[];

    private ecosystem: string;

    private pagination: { currentPage: number, totalPages: number, size: number, count: number };

    private subCategory: string;

    private watchlist: boolean;

    private watchlistedTokenIdsSet: Set<string>;

    private watchlistedGenesisTokenIdsSet: Set<string>;

    private network: string;

    private isTerminalAccess: boolean;

    constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.currentUser = params.internalDecodedParams.currentUser! || {};
        oThis.isTerminalAccess = params.internalDecodedParams.isTerminalAccess! || false;

        Logger.info(`GetAgentTokens::constructor::currentUser: ${JSON.stringify(oThis.currentUser)}`);
        oThis.currentUser = params.internalDecodedParams.currentUser! || {};
        oThis.page = Number(params.page) || ExplorerConstant.defaultPage;
        oThis.sortBy = params.sortBy?.toUpperCase() || 'MCAP';
        oThis.sortOrder = params.sortOrder?.toUpperCase() || ExplorerConstant.sortOrderDESC;
        oThis.search = params.search;
        oThis.agents = [];
        oThis.ecosystem = params.ecosystem;
        oThis.subCategory = params.subCategory;
        oThis.watchlist = params.watchlist === 'true';
        oThis.watchlistedTokenIdsSet = new Set();
        oThis.watchlistedGenesisTokenIdsSet = new Set();
        oThis.network = params.network;
    }

    public async servicePerform(): Promise<any | ErrorResponse> {
        const oThis = this;

        await oThis.validateParams();

        if (oThis.currentUser.id) {
            await oThis.fetchWatchlistedTokens();

            await oThis.fetchWatchlistedGenesisTokens();
        }

        await oThis.fetchAgentTokens();

        return oThis.prepareResponse();
    }

    private async validateParams() {
        const oThis = this;
        const supportedEcosystems = [AIAgentConstant.ecosystemVirtuals, AIAgentConstant.ecosystemSolana];

        // Set default ecosystem if none provided
        if (!oThis.ecosystem) {
            oThis.ecosystem = supportedEcosystems.join(',');
            return;
        }

        // Parse and validate ecosystems
        const ecosystems = oThis.ecosystem.split(',').map(eco => eco.trim());
        if (ecosystems.some(eco => !supportedEcosystems.includes(eco))) {
            return oThis.unauthorizedResponse('invalidEcosystem', 's_s_gat_vp_0');
        }

        // Skip subcategory validation if none provided
        if (!oThis.subCategory) return;

        // Validate subcategory
        const supportedSubCategories = [
            AIAgentConstant.subCategoryVirtualGenesis,
            AIAgentConstant.subCategoryVirtualSentient,
            AIAgentConstant.subCategoryVirtualPrototype,
            AIAgentConstant.subCategoryVirtualGenesis
        ];

        if (!supportedSubCategories.includes(oThis.subCategory)) {
            return oThis.unauthorizedResponse('invalidSubCategory', 's_s_gat_vp_1');
        }

        // Check if subcategory is compatible with selected ecosystems
        const isSolanaOnly = ecosystems.length === 1 && ecosystems[0] === AIAgentConstant.ecosystemSolana;
        if (isSolanaOnly) {
            return oThis.unauthorizedResponse('invalidSubCategory', 's_s_gat_vp_2');
        }

        // Validate watchlist param requires user to be logged in
        if (oThis.watchlist && !oThis.currentUser.id) {
            return oThis.unauthorizedResponse('userNotLoggedIn', 's_s_gat_vp_3');
        }

        if (!oThis.isTerminalAccess && oThis.page > 1) {
            return oThis.unauthorizedResponse('terminalAccessRequired', 's_s_gat_vp_4');
        }
    }

    private async fetchWatchlistedTokens() {
        const oThis = this;
        if (!oThis.currentUser.id) return;
        try {
            const userWatchlistByUserIds = await (new UserWatchlistByUserIds({
                userIds: [oThis.currentUser.id!],
            })).fetch();
            const userWatchList: any[] = userWatchlistByUserIds.data[oThis.currentUser.id!];
            let watchlistedTokenIds: string[] = [];
            if (!Basic.isEmptyObject(userWatchList)) {
                watchlistedTokenIds = userWatchList.map((token: any) => token.tokenId);
            }
            oThis.watchlistedTokenIdsSet = new Set(watchlistedTokenIds);
            Logger.info(`GetAgentTokens::fetchWatchlistedTokens:: Found ${JSON.stringify(Array.from(oThis.watchlistedTokenIdsSet))}, size: ${oThis.watchlistedTokenIdsSet.size} watchlisted tokens for user ${oThis.currentUser.id}`);
        } catch (error: any) {
            Logger.error(`GetAgentTokens::fetchWatchlistedTokens:: Error fetching watchlisted tokens: ${JSON.stringify(error.message)}`);
        }
    }

    private async fetchWatchlistedGenesisTokens() {
        const oThis = this;
        if (!oThis.currentUser.id) return;
        try {
            const userWatchlistGenesisByUserIds = await (new UserWatchlistGenesisByUserIds({
                userIds: [oThis.currentUser.id!],
            })).fetch();
            const userWatchListedGenesis: any[] = userWatchlistGenesisByUserIds.data[oThis.currentUser.id!];
            let watchlistedGenesisTokenIds: string[] = [];
            if (!Basic.isEmptyObject(userWatchListedGenesis)) {
                watchlistedGenesisTokenIds = userWatchListedGenesis.map((genesis: any) => genesis.tokenId);
            }
            oThis.watchlistedGenesisTokenIdsSet = new Set(watchlistedGenesisTokenIds);
            Logger.info(`GetAgentTokens::fetchWatchlistedGenesisTokens:: Found ${JSON.stringify(Array.from(oThis.watchlistedGenesisTokenIdsSet))}, size: ${oThis.watchlistedGenesisTokenIdsSet.size} watchlisted genesis tokens for user ${oThis.currentUser.id}`);
        } catch (error: any) {
            Logger.error(`GetAgentTokens::fetchWatchlistedGenesisTokens:: Error fetching watchlisted genesis tokens: ${JSON.stringify(error.message)}`);
        }
    }

    private async fetchAgentTokens() {
        const oThis = this;
        const limit = ExplorerConstant.pageSize;
        const offset = oThis.page > 1 ? limit * (oThis.page - 1) : ExplorerConstant.defaultOffset;
        const tokenDetailsModel = (await Postgres.getDbModels()).tokenDetails;
        const lqaHistoricalMetricModel = (await Postgres.getDbModels()).lqaHistoricalMetric;
        try {
            const sortColumn = ExplorerConstant.sortTypeColumnIdentifierMap[oThis.sortBy];
            const sortDirection = oThis.sortOrder == ExplorerConstant.sortOrderASC ? 'asc' : 'desc';
            const categories = oThis.subCategory
                ? AIAgentConstant.publicSubCategoryToCategoriesMap[oThis.subCategory]
                : oThis.ecosystem.split(',')
                    .map(eco => eco.trim())
                    .flatMap(eco => AIAgentConstant.publicEcosystemToCategoriesMap[eco] || [])

            let tokenIds: string[] = [];
            let totalTokenCount = 0;

            const network = oThis.network == AIAgentConstant.networkBase.toLowerCase()
                ? AIAgentConstant.network[oThis.network.toUpperCase()]
                : undefined;

            Logger.info(`GetAgentTokens::fetchAgentTokens::watchlistedTokenIds: ${JSON.stringify(oThis.watchlistedTokenIdsSet)}`);
            Logger.info(`GetAgentTokens::fetchAgentTokens::watchlistedGenesisTokenIdsSet: ${JSON.stringify(oThis.watchlistedGenesisTokenIdsSet)}`);

            // If watchlist filter is enabled and user is logged in, pass the watchlisted token IDs
            const watchlistedTokenIds = (oThis.watchlist && oThis.currentUser.id)
                ? [...Array.from(oThis.watchlistedTokenIdsSet), ...Array.from(oThis.watchlistedGenesisTokenIdsSet)]
                : undefined;

            const td = await tokenDetailsModel.getSortedTokenIds(
                categories,
                limit,
                offset,
                oThis.search,
                sortColumn,
                sortDirection,
                watchlistedTokenIds,
                network
            );

            tokenIds = td?.tokenIds || [];
            totalTokenCount = td?.count || 0;

            Logger.info(`GetAgentTokens::fetchAgentTokens:: totalTokenCount: ${totalTokenCount} | tokenIds: ${JSON.stringify(tokenIds)}`);

            oThis.setPeginationAttributes(totalTokenCount);

            if (tokenIds.length > 0) {
                const tokenDetailsByTokenIds = await (new VirtualTokenDetailsByTokenIds({
                    tokenIds: tokenIds,
                })).fetch();

                Logger.info(`GetAgentTokens::fetchAgentTokens::tokenDetailsByTokenIds::${JSON.stringify(tokenDetailsByTokenIds.data)}`);
                if (!tokenDetailsByTokenIds.success) {
                    return oThis.unauthorizedResponse('invalidParams', 's_s_gat_fat_0');
                }
                const tokens = tokenDetailsByTokenIds.data;

                const lqaMetricsByTokenId = await lqaHistoricalMetricModel.getByTokenIds(tokenIds);

                for (const tokenId of tokenIds) {
                    const token = tokens[tokenId];
                    const tokenLQAMetrics = lqaMetricsByTokenId[tokenId];
                    if (token) {
                        const formattedTokenData = await oThis.getFormattedTokenData(token, tokenLQAMetrics);
                        oThis.agents.push(formattedTokenData);
                    }
                }
            }
        } catch (error: any) {
            Logger.error(`GetAgentTokens::fetchAgentTokens::Error fetching tokens data: ${JSON.stringify(error.message)}`);
        }
    }

    private async getFormattedTokenData(token: any, tokenLQAMetrics: LQAHistoricalMetricModelAttributes) {
        const oThis = this;
        // const lqaIndicator = oThis.getLQAIndicator(token);
        Logger.info(`GetAgentTokens::getFormattedTokenData::token: ${JSON.stringify(token?.token_id)} & price ${token?.usd_price} : ${JSON.stringify(token)}`);
        const formattedTokenData = {
            id: token?.token_id,
            name: token?.token_name,
            ticker: `$${token?.token}`,
            icon: token?.image,
            category: token?.category,
            network: AIAgentConstant.networkMap[token?.network],
            ca: token?.token_address,
            volume: Utils.isValidChange(token?.volume_24h) ? '$' + Utils.millify(token?.volume_24h, Utils.getPrecision(token?.volume_24h)) : null,
            price: {
                current: Utils.isValidChange(token?.usd_price) ? '$' + Utils.millify(token?.usd_price, Utils.getPrecision(token?.usd_price)) : null,
                changePercentage1h: Utils.isValidChange(token?.price_change_percentage_1h) ? Utils.millify(token?.price_change_percentage_1h, Utils.getPrecision(token?.price_change_percentage_1h)) + '%' : null,
                changePercentage24h: Utils.isValidChange(token?.price_change_percentage_24h) ? Utils.millify(token?.price_change_percentage_24h, Utils.getPrecision(token?.price_change_percentage_24h)) + '%' : null
            },
            mcap: {
                current: Utils.isValidChange(token?.mcap) ? '$' + Utils.millify(token?.mcap, Utils.getPrecision(token?.mcap)) : null,
                changePercentage15m: Utils.isValidChange(token?.mcap_change_percentage_15m) ? Utils.millify(token?.mcap_change_percentage_15m, Utils.getPrecision(token?.mcap_change_percentage_15m)) + '%' : null,
                changePercentage1h: Utils.isValidChange(token?.mcap_change_percentage_1h) ? Utils.millify(token?.mcap_change_percentage_1h, Utils.getPrecision(token?.mcap_change_percentage_1h)) + '%' : null,
                changePercentage6h: Utils.isValidChange(token?.mcap_change_percentage_6h) ? Utils.millify(token?.mcap_change_percentage_6h, Utils.getPrecision(token?.mcap_change_percentage_6h)) + '%' : null,
                changePercentage24h: Utils.isValidChange(token?.mcap_change_percentage_24h) ? Utils.millify(token?.mcap_change_percentage_24h, Utils.getPrecision(token?.mcap_change_percentage_24h)) + '%' : null
            },
            mindshare: {
                "3d": token?.mindshare_7d != null && token?.mindshare_3d != 0 ? Utils.millify(token?.mindshare_3d, Utils.getPrecision(token?.mindshare_3d)) + '%' : null,
                "7d": token?.mindshare_7d != null && token?.mindshare_7d != 0 ? Utils.millify(token?.mindshare_7d, Utils.getPrecision(token?.mindshare_7d)) + '%' : null,
            },
            smartNetflow: {
                inflow15m: token?.top_25_holder_buy_15m ? Utils.millify(token?.top_25_holder_buy_15m, Utils.getPrecision(token?.top_25_holder_buy_15m)) : '0',
                inflow1h: token?.top_25_holder_buy_1h ? Utils.millify(token?.top_25_holder_buy_1h, Utils.getPrecision(token?.top_25_holder_buy_1h)) : '0',
                inflow6h: token?.top_25_holder_buy_6h ? Utils.millify(token?.top_25_holder_buy_6h, Utils.getPrecision(token?.top_25_holder_buy_6h)) : '0',
                inflow24h: token?.top_25_holder_buy_24h ? Utils.millify(token?.top_25_holder_buy_24h, Utils.getPrecision(token?.top_25_holder_buy_24h)) : '0',
                outflow15m: token?.top_25_holder_sold_15m ? Utils.millify(token?.top_25_holder_sold_15m, Utils.getPrecision(token?.top_25_holder_sold_15m)) : '0',
                outflow1h: token?.top_25_holder_sold_1h ? Utils.millify(token?.top_25_holder_sold_1h, Utils.getPrecision(token?.top_25_holder_sold_1h)) : '0',
                outflow6h: token?.top_25_holder_sold_6h ? Utils.millify(token?.top_25_holder_sold_6h, Utils.getPrecision(token?.top_25_holder_sold_6h)) : '0',
                outflow24h: token?.top_25_holder_sold_24h ? Utils.millify(token?.top_25_holder_sold_24h, Utils.getPrecision(token?.top_25_holder_sold_24h)) : '0',
                netflow15m: this.getFormattedSmartMoneyNetFlow(token?.top_25_holder_buy_15m, token?.top_25_holder_sold_15m),
                netflow1h: this.getFormattedSmartMoneyNetFlow(token?.top_25_holder_buy_1h, token?.top_25_holder_sold_1h),
                netflow6h: this.getFormattedSmartMoneyNetFlow(token?.top_25_holder_buy_6h, token?.top_25_holder_sold_6h),
                netflow24h: this.getFormattedSmartMoneyNetFlow(token?.top_25_holder_buy_24h, token?.top_25_holder_sold_24h),
            },
            confidence: {
                current: tokenLQAMetrics?.confidence_15m ? Utils.millify(tokenLQAMetrics?.confidence_15m, Utils.getPrecision(tokenLQAMetrics?.confidence_15m)) + '%' : null,
                changePercentage15m: Utils.isValidChange(tokenLQAMetrics?.confidence_change_15m) ? Utils.millify(tokenLQAMetrics?.confidence_change_15m, Utils.getPrecision(tokenLQAMetrics?.confidence_change_15m)) + '%' : null,
                changePercentage1h: Utils.isValidChange(tokenLQAMetrics?.confidence_change_1h) ? Utils.millify(tokenLQAMetrics?.confidence_change_1h, Utils.getPrecision(tokenLQAMetrics?.confidence_change_1h)) + '%' : null,
                changePercentage6h: Utils.isValidChange(tokenLQAMetrics?.confidence_change_6h) ? Utils.millify(tokenLQAMetrics?.confidence_change_6h, Utils.getPrecision(tokenLQAMetrics?.confidence_change_6h)) + '%' : null,
                changePercentage24h: Utils.isValidChange(tokenLQAMetrics?.confidence_change_24h) ? Utils.millify(tokenLQAMetrics?.confidence_change_24h, Utils.getPrecision(tokenLQAMetrics?.confidence_change_24h)) + '%' : null
            },
            devWallets: token?.token_id == 'loky-by-virtuals' ? 1 : (token?.bundle_wallet_count || null),
            devBundleSupply: Utils.isValidChange(token?.bundle_wallet_supply_percentage) ? Utils.millify(token?.bundle_wallet_supply_percentage, Utils.getPrecision(token?.bundle_wallet_supply_percentage)) + '%' : null,
            isWatchlisted: oThis.watchlistedTokenIdsSet.has(token?.token_id) || oThis.watchlistedGenesisTokenIdsSet.has(token?.token_id),
            askQuestion: `Analyze $${token?.token} for short-term investment`
        }
        Logger.debug(`GetAgentTokens::getFormattedTokenData::formattedTokenData: ${JSON.stringify(formattedTokenData)}`);
        return formattedTokenData;
    }

    private getFormattedSmartMoneyNetFlow(totalBuy: number | null, totalSold: number | null) {
        const oThis = this;
        const inflow = totalBuy ? Number(totalBuy) : 0;
        const outflow = totalSold ? Number(totalSold) : 0;
        const netFlow = inflow - outflow;
        return netFlow ? Utils.millify(netFlow, Utils.getPrecision(netFlow)) : '0';
    }

    private getDevWalletNetFlow(token: any) {
        const inflow = token?.dev_wallet_inflow_amount_24h ? Number(token?.dev_wallet_inflow_amount_24h) : 0;
        const outflow = token?.dev_wallet_outflow_amount_24h ? Number(token?.dev_wallet_outflow_amount_24h) : 0;
        const netFlow = inflow - outflow;
        return netFlow ? Utils.millify(netFlow, Utils.getPrecision(netFlow)) : null;
    }

    private getLQAIndicator(token: any): string {
        const netFlow = 0;
        const bought = token.devWalletBought || 0;
        const sold = token.devWalletSold || 0;
        const devActivity = (bought + sold) == 0 ? 'NO_ACTIVITY' : 'ACTIVE';

        // Calculate net flow percentage
        const netFlowPercentage = ((bought - sold) / (bought + sold)) * 100;

        // Case 1: Net flow is positive and dev wallet sold is less than 5M
        if (netFlow > 0 && sold < 5000000) {
            return ExplorerConstant.indicatorPositive;
        }
        // Case 2: Net flow is positive and dev wallet sold is greater than 5M
        else if (netFlow > 0 && sold >= 5000000) {
            return ExplorerConstant.indicatorNegative;
        }
        // Case 3: Net flow is positive and dev wallet have 'no activity'
        else if (netFlow > 0 && devActivity === 'NO_ACTIVITY') {
            return ExplorerConstant.indicatorPositive;
        }
        // Case 4: Net flow is negative and dev wallet sold is less than 5M
        else if (netFlow < 0 && sold < 5000000) {
            return ExplorerConstant.indicatorNegative;
        }
        // Case 5: Net flow percentage is ≤5 and > 0, and dev wallet have 'NO_ACTIVITY'
        else if (netFlowPercentage <= 5 && netFlowPercentage > 0 && devActivity === 'NO_ACTIVITY') {
            return ExplorerConstant.indicatorNeutral;
        }
        // Case 6: Net flow percentage is ≤5 and < 0, and dev wallet have 'NO_ACTIVITY'
        else if (netFlowPercentage <= 5 && netFlowPercentage < 0 && devActivity === 'NO_ACTIVITY') {
            return ExplorerConstant.indicatorNegative;
        }
        // Default case: Return a neutral indicator if no conditions are met
        return ExplorerConstant.indicatorNeutral;
    }

    private setPeginationAttributes(totalTokenCount: number) {
        const oThis = this;
        const totalPages = Math.ceil(totalTokenCount / ExplorerConstant.pageSize);
        oThis.page = oThis.page > totalPages ? totalPages : oThis.page;
        oThis.pagination = {
            currentPage: oThis.page,
            totalPages: totalPages,
            size: ExplorerConstant.pageSize,
            count: totalTokenCount
        }
    }

    private prepareResponse(): SuccessResponse {
        const oThis = this;
        Logger.info(`GetAgentTokens:: prepareResponse::Service execution completed`);
        return ResponseHelper.success({
            pagination: oThis.pagination,
            tokens: oThis.isTerminalAccess ? oThis.agents : oThis.agents.slice(0, 2)
        });
    }
}
