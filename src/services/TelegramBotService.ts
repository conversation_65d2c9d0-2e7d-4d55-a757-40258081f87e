import { Telegraf } from 'telegraf';
import { ExtraReplyMessage } from 'telegraf/typings/telegram-types';
import TelegramConstant from '../lib/constant/TelegramConstant';
import Logger from '../lib/Logger';
import { TelegramMessageAttributes } from '../lib/Types';
import AIAgentConstant from '../lib/constant/AIAgentConstant';
import SignalConstant from '../lib/constant/SignalConstant';

interface TelegramInlineKeyboardInterface {
    text: string;
    url: string;
}

export class TelegramBotService {
    private static instance: TelegramBotService | null = null;
    private bot: Telegraf;

    private constructor() {
        const oThis = this;
        const token = TelegramConstant.telegramBotToken;
        if (!token) {
            throw new Error('TelegramBotService::constructor:: TELEGRAM_BOT_TOKEN is not set in environment variables.');
        }
        if (!TelegramConstant.telegramBotEnabled) {
            Logger.info('TelegramBotService::constructor:: Telegram bot is disabled. To enable it, set TELEGRAM_BOT_ENABLED to true in environment variables.');
            return;
        }
        oThis.bot = new Telegraf(token);
        oThis.initializeBot();
    }

    public static getInstance(): TelegramBotService {
        if (!TelegramBotService.instance) {
            TelegramBotService.instance = new TelegramBotService();
        }
        return TelegramBotService.instance;
    }

    private initializeBot(): void {
        const oThis = this;

        // Handle errors
        oThis.bot.catch((err: any) => {
            Logger.error('TelegramBotService::initializeBot:: Error while initializing bot:', err);
        });

        // Start the bot
        oThis.bot.launch().catch(err => {
            Logger.error('TelegramBotService::initializeBot:: Failed to launch Telegram bot:', err);
        });

        // graceful stop
        process.once('SIGINT', () => oThis.bot.stop('SIGINT'));
        process.once('SIGTERM', () => oThis.bot.stop('SIGTERM'));
    }

    /**
     * Send a message to a specific chat and optionally to a specific thread
     * @param chatId The ID of the chat (group or private)
     * @param message The message to send
     * @param threadId Optional thread ID for forum topics
     * @param options Optional extra parameters for the message
     * @param disableWebPagePreview Optional parameters to disable web page preview
     */
    public async sendMessage(
        chatId: string | number,
        message: string,
        threadId?: number,
        options?: ExtraReplyMessage,
        disableWebPagePreview: boolean = false,
    ): Promise<void> {
        const oThis = this;
        try {
            const messageOptions = {
                ...options,
                ...(threadId && { message_thread_id: threadId }),
                disable_web_page_preview: disableWebPagePreview,
            };
            await oThis.bot.telegram.sendMessage(chatId, message, messageOptions);
        } catch (error) {
            Logger.error('TelegramBotService::sendMessage:: Error sending Telegram message:', error);
            throw error;
        }
    }

    /**
     * Send a message with HTML formatting
     * @param chatId The ID of the chat (group or private)
     * @param message The message to send (can include HTML tags)
     * @param threadId Optional thread ID for forum topics
     */
    public async sendHTMLMessage(
        chatId: string | number,
        message: string,
        threadId?: number
    ): Promise<void> {
        await this.sendMessage(chatId, message, threadId, { parse_mode: 'HTML' });
    }

    /**
     * Send a message with Markdown formatting
     * @param chatId The ID of the chat (group or private)
     * @param message The message to send (can include Markdown syntax)
     * @param threadId Optional thread ID for topics
     */
    public async sendMarkdownMessage(
        chatId: string | number,
        message: string,
        threadId?: number,
        disableWebPagePreview: boolean = false
    ): Promise<void> {
        await this.sendMessage(chatId, message, threadId, { parse_mode: 'Markdown' }, disableWebPagePreview);
    }

    /**
     * Send a photo with caption to a specific chat
     * @param chatId The ID of the chat (group or private)
     * @param photoUrl URL of the photo to send
     * @param caption Optional caption for the photo
     * @param threadId Optional thread ID for topics
     * @param options Optional extra parameters for the message
     */
    public async sendPhotoMessage(
        chatId: string | number,
        photoUrl: string,
        caption?: string,
        threadId?: number,
        options?: ExtraReplyMessage
    ): Promise<void> {
        const oThis = this;
        try {
            const messageOptions = {
                ...options,
                caption,
                ...(threadId && { message_thread_id: threadId })
            };
            await oThis.bot.telegram.sendPhoto(chatId, photoUrl, messageOptions);
        } catch (error) {
            Logger.error('TelegramBotService::sendPhotoMessage:: Error sending Telegram photo message:', error);
            throw error;
        }
    }

    /**
     * Send a signal message with appropriate buttons based on signal type
     * @param signalType Type of signal (e.g., 'redPilled')
     * @param telegramObject Object containing message details and optional image
     */
    public async sendSignalMessage(
        signalType: string,
        telegramObject: TelegramMessageAttributes
    ): Promise<void> {
        const oThis = this;
        const messageOptions: ExtraReplyMessage = {
            parse_mode: 'Markdown',
            reply_markup: oThis.getSignalButtons(signalType, telegramObject)
        };
        const signalTextSuffix = TelegramConstant.signalTextSuffix;
        const telegramText = telegramObject.telegramText;
        const telegramTextWithSuffix = `${telegramText}\n\n${signalTextSuffix}`;

        if (telegramObject.tokenImage) {
            // If there's an image, send it with the message as caption
            await oThis.sendPhotoMessage(
                TelegramConstant.lokyTerminalChatId,
                telegramObject.tokenImage,
                telegramTextWithSuffix,
                TelegramConstant.lokyTerminalThreadId,
                messageOptions
            );
        } else {
            // If no image, send as regular message
            await oThis.sendMessage(
                TelegramConstant.lokyTerminalChatId,
                telegramTextWithSuffix,
                TelegramConstant.lokyTerminalThreadId,
                messageOptions
            );
        }
    }

    /**
     * Send the Top AI Agents by Mindshare post to Telegram
     * @param message Post message to send
     */
    public async sendMindsharePostToTelegram(message: string): Promise<void> {
        const oThis = this;
        try {
            await oThis.sendMarkdownMessage(TelegramConstant.lokyTerminalChatId, message, TelegramConstant.lokyTerminalThreadId, true);
            Logger.info("TelegramBotService::sendMindsharePostToTelegram:: Message sent successfully.");
        } catch (error) {
            Logger.error("TelegramBotService::sendMindsharePostToTelegram:: Error sending message:", error);
        }
    }

    private getSignalButtons(type: string, telegramObject: TelegramMessageAttributes) {
        // Create an array to hold all buttons
        const allButtons: Array<TelegramInlineKeyboardInterface> = [
            {
                text: '✨Terminal',
                url: TelegramConstant.lokyTerminalUrl
            }
        ];

        // Add additional buttons based on signal type
        if (TelegramConstant.signalSupportForTelegramCategories.includes(type)) {
            if (telegramObject.tokenAddress) {
                if (type === SignalConstant.newAgentStrId) {
                    allButtons.push({
                        text: '🚀Virtuals',
                        url: TelegramConstant.virtualsPrototypeUrl(telegramObject.tokenAddress)
                    });
                } else {
                    if (telegramObject.virtualId) {
                        allButtons.push({
                            text: '🚀Virtuals',
                            url: TelegramConstant.virtualsSentientUrl(telegramObject?.virtualId)
                        });
                    }
                    allButtons.push({
                        text: '📊DexScreener',
                        url: TelegramConstant.dexScannerUrl(telegramObject.network, telegramObject.tokenAddress)
                    });
                }

                if (type === SignalConstant.newAgentStrId || type === SignalConstant.redPilledStrId) {
                    if (telegramObject.devWallet) {
                        allButtons.push(
                            {
                                text: '𝕏 Search (Dev Wallet)',
                                url: TelegramConstant.xDevSearchUrl(telegramObject.devWallet)
                            }
                        );
                    }
                }

                if (telegramObject.network === AIAgentConstant.networkMap[AIAgentConstant.network.BASE]) {
                    allButtons.push({
                        text: '🪙Token',
                        url: TelegramConstant.basescanTokenUrl(telegramObject.tokenAddress)
                    });
                    allButtons.push(
                        {
                            text: '🤖Sigma Bot 5',
                            url: TelegramConstant.sigma5TradingBotUrl("******************************************")
                        }
                    );
                    allButtons.push(
                        {
                            text: '🤖Sigma Bot 10',
                            url: TelegramConstant.sigma10TradingBotUrl("******************************************")
                        }
                    );
                }
            }

            if (telegramObject.devWallet && telegramObject.network === AIAgentConstant.networkMap[AIAgentConstant.network.BASE]) {
                allButtons.push({
                    text: '💰Dev Wallet',
                    url: TelegramConstant.basescanAddressUrl(telegramObject.devWallet)
                });
            }

            if (telegramObject.virtualsAppId) {
                allButtons.push({
                    text: '🚀Virtuals',
                    url: TelegramConstant.virtualsGenesisUrl(telegramObject.virtualsAppId)
                });
            }

            if (telegramObject.addLokyDocsButton) {
                allButtons.push({
                    text: '📚Loky Docs',
                    url: TelegramConstant.lokyDocsUrl
                });
            }

            if (telegramObject.addLokyXButton) {
                allButtons.push({
                    text: '𝕏 @0xLoky_AI',
                    url: TelegramConstant.lokyXUrl
                });
            }

            if (telegramObject.addLokyHomePageButton) {
                allButtons.push({
                    text: '🏠 Loky Homepage',
                    url: TelegramConstant.lokyHomePageUrl
                });
            }
        }

        // Organize buttons into rows with a maximum of 2 buttons per row
        const MAX_BUTTONS_PER_ROW = 2;
        const orderedButtons: Array<Array<TelegramInlineKeyboardInterface>> = [];

        for (let i = 0; i < allButtons.length; i += MAX_BUTTONS_PER_ROW) {
            orderedButtons.push(allButtons.slice(i, i + MAX_BUTTONS_PER_ROW));
        }

        return {
            inline_keyboard: orderedButtons
        };
    }
}
