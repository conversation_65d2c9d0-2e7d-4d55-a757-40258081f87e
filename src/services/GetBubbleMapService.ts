import Logger from '../lib/Logger';
import ResponseHelper from '../lib/helper/ResponseHelper';
import { SuccessResponse, ErrorResponse } from '../lib/Types';
import ServiceBase from "./Base";
import Utils from '../lib/Utils';
import DevBundleWalletsByTokenAddresses from '../lib/cache/DevBundleWalletsByTokenAddresses';
import Top25HoldersByTokenAddresses from '../lib/cache/Top25HoldersByTokenAddresses';
import TokenDetailsByTicker from '../lib/cache/TokenDetailsByTickers';
import GeneralConstant from '../config/GeneralConstant';
import GeneralValidator from '../lib/validator/GeneralValidator';

export default class GetBubbleMapService extends ServiceBase {

    private authKey: string;

    private tokenTicker: string;

    private tokenAddress: string;

    private devBundleWalletList: string[];

    private devBundleWalletMap: any;

    private top25Holders: any;

    constructor(params: { api_key: string, ticker: string, internalDecodedParams: any }) {
        super(params);
        const oThis = this;
        oThis.authKey = params.api_key;
        oThis.tokenTicker = params.ticker?.toLowerCase();
        oThis.devBundleWalletList = [];
    }

    // internal use function to get the wallet map
    public async getWalletMap(tokenAddress: string): Promise<{ devBundleWalletMap: any, top25Holders: any }> {
        const oThis = this;
        oThis.tokenAddress = tokenAddress;
        // format top25 holders first since it's needed by formatDevBundleWalletMap
        await oThis.formatTop25Holders();
        await oThis.formatDevBundleWalletMap();
        return {
            devBundleWalletMap: oThis.devBundleWalletMap,
            top25Holders: oThis.top25Holders,
        }
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;

        await oThis.validateAuthKey();

        await oThis.validateParams();

        await oThis.fetchTokenDetails();

        await oThis.formatTop25Holders();

        await oThis.formatDevBundleWalletMap()

        return await oThis.prepareResponse();
    }

    private async validateAuthKey(): Promise<void | ErrorResponse> {
        const oThis = this;
        if (!oThis.authKey || oThis.authKey.trim() !== GeneralConstant.internalAuthKey) {
            return oThis.unauthorizedResponse('somethingWentWrong', 's_s_gbms_vak_0');
        }
    }

    private async validateParams(): Promise<void | ErrorResponse> {
        const oThis = this;
        if (!oThis.tokenTicker || oThis.tokenTicker.trim() === '') {
            return oThis.unauthorizedResponse('invalidParams', 's_s_gbms_vp_0');
        }
    }

    private async fetchTokenDetails() {
        const oThis = this;
        Logger.info(`GetBubbleMapService::fetchTokenDetails::Fetching token details for ticker: ${oThis.tokenTicker}`);
        const tokenDetailsByTicker = await (new TokenDetailsByTicker({
            tickers: [oThis.tokenTicker]
        })).fetch();
        const tokens = tokenDetailsByTicker.data[oThis.tokenTicker] || [];
        Logger.debug(`GetBubbleMapService::fetchTokenDetails::Result from cache ${JSON.stringify(tokens)}`);
        if (!tokens.length) {
            Logger.info(`GetBubbleMapService::fetchTokenDetails::No data found for token ${oThis.tokenTicker}`);
            return oThis.badResponse('invalidTicker', 's_s_gbms_ftd_0');
        }
        const tokenAddress = tokens[0].token_address
        if (!tokenAddress) {
            return oThis.badResponse('invalidTicker', 's_s_gbms_ftd_1');
        }
        oThis.tokenAddress = tokenAddress;
    }

    private async formatDevBundleWalletMap(): Promise<any> {
        const oThis = this;
        try {
            const devBundleWalletsByTokenAddresses = await new DevBundleWalletsByTokenAddresses({
                tokenAddresses: [oThis.tokenAddress.toLowerCase()],
            }).fetch();

            Logger.debug(`GetBubbleMapService::formatDevBundleWalletMap::devBundleWalletsByTokenAddresses:: ${JSON.stringify(devBundleWalletsByTokenAddresses)}`);

            const devBundleWallets = devBundleWalletsByTokenAddresses.data[oThis.tokenAddress] || [];

            if (!GeneralValidator.validateNonEmptyObject(devBundleWallets) || devBundleWallets.length === 0) {
                oThis.devBundleWalletMap = [];
            } else {
                const type0 = devBundleWallets.filter((w: any) => w.type === 0);
                const type1 = devBundleWallets.filter((w: any) => w.type === 1);
                const type2 = devBundleWallets.filter((w: any) => w.type === 2);

                oThis.devBundleWalletMap = type0.map((devWallet: any) => {
                    try {
                        const level1 = type1
                            .filter((w: any) => w.parentWalletAddress === devWallet.walletAddress)
                            .map((w1: any) => {
                                try {
                                    oThis.devBundleWalletList.push(w1.walletAddress);
                                    return {
                                        devWallet: w1.walletAddress,
                                        spreadWallets: type2
                                            .filter((w2: any) => w2.parentWalletAddress === w1.walletAddress)
                                            .map((w2: any) => w2.walletAddress)
                                    };
                                } catch (innerError: any) {
                                    Logger.error(`GetBubbleMapService::formatDevBundleWalletMap::Error processing level2 wallets:: ${JSON.stringify(innerError)}`);
                                    return {
                                        devWallet: w1.walletAddress,
                                        spreadWallets: []
                                    };
                                }
                            });

                        oThis.devBundleWalletList.push(devWallet.walletAddress);
                        return {
                            devWallet: devWallet.walletAddress,
                            spreadWallets: level1
                        };
                    } catch (mapError: any) {
                        Logger.error(`GetBubbleMapService::formatDevBundleWalletMap::Error processing level1 wallets:: ${JSON.stringify(mapError)}`);
                        oThis.devBundleWalletList.push(devWallet.walletAddress);
                        return {
                            devWallet: devWallet.walletAddress,
                            spreadWallets: []
                        };
                    }
                });
            }

            Logger.info(`GetBubbleMapService::formatDevBundleWalletMap::Initial devBundleWalletMap has ${oThis.devBundleWalletMap.length} entries`);
            Logger.debug(`GetBubbleMapService::formatDevBundleWalletMap::All bundle wallet list: ${JSON.stringify(oThis.devBundleWalletList)}`);
            Logger.debug(`GetBubbleMapService::formatDevBundleWalletMap::Dev bundle wallet map before modification:: ${JSON.stringify(oThis.devBundleWalletMap)}`);
            await oThis.addTop25HoldersToDevBundleWalletMap();

            Logger.info(`GetBubbleMapService::formatDevBundleWalletMap::Final devBundleWalletMap has ${oThis.devBundleWalletMap.length} entries`);
            Logger.debug(`GetBubbleMapService::formatDevBundleWalletMap::devBundleWalletMap:: ${JSON.stringify(oThis.devBundleWalletMap)}`);
        } catch (error: any) {
            Logger.error(`GetBubbleMapService::addTop25HoldersToDevBundleWalletMap::Error:: ${JSON.stringify(error.message)}`);
            oThis.devBundleWalletMap = [];
        }

    }

    private async addTop25HoldersToDevBundleWalletMap(): Promise<void> {
        const oThis = this;
        try {
            if (!oThis.devBundleWalletMap) {
                Logger.error(`GetBubbleMapService::addTop25HoldersToDevBundleWalletMap::devBundleWalletMap is not initialized`);
                oThis.devBundleWalletMap = [];
            }

            // Count how many wallets we add
            let addedCount = 0;

            // Add each top25Holder to devBundleWalletMap if not already present as a devWallet
            for (const walletAddress of Object.keys(oThis.top25Holders)) {
                const lowerCaseWalletAddress = walletAddress.toLowerCase();
                if (oThis.devBundleWalletList.includes(lowerCaseWalletAddress)) {
                    Logger.debug(`GetBubbleMapService::addTop25HoldersToDevBundleWalletMap::Skipping wallet already exists as devWallet: ${lowerCaseWalletAddress}`);
                    continue
                }
                oThis.devBundleWalletMap.push({
                    devWallet: lowerCaseWalletAddress,
                    spreadWallets: []
                });
                addedCount++;

                Logger.debug(`GetBubbleMapService::addTop25HoldersToDevBundleWalletMap::Added top holder to devBundleWalletMap: ${lowerCaseWalletAddress}`);
            }

            Logger.info(`GetBubbleMapService::addTop25HoldersToDevBundleWalletMap::Added ${addedCount} top holder wallets to devBundleWalletMap`);
        } catch (error: any) {
            Logger.error(`GetBubbleMapService::addTop25HoldersToDevBundleWalletMap::Error:: ${JSON.stringify(error.message)}`);
            if (!oThis.devBundleWalletMap) {
                oThis.devBundleWalletMap = [];
            }
        }
    }

    private async formatTop25Holders(): Promise<any> {
        const oThis = this;

        try {
            const top25HoldersByTokenAddresses = await new Top25HoldersByTokenAddresses({
                tokenAddresses: [oThis.tokenAddress],
            }).fetch();
            const top25Holders = top25HoldersByTokenAddresses.data[oThis.tokenAddress] || [];

            if (top25Holders.length === 0) {
                oThis.top25Holders = {};
                return;
            }

            const holdersArray = top25Holders.map((holder: any) => ({
                walletAddress: holder.walletAddress,
                holdingPercentage: holder.holdingPercentage ? parseFloat(holder.holdingPercentage) : null,
                balance: holder.balance ? parseFloat(holder.balance) : null
            }));

            holdersArray.sort((a: any, b: any) => {
                if (a.holdingPercentage === null) return 1;
                if (b.holdingPercentage === null) return -1;
                return b.holdingPercentage - a.holdingPercentage;
            });

            oThis.top25Holders = holdersArray.reduce((result: any, holder: any) => {
                result[holder.walletAddress] = [
                    holder.holdingPercentage !== null ? `${holder.holdingPercentage}%` : null,
                    holder.balance !== null ? `${Utils.millify(holder.balance, Utils.getPrecision(holder.balance))}` : null,
                ]
                return result;
            }, {});

            Logger.info(`GetBubbleMapService::formatTop25Holders::Found ${Object.keys(oThis.top25Holders).length} top25Holders`);
            Logger.debug(`GetBubbleMapService::formatTop25Holders::top25Holders:: ${JSON.stringify(oThis.top25Holders)}`);
        } catch (error: any) {
            Logger.error(`GetBubbleMapService::formatTop25Holders::Error:: ${JSON.stringify(error.message)}`);
            oThis.top25Holders = {};
        }
    }

    private async prepareResponse(): Promise<SuccessResponse> {
        const oThis = this;
        Logger.info(`GetBubbleMapService::prepareResponse::Service execution completed`);
        return ResponseHelper.success({
            devBundleWalletMap: oThis.devBundleWalletMap,
            top25Holders: oThis.top25Holders,
        });
    }
}
