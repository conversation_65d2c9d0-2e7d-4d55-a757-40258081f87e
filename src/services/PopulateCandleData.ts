import { ErrorResponse, SuccessResponse } from "../lib/Types";
import ResponseHelper from "../lib/helper/ResponseHelper";
import Logger from "../lib/Logger";
import ServiceBase from "./Base";
import Postgres from "../lib/Postgres";
import TokenCandleData from "../models/TokenCandleData";
import SolanaCandleData from "../models/SolanaCandleData";
import EthereumCandleData from "../models/EthereumCandleData";
import TokenPools from "../models/TokenPools";
import TokenDetails from "../models/TokenDetails";
import TokenCategoryMap from "../models/TokenCategoryMap";
import BasicHelper from "../lib/helper/Basic";
import AIAgentConstant from "../lib/constant/AIAgentConstant";
import { CoinGeckoHelper } from "../lib/helper/CoinGeckoHelper";
import { TechnicalIndicators } from "../lib/helper/TechnicalIndicators";
import VirtualTokenDetailsByTokenIds from "../lib/cache/VirtualTokenDetailsByTokenIds";
import TokenDetailsByTokenAddresses from "../lib/cache/TokenDetailsByTokenAddresses";
import VirtualTokenDetailsByTokenAddresses from "../lib/cache/VirtualTokenDetailsByTokenAddresses";
import moment from "moment";
import { Op } from "sequelize";

export default class PopulateCandleData extends ServiceBase {

    private tokenPoolsModel: TokenPools;
    private tokenCandleDataModel: TokenCandleData;
    private solanaCandleDataModel: SolanaCandleData;
    private ethereumCandleDataModel: EthereumCandleData;
    private tokenDetailsModel: TokenDetails;
    private tokenCategoryMapModel: TokenCategoryMap;
    private limit: number;
    private network: number;
    private category: string | null;
    private updatedVirtualTokenIds: string[] = [];

    constructor(params: { limit: number, network: number, category?: string }) {
        super(params);
        const oThis = this;
        oThis.limit = params.limit || 120; // 24 hours * 5
        oThis.network = params.network;
        oThis.category = params.category || null;
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        const models = await Postgres.getDbModels();
        oThis.tokenPoolsModel = models.tokenPools;
        oThis.tokenCandleDataModel = models.tokenCandleData;
        oThis.solanaCandleDataModel = models.solanaCandleData;
        oThis.ethereumCandleDataModel = models.ethereumCandleData;
        oThis.tokenDetailsModel = models.tokenDetails;
        oThis.tokenCategoryMapModel = models.tokenCategoryMap;

        Logger.info(`PopulateCandleData::populateCandleData::servicePerform::Limit: ${oThis.limit}`);

        if (oThis.category) {
            // Validate that the category belongs to the network
            if (!oThis.isValidCategoryForNetwork(oThis.category, oThis.network)) {
                Logger.error(`PopulateCandleData::populateCandleData::Invalid category for network: ${oThis.category} | network: ${oThis.network}`);
                return ResponseHelper.error(['invalidParams'], {
                    error: 'Category does not belong to the specified network',
                    category: oThis.category,
                    network: oThis.network
                });
            }
            await oThis.populateCandleData();
            await oThis.populateTechnicalIndicators();
        } else if (!oThis.network) {
            // If no network specified, run for both networks
            Logger.info('PopulateCandleData::servicePerform::Running for both networks');

            // First run for Base network
            oThis.network = AIAgentConstant.network.BASE;
            await oThis.populateCandleData();
            await oThis.populateTechnicalIndicators();

            // Then run for Solana network
            oThis.network = AIAgentConstant.network.SOLANA;
            await oThis.populateCandleData();
            await oThis.populateTechnicalIndicators();

            // Then run for Ethereum network
            oThis.network = AIAgentConstant.network.ETH;
            await oThis.populateCandleData();
            await oThis.populateTechnicalIndicators();
        } else {
            await oThis.populateCandleData();
            await oThis.populateTechnicalIndicators();
        }

        await oThis.cleanupOlderData();

        await oThis.flushCache();

        return oThis.prepareResponse();
    }

    private isValidCategoryForNetwork(category: string, network: number): boolean {
        if (network == AIAgentConstant.network.BASE) {
            return [
                AIAgentConstant.categoryVirtualEcosystemGenesis,
                AIAgentConstant.categoryVirtualEcosystem,
                AIAgentConstant.categoryBaseEcosystem,
            ].includes(category);
        } else if (network == AIAgentConstant.network.SOLANA) {
            return [
                AIAgentConstant.categorySolanaEcosystem,
                AIAgentConstant.categorySolanaMeme
            ].includes(category);
        } else if (network == AIAgentConstant.network.ETH) {
            return [
                AIAgentConstant.categoryVirtualEcosystemGenesis
            ].includes(category);
        }
        return false;
    }

    private async populateCandleData(): Promise<void> {
        const oThis = this;
        try {
            if (oThis.category) {
                // If category is specified, process only that category
                await oThis.processCategoryCandleData(oThis.category);
            } else {
                // Otherwise, loop through all categories for the network
                const categories = AIAgentConstant.getCandleDataCategoriesByNetwork(oThis.network);
                for (const category of categories) {
                    Logger.info(`PopulateCandleData::populateCandleData::Processing category: ${category}`);
                    await oThis.processCategoryCandleData(category);
                }
            }
        } catch (error) {
            Logger.error(`PopulateCandleData::populateCandleData::Error in main process: ${error}`);
            throw error;
        }
    }

    private async processCategoryCandleData(category: string): Promise<void> {
        const oThis = this;
        try {
            // Get tokens for the specific category
            const tokenIds = await oThis.tokenCategoryMapModel.getTokenIdsByCategory(category);
            Logger.debug(`PopulateCandleData::processCategoryCandleData::category:${category}::tokenIds count: ${tokenIds.length}`);

            // get pool details for filtered tokens with specific category
            const poolDetails = await oThis.tokenPoolsModel.getPoolsByNetwork(oThis.network, tokenIds, category);
            Logger.debug(`PopulateCandleData::processCategoryCandleData::poolDetails::${JSON.stringify(poolDetails)}`);

            const candleModel = oThis.getCandleModelForNetwork();

            const existingCandleData = await candleModel.getExistingTokenIdsWithLastTimestamp();
            const candleDataMap = existingCandleData.reduce((acc, item) => {
                acc[item.tokenId] = item.lastTimestamp;
                return acc;
            }, {} as { [key: string]: Date });

            for (let idx = 0; idx < poolDetails.length; idx++) {
                const poolDetail = poolDetails[idx];
                const lastTimestamp = candleDataMap[poolDetail.tokenId];

                try {
                    if (lastTimestamp) {
                        Logger.debug(`PopulateCandleData::processCategoryCandleData::Token ${poolDetail.tokenId} already has candle data`);
                        const unixTimestamp = lastTimestamp.getTime() / 1000;

                        let ohlcvData = await CoinGeckoHelper.fetchCandleData(
                            AIAgentConstant.networkMap[poolDetail.network],
                            poolDetail.poolAddress,
                            oThis.limit
                        );

                        if (ohlcvData && ohlcvData.length > 0) {
                            const lastCandleUpdatedData = ohlcvData.find((data: any) => data[0] === unixTimestamp);
                            await oThis.updateLastCandleData(poolDetail.tokenId, lastTimestamp, lastCandleUpdatedData, candleModel);

                            const newCandleData = ohlcvData.filter((data: any) => data[0] > unixTimestamp);
                            if (newCandleData.length > 0 && lastCandleUpdatedData && lastCandleUpdatedData.length > 0) {
                                await oThis.saveCandleData(poolDetail.tokenId, newCandleData, candleModel);
                                Logger.debug(`PopulateCandleData::processCategoryCandleData::Saved new candle data for token ${poolDetail.tokenId}`);
                            }
                        }
                    } else {
                        Logger.info(`PopulateCandleData::processCategoryCandleData::Token ${poolDetail.tokenId} does not have candle data`);

                        const ohlcvData = await CoinGeckoHelper.fetchCandleData(
                            AIAgentConstant.networkMap[poolDetail.network],
                            poolDetail.poolAddress,
                            1000
                        );
                        Logger.debug(`PopulateCandleData::processCategoryCandleData::ohlcvData::${JSON.stringify(ohlcvData)}`);

                        await oThis.saveCandleData(poolDetail.tokenId, ohlcvData, candleModel);
                    }
                } catch (error) {
                    Logger.error(`PopulateCandleData::processCategoryCandleData::Error processing token ${poolDetail.tokenId}: ${error}`);
                }
            }
        } catch (error) {
            Logger.error(`PopulateCandleData::processCategoryCandleData::Error processing category ${category}: ${error}`);
            throw error;
        }
    }

    private getCandleModelForNetwork(): TokenCandleData | SolanaCandleData | EthereumCandleData {
        const oThis = this;
        switch (oThis.network) {
            case AIAgentConstant.network.SOLANA:
                return oThis.solanaCandleDataModel;
            case AIAgentConstant.network.ETH:
                return oThis.ethereumCandleDataModel;
            default:
                return oThis.tokenCandleDataModel;
        }
    }

    private async populateTechnicalIndicators(): Promise<void> {
        const oThis = this;
        try {
            if (oThis.category) {
                // If category is specified, process only that category
                await oThis.processCategoryTechnicalIndicators(oThis.category);
            } else {
                // Otherwise, loop through all categories for the network
                const categories = AIAgentConstant.getCandleDataCategoriesByNetwork(oThis.network);
                for (const category of categories) {
                    Logger.info(`PopulateCandleData::populateTechnicalIndicators::Processing technical indicators for category: ${category}`);
                    await oThis.processCategoryTechnicalIndicators(category);
                }
            }
        } catch (error) {
            Logger.error(`PopulateCandleData::populateTechnicalIndicators::Error in main process: ${error}`);
            throw error;
        }
    }

    private async processCategoryTechnicalIndicators(category: string): Promise<void> {
        const oThis = this;
        try {
            const candleModel = oThis.getCandleModelForNetwork();

            // First, get all tokens for the current category
            const allTokensForCategory = await oThis.tokenCategoryMapModel.getTokenIdsByCategory(category);

            // Then filter to only include tokens that have candle data
            const tokensWithCandles = await candleModel.getExistingTokenIds();

            // Find the intersection of both arrays
            const tokenIds = allTokensForCategory.filter((id: string) => tokensWithCandles.includes(id));
            Logger.debug(`PopulateCandleData::processCategoryTechnicalIndicators::Filtered tokens for category ${category}: ${tokenIds.length}`);

            const batchSize = AIAgentConstant.technicalIndicatorsBatchSize;

            await oThis.processTechnicalIndicatorsBatch(tokenIds, candleModel, batchSize);
        } catch (error) {
            Logger.error(`PopulateCandleData::processCategoryTechnicalIndicators::Error processing category ${category}: ${error}`);
            throw error;
        }
    }

    private async processTechnicalIndicatorsBatch(
        tokenIds: string[],
        candleModel: TokenCandleData | SolanaCandleData | EthereumCandleData,
        batchSize: number
    ): Promise<void> {
        const oThis = this;

        for (let i = 0; i < tokenIds.length; i += batchSize) {
            try {
                const batchTokenIds = tokenIds.slice(i, i + batchSize);
                Logger.debug(`PopulateCandleData::processTechnicalIndicatorsBatch::Processing batch ${Math.floor(i / batchSize) + 1} with ${batchTokenIds.length} tokens`);

                await Promise.all(batchTokenIds.map(async (tokenId: string) => {
                    try {
                        const candleData = await candleModel.getLastNCandlesByTokenId(
                            tokenId,
                            AIAgentConstant.technicalIndicatorsPeriod.SMA
                        );

                        if (!candleData || candleData.length === 0) {
                            Logger.info(`PopulateCandleData::processTechnicalIndicatorsBatch::No candle data for token ${tokenId}`);
                            return;
                        }

                        const closes = candleData.map((candle: any) => parseFloat(candle.close));
                        const highs = candleData.map((candle: any) => parseFloat(candle.high));
                        const lows = candleData.map((candle: any) => parseFloat(candle.low));

                        const technicalData = await TechnicalIndicators.calculateIndicators(closes, highs, lows);
                        await oThis.updateIndicators(tokenId, technicalData);
                    } catch (error) {
                        Logger.error(`PopulateCandleData::processTechnicalIndicatorsBatch::Error processing token ${tokenId}: ${error}`);
                    }
                }));
                await BasicHelper.sleep(100);
            } catch (error) {
                Logger.error(`PopulateCandleData::processTechnicalIndicatorsBatch::Error in batch: ${error}`);
            }
        }
    }

    private async saveCandleData(
        tokenId: string,
        candleData: any,
        candleModel: TokenCandleData | SolanaCandleData | EthereumCandleData
    ): Promise<void> {
        const tokenCandleDataObjects = candleData.map((data: any) => ({
            tokenId: tokenId,
            timestamp: new Date(data[0] * 1000).toISOString(),
            open: data[1],
            high: data[2],
            low: data[3],
            close: data[4],
            volume: data[5]
        }));
        await candleModel.bulkCreate(tokenCandleDataObjects);
    }

    private async updateLastCandleData(
        tokenId: string,
        timestamp: Date,
        candleData: any,
        candleModel: TokenCandleData | SolanaCandleData | EthereumCandleData
    ): Promise<void> {
        const oThis = this;

        if (!candleData || !Array.isArray(candleData) || candleData.length < 6) {
            Logger.error(`PopulateCandleData::updateLastCandleData::No token detail found in last ${oThis.limit} hours for ${tokenId}`);
            return;
        }

        Logger.debug(`PopulateCandleData::updateLastCandleData::Updating last candle data for token ${tokenId}: ${JSON.stringify(candleData)}`);

        await candleModel.update(
            {
                open: candleData[1],
                high: candleData[2],
                low: candleData[3],
                close: candleData[4],
                volume: candleData[5]
            },
            {
                tokenId: tokenId,
                timestamp: timestamp
            }
        );
    }

    private async updateIndicators(tokenId: string, technicalData: {
        rsi: number | null,
        sma: number | null,
        support: number | null,
        resistance: number | null
    }): Promise<void> {
        const oThis = this;
        try {
            const updateResult = await oThis.tokenDetailsModel.update(
                {
                    rsi: technicalData.rsi,
                    sma: technicalData.sma,
                    support: technicalData.support,
                    resistance: technicalData.resistance
                },
                {
                    token_id: tokenId
                }
            );

            if (!updateResult || !updateResult[0]) {
                Logger.error(`PopulateCandleData::updateIndicators::No rows updated for token ${tokenId}`);
            } else {
                Logger.debug(`PopulateCandleData::updateIndicators::Updated token ${tokenId} with technical data: ${JSON.stringify(technicalData)}`);
            }
            if (oThis.network === AIAgentConstant.network.BASE || oThis.network === AIAgentConstant.network.ETH) {
                oThis.updatedVirtualTokenIds.push(tokenId);
            }
        } catch (error: any) {
            Logger.error(`PopulateCandleData::updateIndicators::Error updating token ${tokenId}: ${error}`);
            throw error;
        }
    }

    private async cleanupOlderData() {
        const oThis = this;
        const olderThenDate = moment().subtract(15, 'days').toDate();
        Logger.info(`PopulateCandleData::cleanupOlderData::Deleting records older then: ${olderThenDate}`);
        await oThis.tokenCandleDataModel.delete({
            timestamp: {
                [Op.lt]: olderThenDate
            }
        });
        await oThis.solanaCandleDataModel.delete({
            timestamp: {
                [Op.lt]: olderThenDate
            }
        });
        await oThis.ethereumCandleDataModel.delete({
            timestamp: {
                [Op.lt]: olderThenDate
            }
        });
    }

    private async flushCache() {
        const oThis = this;

        if (oThis.updatedVirtualTokenIds.length === 0) {
            return;
        }

        const tokenDetailsByTokenIds = await new VirtualTokenDetailsByTokenIds({
            tokenIds: oThis.updatedVirtualTokenIds
        }).fetch();

        Logger.info(`PopulateCandleData::flushCache::tokenDetailsByTokenIds::${JSON.stringify(tokenDetailsByTokenIds.data)}`);
        if (!tokenDetailsByTokenIds.success) {
            return oThis.unauthorizedResponse('invalidParams', 's_s_pcd_fc_0');
        }
        const tokenDetails = tokenDetailsByTokenIds.data;
        const tokenAddresses = oThis.updatedVirtualTokenIds
            .map(tokenId => tokenDetails[tokenId]?.token_address)
            .filter((address): address is string => address != null);
        Logger.info(`PopulateCandleData::flushCache::Flushing token cache | tokenAddresses :: ${JSON.stringify(tokenAddresses)} | length :: ${tokenAddresses?.length}`);
        await (new TokenDetailsByTokenAddresses({ tokenAddresses: tokenAddresses, category: AIAgentConstant.categoryVirtualEcosystem })).clear();
        await (new VirtualTokenDetailsByTokenAddresses({ tokenAddresses: tokenAddresses })).clear();

        Logger.info(`PopulateCandleData::flushCache::Flushing token cache | tokenIds :: ${JSON.stringify(oThis.updatedVirtualTokenIds)} | length :: ${oThis.updatedVirtualTokenIds?.length}`);
        await (new VirtualTokenDetailsByTokenIds({ tokenIds: oThis.updatedVirtualTokenIds })).clear();
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`PopulateCandleData::prepareResponse::Service execution completed`);
        return ResponseHelper.success({});
    }
}
