import Logger from '../lib/Logger';
import ResponseHelper from '../lib/helper/ResponseHelper';
import { SuccessResponse, ErrorResponse, UserModelAttributes, LQAHistoricalMetricModelAttributes } from '../lib/Types';
import ServiceBase from "./Base";
import AIAgentConstant from "../lib/constant/AIAgentConstant";
import Postgres from '../lib/Postgres';
import Utils from '../lib/Utils';
import moment from 'moment';
import LQAHelper from './lqa/LQAHelper';
import { Op } from 'sequelize';
import VirtualTokenDetailsByTokenAddresses from '../lib/cache/VirtualTokenDetailsByTokenAddresses';
import GetBubbleMapService from './GetBubbleMapService';


export default class GetAgentDegenService extends ServiceBase {
    private tokenAddress: string;
    private tokenDetails: any;
    private currentUser: UserModelAttributes;

    // Format variables with any type
    private id: any;
    private watchlistedTokenIds: Set<string>;
    private watchlist: boolean;
    private agentInfo: any;
    private socials: any;
    private devBundleWalletInsights: any;
    private agentMetrics: any;
    private devWalletOtherAgents: any;
    private devBundleWalletMap: any;
    private top25Holders: any;
    private lastUpdatedAt: any;
    private result: any;

    constructor(params: { tokenAddress: string, internalDecodedParams: any }) {
        super(params);
        const oThis = this;
        oThis.tokenAddress = params.tokenAddress.toLowerCase();
        oThis.currentUser = params.internalDecodedParams.currentUser! || {};
        oThis.watchlistedTokenIds = new Set();
    }

    private async validateParams(): Promise<void | ErrorResponse> {
        const oThis = this;
        if (!oThis.tokenAddress) {
            return oThis.unauthorizedResponse('invalidParams', 's_s_gat_fat_0');
        }

        // Validate watchlist param requires user to be logged in
        if (oThis.watchlist && !oThis.currentUser.id) {
            return oThis.unauthorizedResponse('userNotLoggedIn', 's_s_gat_vp_3');
        }
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;

        await oThis.validateParams();

        if (oThis.currentUser.id) {
            await oThis.fetchWatchlistedTokens();
        }

        // Get token details from token_details table by token address
        await oThis.fetchTokenDetails();

        // Then format the rest of the data
        await Promise.all([
            oThis.formatAgentInfo(),
            oThis.formatSocials(),
            oThis.formatDevBundleWalletInsights(),
            oThis.formatAgentMetrics(),
            oThis.formatDevWalletOtherAgents(),
        ]);

        // Get wallet map
        Logger.debug(`GetAgentDegen::servicePerform::Getting wallet map for ${oThis.tokenAddress}`);
        const { devBundleWalletMap, top25Holders } = await new GetBubbleMapService({} as any).getWalletMap(oThis.tokenAddress);

        oThis.devBundleWalletMap = devBundleWalletMap;
        oThis.top25Holders = top25Holders;
        Logger.debug(`GetAgentDegen::servicePerform::Wallet map fetched for ${oThis.tokenAddress}`);

        if (Utils.isObjectEmpty(oThis.tokenDetails)) {
            oThis.result = {};
        } else {
            oThis.result = {
                id: oThis.id,
                rugStatus: AIAgentConstant.rugStatus[oThis.tokenDetails.rug_status],
                watchListed: oThis.watchlistedTokenIds.has(oThis.tokenDetails.token_id),
                agentInfo: oThis.agentInfo,
                socials: oThis.socials,
                devBundleWalletInsights: oThis.devBundleWalletInsights,
                agentMetrics: oThis.agentMetrics,
                devWalletOtherAgents: oThis.devWalletOtherAgents,
                devBundleWalletMap: oThis.devBundleWalletMap,
                top25Holders: oThis.top25Holders,
                lastUpdatedAt: oThis.lastUpdatedAt
            }
        }

        return oThis.prepareResponse();
    }

    private async fetchTokenDetails() {
        const oThis = this;
        Logger.info(`GetAgentDegen::fetchTokenDetails::Fetching token details for address: ${oThis.tokenAddress}`);

        const tokenDetailsByAddresses = await new VirtualTokenDetailsByTokenAddresses({
            tokenAddresses: [oThis.tokenAddress]
        }).fetch();

        Logger.info(`GetAgentDegen::fetchTokenDetails::Token details response: ${JSON.stringify(tokenDetailsByAddresses)}`);

        if (!tokenDetailsByAddresses.success) {
            oThis.result = null;
            return;
        }

        oThis.tokenDetails = tokenDetailsByAddresses.data[oThis.tokenAddress];
    }

    private async fetchWatchlistedTokens() {
        const oThis = this;
        if (!oThis.currentUser.id) return;

        try {
            const dbModels = await Postgres.getDbModels();
            const userWatchlistModel = dbModels.userWatchlist;

            const watchlistedTokenIds = await userWatchlistModel.getWatchlistedTokenIdsByUserId(oThis.currentUser.id);
            oThis.watchlistedTokenIds = new Set(watchlistedTokenIds);
            Logger.info(`GetAgentTokens::fetchWatchlistedTokens:: Found ${oThis.watchlistedTokenIds.size} watchlisted tokens for user ${oThis.currentUser.id}`);
        } catch (error) {
            Logger.error(`GetAgentTokens::fetchWatchlistedTokens:: Error fetching watchlisted tokens: ${JSON.stringify(error)}`);
        }
    }

    private async formatAgentInfo(): Promise<any> {
        const oThis = this;
        oThis.agentInfo = {
            id: oThis.tokenDetails.token_id,
            symbol: oThis.tokenDetails.token,
            name: oThis.tokenDetails.token_name,
            ecosystem: oThis.tokenDetails.category,
            handle: oThis.tokenDetails.handle?.trim() === "" ? null : oThis.tokenDetails.handle,
            description: oThis.tokenDetails.description?.trim() === "" ? null : oThis.tokenDetails.description,
            ca: oThis.tokenDetails.token_address,
            network: AIAgentConstant.networkMap[oThis.tokenDetails.network],
            launchedAt: oThis.tokenDetails.launch_date ? oThis.tokenDetails.launch_date : null,
            image: oThis.tokenDetails.image?.trim() === "" ? null : oThis.tokenDetails.image
        };
    }

    private async formatSocials(): Promise<any> {
        const oThis = this;
        oThis.socials = {
            website: null,
            x: oThis.tokenDetails.handle?.trim() === "" ? null : oThis.tokenDetails.handle,
            telegram: null
        };
    }

    private async formatDevBundleWalletInsights(): Promise<any> {
        const oThis = this;
        const hasValidCirculatingSupply = oThis.tokenDetails.circulating_supply && oThis.tokenDetails.circulating_supply > 0;
        const hasValidTotalSupply = oThis.tokenDetails.total_supply && oThis.tokenDetails.total_supply > 0;

        let denominator = 1_000_000_000; // Default fallback value
        if (hasValidCirculatingSupply) {
            denominator = oThis.tokenDetails.circulating_supply;
        } else if (hasValidTotalSupply) {
            denominator = oThis.tokenDetails.total_supply;
        }

        const devBundlePurchasePercentage = (oThis.tokenDetails.dev_total_buy / denominator) * 100;
        oThis.devBundleWalletInsights = {
            devWalletAddress: oThis.tokenDetails.dev_wallet?.trim() === "" ? null : oThis.tokenDetails.dev_wallet,
            devWalletAge: oThis.tokenDetails.dev_wallet_funded_on ? Utils.getAgeInDays(oThis.tokenDetails.dev_wallet_funded_on) + " days" : null,
            devWalletTotalHoldingPercentage: oThis.tokenDetails.dev_wallet_total_holding_percentage ? Utils.millify(oThis.tokenDetails.dev_wallet_total_holding_percentage, Utils.getPrecision(oThis.tokenDetails.dev_wallet_total_holding_percentage)) : null,
            devBundleWalletOutflow: oThis.tokenDetails.bundle_wallet_outflow ? Utils.millify(oThis.tokenDetails.bundle_wallet_outflow, Utils.getPrecision(oThis.tokenDetails.bundle_wallet_outflow)) : null,
            devBundleWalletInflow: oThis.tokenDetails.bundle_wallet_inflow ? Utils.millify(oThis.tokenDetails.bundle_wallet_inflow, Utils.getPrecision(oThis.tokenDetails.bundle_wallet_inflow)) : null,
            devBundleNetflow: oThis.tokenDetails.bundle_wallet_netflow ? Utils.millify(oThis.tokenDetails.bundle_wallet_netflow, Utils.getPrecision(oThis.tokenDetails.bundle_wallet_netflow)) : null,
            devWalletBalanceEth: oThis.tokenDetails.dev_wallet_balance_eth ? Utils.millify(oThis.tokenDetails.dev_wallet_balance_eth, Utils.getPrecision(oThis.tokenDetails.dev_wallet_balance_eth)) : null,
            devPurchaseAmount: oThis.tokenDetails.dev_total_buy ? Utils.millify(oThis.tokenDetails.dev_total_buy, Utils.getPrecision(oThis.tokenDetails.dev_total_buy)) : null,
            devPurchaseAmountPercentage: devBundlePurchasePercentage ? Utils.millify(devBundlePurchasePercentage, Utils.getPrecision(devBundlePurchasePercentage)) : null,
            note: "A sample note" // TODO: Add note
        };
    }

    private async formatAgentMetrics(): Promise<any> {
        Logger.debug(`GetAgentDegen::formatAgentMetrics::Formatting agent metrics`);
        const oThis = this;
        const lqaHistoricalMetricModel = (await Postgres.getDbModels()).lqaHistoricalMetric;
        const lqaMetricsByTokenId = await lqaHistoricalMetricModel.getByTokenIds([oThis.tokenDetails.token_id]);
        const lqaMetric = lqaMetricsByTokenId[oThis.tokenDetails.token_id];
        const reasoningSummary = await oThis.getReasoningSummary(lqaMetric);

        const usdPrice = oThis.tokenDetails.usd_price ? Utils.millify(oThis.tokenDetails.usd_price, Utils.getPrecision(oThis.tokenDetails.usd_price)) : null;
        const mcap = oThis.tokenDetails.mcap ? Utils.millify(oThis.tokenDetails.mcap, Utils.getPrecision(oThis.tokenDetails.mcap)) : null;
        const liquidity = oThis.tokenDetails.total_liquidity ? Utils.millify(oThis.tokenDetails.total_liquidity, Utils.getPrecision(oThis.tokenDetails.total_liquidity)) : null;
        const volume24h = oThis.tokenDetails.volume_24h ? Utils.millify(oThis.tokenDetails.volume_24h, Utils.getPrecision(oThis.tokenDetails.volume_24h)) : null;
        const mcapChangePercentage24h = oThis.tokenDetails.mcap_change_percentage_24h ? Utils.millify(oThis.tokenDetails.mcap_change_percentage_24h, Utils.getPrecision(oThis.tokenDetails.mcap_change_percentage_24h)) : null;
        const priceChangePercentage24h = oThis.tokenDetails.price_change_percentage_24h ? Utils.millify(oThis.tokenDetails.price_change_percentage_24h, Utils.getPrecision(oThis.tokenDetails.price_change_percentage_24h)) : null;
        const totalSupply = oThis.tokenDetails.total_supply ? Utils.millify(oThis.tokenDetails.total_supply, Utils.getPrecision(oThis.tokenDetails.total_supply)) : null;
        const totalBurn = oThis.tokenDetails.token_burn_amount ? Utils.millify(oThis.tokenDetails.token_burn_amount, Utils.getPrecision(oThis.tokenDetails.token_burn_amount)) : null;
        const confidence = lqaMetric?.confidence_15m ? Utils.millify(lqaMetric.confidence_15m, Utils.getPrecision(lqaMetric.confidence_15m)) : null;
        const fiftyPercentageHoldingWalletCount = oThis.tokenDetails.fifty_percentage_holding_wallet_count ? Utils.millify(oThis.tokenDetails.fifty_percentage_holding_wallet_count, Utils.getPrecision(oThis.tokenDetails.fifty_percentage_holding_wallet_count)) : null;
        const totalHolderCount = oThis.tokenDetails.total_holder_count ? Utils.millify(oThis.tokenDetails.total_holder_count, Utils.getPrecision(oThis.tokenDetails.total_holder_count)) : null;
        const holderCountChangePercentage24h = oThis.tokenDetails.holder_count_change_percentage_24h ? Utils.millify(oThis.tokenDetails.holder_count_change_percentage_24h, Utils.getPrecision(oThis.tokenDetails.holder_count_change_percentage_24h)) : null;

        oThis.agentMetrics = {
            usdPrice: usdPrice,
            mcap: mcap,
            liquidity: liquidity,
            volume24h: volume24h,
            mcapChangePercentage24h: mcapChangePercentage24h,
            priceChangePercentage24h: priceChangePercentage24h,
            totalSupply: totalSupply,
            totalBurn: totalBurn,
            confidence: confidence,
            agentHealthReasoning: reasoningSummary?.trim() === "" ? null : reasoningSummary,
            fiftyPercentageHoldingWalletCount: fiftyPercentageHoldingWalletCount,
            totalHolderCount: totalHolderCount,
            holderCountChangePercentage24h: holderCountChangePercentage24h
        };
        Logger.debug(`GetAgentDegen::formatAgentMetrics::Formatted agent metrics completed`);
    }

    private async getReasoningSummary(lqaMetric: LQAHistoricalMetricModelAttributes): Promise<string> {
        const oThis = this;
        const now = moment();

        if (!lqaMetric) {
            Logger.error(`GetAgentDegen::getReasoningSummary::No LQA metric found for token ${oThis.tokenDetails.token_id}`);
            return '';
        }

        const shouldGenerate = !lqaMetric.reasoning_summary || now.diff(moment(lqaMetric.summary_created_at), 'hours') >= 24;
        if (shouldGenerate) {
            Logger.info(`GetAgentDegen::getReasoningSummary::Generating reasoning summary for token ${oThis.tokenDetails.token_id}`);
            const rawMetrics = {
                lqa_confidence_percentage: lqaMetric.confidence_24h ? Utils.millify(lqaMetric.confidence_24h, Utils.getPrecision(lqaMetric.confidence_24h)) : null,
                price_change_percentage: oThis.tokenDetails.price_change_percentage_24h ? Utils.millify(oThis.tokenDetails.price_change_percentage_24h, Utils.getPrecision(oThis.tokenDetails.price_change_percentage_24h)) : null,
                market_cap: oThis.tokenDetails.mcap ? Utils.millify(oThis.tokenDetails.mcap, Utils.getPrecision(oThis.tokenDetails.mcap)) : null,
                volume: oThis.tokenDetails.volume_24h ? Utils.millify(oThis.tokenDetails.volume_24h, Utils.getPrecision(oThis.tokenDetails.volume_24h)) : null,
                holder_change_percentage: oThis.tokenDetails.holder_count_change_percentage_24h ? Utils.millify(oThis.tokenDetails.holder_count_change_percentage_24h, Utils.getPrecision(oThis.tokenDetails.holder_count_change_percentage_24h)) : null,
                dev_wallet_supply_percentage: oThis.tokenDetails.bundle_wallet_supply_percentage ? Utils.millify(oThis.tokenDetails.bundle_wallet_supply_percentage, Utils.getPrecision(oThis.tokenDetails.bundle_wallet_supply_percentage)) : null,
                mindshare_change: oThis.tokenDetails.mindshare_change ? Utils.millify(oThis.tokenDetails.mindshare_change, Utils.getPrecision(oThis.tokenDetails.mindshare_change)) : null
            };

            const dataPoints: any = {};
            for (const [key, value] of Object.entries(rawMetrics)) {
                if (value !== undefined && value !== null) {
                    const num = Number(value);
                    const validValue = isNaN(num) ? null : num;
                    if (validValue) {
                        dataPoints[key] = Utils.millify(validValue, Utils.getPrecision(validValue));
                    }
                }
            }
            const newSummary = await new LQAHelper().generateReasoningSummary(JSON.stringify(dataPoints));
            if (newSummary) {
                const lqaHistoricalMetricModel = (await Postgres.getDbModels()).lqaHistoricalMetric;
                const currentDate = moment().toISOString();
                const before24Hours = moment().subtract(24, 'hours').toISOString();
                await lqaHistoricalMetricModel.update(
                    {
                        reasoningSummary: newSummary,
                        summaryCreatedAt: currentDate
                    },
                    {
                        tokenId: oThis.tokenDetails.token_id,
                        [Op.or]: [
                            { summaryCreatedAt: { [Op.lte]: before24Hours } },
                            { summaryCreatedAt: null }
                        ]
                    }
                );
                return newSummary;
            }
        }
        return lqaMetric.reasoning_summary;
    }

    private async formatDevWalletOtherAgents(): Promise<any> {
        const oThis = this;
        Logger.info(`GetAgentDegen::formatDevWalletOtherAgents::Formatting dev wallet other agents`);
        if (!oThis.tokenDetails?.dev_wallet) {
            oThis.devWalletOtherAgents = [];
            return;
        }

        try {
            const tokenDetailsModel = (await Postgres.getDbModels()).tokenDetails;
            const otherTokens = await tokenDetailsModel.getTokensByDevWallet(oThis.tokenDetails.dev_wallet);

            // Filter out the current token and format the response
            oThis.devWalletOtherAgents = otherTokens
                .filter((token: any) => token.token_address.toLowerCase() !== oThis.tokenAddress.toLowerCase())
                .map((token: any) => ({
                    name: token.token_name,
                    ca: token.token_address,
                    status: token.rug_status
                }));

            Logger.debug(`GetAgentDegen::formatDevWalletOtherAgents::otherAgents:: ${JSON.stringify(oThis.devWalletOtherAgents)}`);
        } catch (error: any) {
            Logger.error(`GetAgentDegen::formatDevWalletOtherAgents::Error:: ${JSON.stringify(error)}`);
            oThis.devWalletOtherAgents = [];
        }
    }

    private prepareResponse(): SuccessResponse {
        const oThis = this;
        Logger.info(`GetLokyDetails:: prepareResponse::Service execution completed`);
        return ResponseHelper.success(oThis.result);
    }
}
