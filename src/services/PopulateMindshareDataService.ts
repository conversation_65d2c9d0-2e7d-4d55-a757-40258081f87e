import ResponseHelper from "../lib/helper/ResponseHelper";
import Logger from "../lib/Logger";
import Postgres from "../lib/Postgres";
import { ErrorResponse, SuccessResponse } from "../lib/Types";
import ServiceBase from "./Base";
import TokenAddressMap from "../models/TokenAddressMap";
import AIAgentConstant from "../lib/constant/AIAgentConstant";
import MindshareData from "../models/MindshareData";
import TokenDetails from "../models/TokenDetails";
import MarketStatistics from "../models/MarketStatistics";
import BrowserRequestService from "../lib/BrowserRequestService";
import VirtualTokenDetailsByTokenIds from "../lib/cache/VirtualTokenDetailsByTokenIds";
import TokenDetailsByTokenIdsAndCategory from "../lib/cache/TokenDetailsByTokenIdsAndCategory";
import TokenDetailsByTokenIdsAndEcosystemCategories from "../lib/cache/TokenDetailsByTokenIdsAndEcosystemCategories";
import TokenDetailsByTokenAddresses from "../lib/cache/TokenDetailsByTokenAddresses";
import TokenDetailsByTicker from "../lib/cache/TokenDetailsByTickers";
import VirtualTokenDetailsByTokenAddresses from "../lib/cache/VirtualTokenDetailsByTokenAddresses";
import MindshareDataByTokenIds from "../lib/cache/MindshareDataByTokenIds";

interface MindshareItem {
    slug: string;
    chainId: number;
    address: string;
    data: any;
}

type Period = typeof AIAgentConstant.mindshareDataPeriods[keyof typeof AIAgentConstant.mindshareDataPeriods];

export default class PopulateMindshareDataService extends ServiceBase {
    private mindshareDataModel: MindshareData;
    private tokenAddressMapModel: TokenAddressMap;
    private tokenDetailsModel: TokenDetails;
    private marketStatisticsModel: MarketStatistics;
    private readonly BATCH_LIMIT = 100;
    private browserService: BrowserRequestService;
    private updatedTokenIds: string[] = [];

    constructor(params: any) {
        super(params);
        this.browserService = BrowserRequestService.getInstance();
    }

    public async servicePerform(): Promise<any | ErrorResponse> {
        const oThis = this;
        const dbModels = await Postgres.getDbModels();
        oThis.mindshareDataModel = dbModels.mindshareData;
        oThis.tokenAddressMapModel = dbModels.tokenAddressMap;
        oThis.tokenDetailsModel = dbModels.tokenDetails;
        oThis.marketStatisticsModel = dbModels.marketStatistics;

        await oThis.getTopTokensMindshareDataByCategory(AIAgentConstant.tokenLimitForFetchMindshareData, AIAgentConstant.mindshareDataPeriods[AIAgentConstant.mindshareDataTimeFrames.SEVEN_DAYS], AIAgentConstant.categoryBaseEcosystem)
        await oThis.getTopTokensMindshareDataByCategory(AIAgentConstant.tokenLimitForFetchMindshareData, AIAgentConstant.mindshareDataPeriods[AIAgentConstant.mindshareDataTimeFrames.THREE_DAYS], AIAgentConstant.categoryBaseEcosystem)

        await oThis.getMindshareDataForPeriod(AIAgentConstant.mindshareDataPeriods[AIAgentConstant.mindshareDataTimeFrames.SEVEN_DAYS]);
        await oThis.getMindshareDataForPeriod(AIAgentConstant.mindshareDataPeriods[AIAgentConstant.mindshareDataTimeFrames.THREE_DAYS]);

        await oThis.updateTopTenAgents(AIAgentConstant.topTenAgentsMetrics.MINDSHARE);
        await oThis.updateTopTenAgents(AIAgentConstant.topTenAgentsMetrics.MARKET_CAP);

        await oThis.flushCache();

        await oThis.browserService.cleanup();

        return oThis.prepareResponse();
    }

    private async getMindshareDataForPeriod(period: Period): Promise<any> {
        const oThis = this;
        try {
            const existingSlugsMap = await oThis.mindshareDataModel.getExistingSlugsMap();
            Logger.info(`PopulateMindshareDataService::getMindshareDataForPeriod::Found ${existingSlugsMap.size} existing slugs in database for ${period}`);

            const firstPageResponse = await oThis.getMindshareData({ page: 1, limit: oThis.BATCH_LIMIT, period });
            const firstPageData = JSON.parse(firstPageResponse)[0].result.data.json;
            const totalPages = firstPageData.totalPages;
            Logger.debug(`PopulateMindshareDataService::getMindshareDataForPeriod::Total pages to process for ${period}: ${totalPages}`);

            for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
                await oThis.processPage(currentPage, totalPages, period, existingSlugsMap, firstPageData);
            }

            Logger.info(`PopulateMindshareDataService::getMindshareDataForPeriod::Completed processing ${totalPages} pages of mindshare data for ${period}`);
        } catch (e: any) {
            Logger.error(`PopulateMindshareDataService::getMindshareDataForPeriod::Error getting mindshare data for ${period}. Exception: ${e.message}`);
            throw e;
        }
    }

    private async processPage(
        currentPage: number,
        totalPages: number,
        period: Period,
        existingSlugsMap: Map<string, string>,
        firstPageData: any
    ): Promise<void> {
        const oThis = this;
        Logger.debug(`PopulateMindshareDataService::processPage::Processing page ${currentPage} of ${totalPages} for ${period}`);

        const pageData = currentPage === 1
            ? firstPageData
            : JSON.parse(await oThis.getMindshareData({
                page: currentPage,
                limit: oThis.BATCH_LIMIT,
                period
            }))[0].result.data.json;

        const transformedData = oThis.transformMindshareData(pageData.projects, period);
        await oThis.processMindshareItems(transformedData, existingSlugsMap, period);
    }

    private async processMindshareItems(
        items: MindshareItem[],
        existingSlugsMap: Map<string, string>,
        period: Period
    ): Promise<void> {
        const oThis = this;
        const { recordsToUpdate, recordsToCreate } = await oThis.categorizeRecords(items, existingSlugsMap, period);

        await oThis.processUpdates(recordsToUpdate, existingSlugsMap, period);
        await oThis.processNewRecords(recordsToCreate, period);
    }

    private async categorizeRecords(items: MindshareItem[], existingSlugsMap: Map<string, string>, period: Period) {
        const oThis = this;
        const recordsToUpdate: Array<{ item: MindshareItem; slug: string }> = [];
        const recordsToCreate: any[] = [];

        for (const item of items) {
            if (existingSlugsMap.get(item.slug)) {
                recordsToUpdate.push({ item, slug: item.slug });
            } else {
                const record = await oThis.prepareNewRecord(item, existingSlugsMap, period);
                if (record) recordsToCreate.push(record);
            }
        }

        return { recordsToUpdate, recordsToCreate };
    }

    private async prepareNewRecord(item: MindshareItem, existingSlugsMap: Map<string, string>, period: Period) {
        const networkId = AIAgentConstant.cookieFunChainIdToNetworkMap[item.chainId];
        if (!networkId) {
            Logger.debug(`PopulateMindshareDataService::prepareNewRecord::Unknown chain ID: ${item.chainId} for slug: ${item.slug}`);
            return null;
        }

        const tokenAddressMap = await this.tokenAddressMapModel.getByTokenAddressAndNetwork(
            item.address,
            networkId
        );

        if (!tokenAddressMap) {
            Logger.debug(`PopulateMindshareDataService::prepareNewRecord::No token address mapping found for address ${item.address} and network ${networkId}`);
            return null;
        }

        existingSlugsMap.set(item.slug, tokenAddressMap.tokenId);

        const extraDataField = `extra_data_${period === AIAgentConstant.mindshareDataPeriods[AIAgentConstant.mindshareDataTimeFrames.SEVEN_DAYS]
            ? AIAgentConstant.mindshareDataTimeFrames.SEVEN_DAYS
            : AIAgentConstant.mindshareDataTimeFrames.THREE_DAYS}` as const;
        return {
            tokenId: tokenAddressMap.tokenId,
            slug: item.slug,
            network: AIAgentConstant.network[networkId],
            [extraDataField]: item.data
        };
    }

    private async processUpdates(
        records: Array<{ item: MindshareItem; slug: string }>,
        existingSlugsMap: Map<string, string>,
        period: Period
    ): Promise<void> {
        const oThis = this;
        for (const record of records) {
            if (!record.item.data) {
                Logger.debug(`PopulateMindshareDataService::processUpdates::No data found for record: ${JSON.stringify(record)}`);
                continue;
            }

            const fieldSuffix = period === AIAgentConstant.mindshareDataPeriods[AIAgentConstant.mindshareDataTimeFrames.SEVEN_DAYS] ? AIAgentConstant.mindshareDataTimeFrames.SEVEN_DAYS : AIAgentConstant.mindshareDataTimeFrames.THREE_DAYS;
            await oThis.updateExistingRecord(record, fieldSuffix, existingSlugsMap);
        }
    }

    private async updateExistingRecord(
        record: { item: MindshareItem; slug: string },
        fieldSuffix: string,
        existingSlugsMap: Map<string, string>
    ): Promise<void> {
        await this.mindshareDataModel.update(
            { [`extra_data_${fieldSuffix}`]: record.item.data },
            { slug: record.item.slug }
        );
        Logger.debug(`PopulateMindshareDataService::updateExistingRecord::Updated existing mindshare data record: ${JSON.stringify(record)}`);

        const tokenId = existingSlugsMap.get(record.item.slug);
        await this.tokenDetailsModel.update(
            { [`mindshare_${fieldSuffix}`]: record.item.data.mindshare.value },
            { token_id: tokenId }
        );
        Logger.debug(`PopulateMindshareDataService::updateExistingRecord::Updated token details record: ${JSON.stringify(record)}`);

        if (tokenId) {
            this.updatedTokenIds.push(tokenId);
        }
    }

    private async processNewRecords(records: any[], period: Period): Promise<void> {
        const oThis = this;
        if (records.length === 0) return;

        Logger.debug(`PopulateMindshareDataService::processNewRecords::Bulk creating ${records.length} new records`);
        await oThis.mindshareDataModel.bulkCreate(records);

        for (const record of records) {
            if (!record.tokenId) {
                Logger.debug(`PopulateMindshareDataService::processNewRecords::No token ID found for record: ${JSON.stringify(record)}`);
                continue;
            }

            const fieldSuffix = period === AIAgentConstant.mindshareDataPeriods[AIAgentConstant.mindshareDataTimeFrames.SEVEN_DAYS] ? AIAgentConstant.mindshareDataTimeFrames.SEVEN_DAYS : AIAgentConstant.mindshareDataTimeFrames.THREE_DAYS;
            const data = record[`extra_data_${fieldSuffix}`];
            if (!data?.mindshare?.value) continue;

            await oThis.tokenDetailsModel.update(
                { [`mindshare_${fieldSuffix}`]: data.mindshare.value },
                { token_id: record.tokenId }
            );
            Logger.debug(`PopulateMindshareDataService::processNewRecords::Updated token details record: ${JSON.stringify(record)}`);

            oThis.updatedTokenIds.push(record.tokenId);
        }
    }

    private async getMindshareData(params: {
        page: number;
        limit: number;
        period: Period;
        orderColumn?: string;
        orderByAscending?: boolean;
    }): Promise<any> {
        const oThis = this;
        const defaultPayload = {
            page: params.page,
            limit: params.limit,
            orderDataPoint: params.period,
            orderColumn: params.orderColumn || "TokenPriceDelta",
            orderByAscending: params.orderByAscending ?? false,
            tags: [],
            projectsFilter: {},
            isWatchlist: false
        };

        try {
            const url = `https://agents.cookie.fun/api/trpc/agents.getAgentsTableDetails?batch=1&input=${encodeURIComponent(JSON.stringify({ "0": { json: defaultPayload } }))}`;

            const result = await oThis.browserService.makeRequest(url, {
                waitForResponse: 'agents.getAgentsTableDetails',
                timeout: 30000 // 30 seconds
            });

            return result;
        } catch (error) {
            Logger.error('PopulateMindshareDataService::getMindshareData::Error fetching data:', error);
            throw error;
        }
    }

    private transformMindshareData(mindshareData: any[], period: Period): MindshareItem[] {
        return mindshareData.map((item: any) => ({
            slug: item.agentDetails.slug,
            chainId: item.agentDetails.contracts[0].chain,
            address: item.agentDetails.contracts[0].contractAddress,
            data: item.twitterStats.dataPoints[period]
        }));
    }

    private async updateTopTenAgents(metric: string): Promise<void> {
        const oThis = this;
        const defaultPayload = {
            type: "all",
            chain: "all",
            metric: metric
        };

        try {
            const url = `https://agents.cookie.fun/api/trpc/agents.getTopAgentsDetails?batch=1&input=${encodeURIComponent(JSON.stringify({ "0": { json: defaultPayload } }))}`;
            const response = await oThis.browserService.makeRequest(url, {
                waitForResponse: 'agents.getTopAgentsDetails',
                timeout: 30000 // 30 seconds
            });

            const responseData = JSON.parse(response)[0].result.data.json.top10Agents;
            Logger.debug(`PopulateMindshareDataService::updateTopTenAgents::Response: ${JSON.stringify(responseData)}`);

            if (metric === AIAgentConstant.topTenAgentsMetrics.MINDSHARE) {
                await oThis.marketStatisticsModel.upsertMarketStatistics(AIAgentConstant.entityType.TOP_TEN_AGENTS_BY_MINDSHARE, responseData);
            } else if (metric === AIAgentConstant.topTenAgentsMetrics.MARKET_CAP) {
                await oThis.marketStatisticsModel.upsertMarketStatistics(AIAgentConstant.entityType.TOP_TEN_AGENTS_BY_MARKET_CAP, responseData);
            }
        } catch (error) {
            Logger.error(`PopulateMindshareDataService::updateTopTenAgents::Error updating market statistics for ${metric}:`, error);
            throw error;
        }
    }

    private async getTopTokensMindshareDataByCategory(limit: number = 500, period: Period, category: string): Promise<any> {
        const oThis  = this;
        
        let tokensMindshareData: MindshareItem[] = [];

        const existingSlugsMap = await oThis.mindshareDataModel.getExistingSlugsMap();
        Logger.info(`PopulateMindshareDataService::getTopTokensMindshareDataByNetwork::Found ${existingSlugsMap.size} existing slugs in database for ${period}`);

        const { tokens } = await oThis.tokenDetailsModel.getTopTokensByCategory(category, limit);
        
        for (const token of tokens) {
          const tokenMindshareData = await oThis.fetchMindshareDataByTokenAddress({tokenAddress: token.token_address, period});
          if(tokenMindshareData){
            Logger.debug(`PopulateMindshareDataService::fetchMindshareDataByTokenAddress::Mindshare data for tokenId ${token.token_id} and tokenAddress ${token.token_address}:: ${JSON.stringify(tokenMindshareData)}`);
            const transformedData: MindshareItem = oThis.transformTokenMindshareData(tokenMindshareData.id, token.network, token.token_address, tokenMindshareData)
            tokensMindshareData.push(transformedData)
          }else{
            Logger.debug(`PopulateMindshareDataService::fetchMindshareDataByTokenAddress::Mindshare data not found for tokenId ${token.token_id} and tokenAddress ${token.token_address}:: Skipping.`);
          }
        }

        await oThis.processMindshareItems(tokensMindshareData, existingSlugsMap, period)

    }

    private transformTokenMindshareData(slug: string, network: number, tokenAddress: string, mindshareData: any): MindshareItem {
        return {
            slug: slug,
            chainId: AIAgentConstant.cookieFunNetworkToChainIdMap[AIAgentConstant.networkMap[network].toUpperCase()],
            address: tokenAddress,
            data: mindshareData
        };
    }

    private async fetchMindshareDataByTokenAddress(params: {
        tokenAddress: string,
        period: Period,
        orderColumn?: string,
        orderByAscending?: string
    }): Promise<any> {
      try {
        Logger.info(`PopulateMindshareDataService::fetchMindshareDataByTokenAddress::Fetching mindshare data for token address ${params.tokenAddress}`);
        const payload = {
          '0': {
            json: {
              projectsFilter: {
                searchFilter: params.tokenAddress
              },
              orderColumn: params.orderColumn || 'TokenMarketCap',
              orderDataPoint: params.period,
              orderByAscending: params.orderByAscending || false,
              limit: 1,
              dataPoint: params.period
            }
          }
        };
        
        const encodedInput = encodeURIComponent(JSON.stringify(payload));
        const url = `https://www.cookie.fun/api/trpc/cookieFun.leaderboard?batch=1&input=${encodedInput}`;
        
        const response = await this.browserService.makeRequest(url, {
          waitForResponse: 'cookieFun.leaderboard',
          timeout: 30000 // 30 seconds
        });
  
        const data = JSON.parse(response);
  
        if (data && data[0]?.result?.data?.json?.[0]) {
          return data[0].result.data.json[0];
        }

        return null;
      } catch (error: any) {
        Logger.error(
          `PopulateMindshareDataService::fetchMindshareDataByTokenAddress::Error fetching Cookie data for token ${params.tokenAddress}: ${error?.message}`
        );
        return null;
      }
    }  

    private prepareResponse(): SuccessResponse {
        Logger.info('PopulateMindshareDataService::prepareResponse::Service execution completed');
        return ResponseHelper.success({});
    }

    /**
     * Flush cache for updated token IDs
     */
    private async flushCache(): Promise<void> {
        const oThis = this;

        const uniqueTokenIds = Array.from(new Set(oThis.updatedTokenIds));
        if (uniqueTokenIds.length === 0) {
            Logger.info(`PopulateMindshareDataService::flushCache::No token IDs to flush from cache`);
            return;
        }

        Logger.info(`PopulateMindshareDataService::flushCache::Flushing token cache | tokenIds :: ${JSON.stringify(uniqueTokenIds)} `);

        await (new VirtualTokenDetailsByTokenIds({ tokenIds: uniqueTokenIds })).clear();
        await (new TokenDetailsByTokenIdsAndCategory({
            tokenIds: uniqueTokenIds,
            categories: AIAgentConstant.publicEcosystemToCategoriesMap[AIAgentConstant.ecosystemVirtuals]
        })).clear();
        await (new TokenDetailsByTokenIdsAndEcosystemCategories({
            tokenIds: uniqueTokenIds,
            categories: AIAgentConstant.publicEcosystemToCategoriesMap[AIAgentConstant.ecosystemSolana]
        })).clear();

        await (new MindshareDataByTokenIds({ tokenIds: uniqueTokenIds })).clear();

        const tokensInfo = await oThis.tokenDetailsModel.getByTokenIds(uniqueTokenIds);

        const tokenAddresses = Object.values(tokensInfo)
            .map((token: any) => token.token_address?.toLowerCase())
            .filter((address: string | null | undefined): address is string => address != null);

        const tokenTickers = Object.values(tokensInfo)
            .map((token: any) => token.token?.toLowerCase())
            .filter((ticker: string | null | undefined): ticker is string => ticker != null);

        if (tokenAddresses.length > 0) {
            Logger.info(`PopulateMindshareDataService::flushCache::Flushing token cache | tokenAddresses :: ${JSON.stringify(tokenAddresses)} | length :: ${tokenAddresses.length} `);
            await (new TokenDetailsByTokenAddresses({
                tokenAddresses: tokenAddresses,
                category: AIAgentConstant.categoryVirtualEcosystem
            })).clear();
            await (new VirtualTokenDetailsByTokenAddresses({ tokenAddresses: tokenAddresses })).clear();
        }

        if (tokenTickers.length > 0) {
            Logger.info(`PopulateMindshareDataService::flushCache::Flushing token cache | tokenTickers :: ${JSON.stringify(tokenTickers)} `);
            await (new TokenDetailsByTicker({ tickers: tokenTickers })).clear();
        }
    }
}
