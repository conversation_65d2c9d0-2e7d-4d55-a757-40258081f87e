import Logger from "../../lib/Logger";
import RedPilledTokensAlert from "../signalServices/RedPilledTokensAlert";
import WhaleWalletAlert from "../signalServices/WhaleWalletAlert";
import TokenHoldersSignal from "../signalServices/TokenHoldersSignal";
import SmartMoneyMovementAlert from "../signalServices/SmartMoneyMovementAlert";
import NewlyLaunchedTokensAlert from "../signalServices/NewlyLaunchedTokensAlert";
import GenesisSignalProcessor from "../genesis/GenesisSignalProcessor";

class PublishSignalCron {
    public async perform() {
        const currentMinute = new Date().getMinutes();
        Logger.info(`PublishSignalCron::perform:: Current date: ${new Date()}, and minute: ${currentMinute}`);

        if (currentMinute % 2 === 0) {
            // Run RedPilledTokensAlert and NewlyLaunchedTokensAlert every 2 minutes
            Logger.info(`PublishSignalCron::perform:: Current time is for red pilled tokens alert`);
            await new RedPilledTokensAlert({}).servicePerform();
            await new NewlyLaunchedTokensAlert({}).servicePerform();
        }

        if (currentMinute % 5 === 0) {
            // Runs every 5 min
            Logger.info(`PublishSignalCron::perform:: Current time is for genesis alert`);
            await new GenesisSignalProcessor({}).servicePerform();
        }

        if (currentMinute % 15 === 0) {
            // Run these alerts every 15 minutes
            Logger.info(`PublishSignalCron::perform:: Current time is for whale wallet, token holders change & smart money movement alert`);
            // await new WhaleWalletAlert({}).perform('cron');
            await new TokenHoldersSignal({}).perform('cron');
            await new SmartMoneyMovementAlert({}).perform('cron');
        }

        process.exit(0);
    }
}

const publishSignalCron = new PublishSignalCron();

publishSignalCron
    .perform()
    .then(() => { })
    .catch((err) => {
        Logger.info(
            `ExceptionAlerts::error::Error occurred during execution: ${err.message}, Stacktrace: ${err.stack}`
        );
    });
