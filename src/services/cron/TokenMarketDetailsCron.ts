import Logger from "../../lib/Logger";
import TokenMarketDetails from "../TwitterBot/TokenMarketDetails";
import AIAgentConstant from "../../lib/constant/AIAgentConstant";

const args = process.argv.slice(2);
let categories: string[] = [];

if (args.length < 1) {
    Logger.warn('If you want to run for specific category, Please pass token category identifier ie. "virtual" or "solana"');
    Logger.info(`Example:: npm run cron:tokenMarketDetails solana\n`);

    Logger.info(`Processing for all categories...`);
    categories = [
        AIAgentConstant.categoryVirtualEcosystem,
        // AIAgentConstant.categoryVirtualEcosystemPrototype,
        AIAgentConstant.categorySolanaEcosystem,
        AIAgentConstant.categorySolanaMeme,
        AIAgentConstant.categoryBaseEcosystem,
        AIAgentConstant.categoryVirtualEcosystemGenesis
    ];
} else {
    const categoryIdentifier = args[0];
    if (categoryIdentifier == 'solana') {
        categories = [AIAgentConstant.categorySolanaEcosystem, AIAgentConstant.categorySolanaMeme];
    } else if (categoryIdentifier == 'virtual') {
        categories = [AIAgentConstant.categoryVirtualEcosystem, AIAgentConstant.categoryVirtualEcosystemGenesis];
    } else if (categoryIdentifier == 'virtual-prototype') {
        categories = [AIAgentConstant.categoryVirtualEcosystemPrototype];
    } else if (categoryIdentifier == 'base') {
        categories = [AIAgentConstant.categoryBaseEcosystem];
    }
}

class TokenMarketDetailsCron {
    public async perform() {
        await new TokenMarketDetails({ categories: categories }).perform('cron');
        process.exit(0);
    }
}
const tokenMarketDetailsCron = new TokenMarketDetailsCron();

tokenMarketDetailsCron
    .perform()
    .then((rsp) => { })
    .catch((err) => {
        Logger.info(
            `ExceptionAlerts::error::Error occurred during refresh token data : ${err.message}, Stacktrace: ${err.stack}`
        );
    });
