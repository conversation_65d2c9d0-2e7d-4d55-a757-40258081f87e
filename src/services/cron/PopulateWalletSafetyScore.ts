import Logger from "../../lib/Logger";
import PopulateWalletSafetyScoreService from "../genesis/PopulateWalletSafetyScoreService";

class PopulateWalletSafetyScore {
    public async perform() {
        await new PopulateWalletSafetyScoreService({}).perform('cron');
        process.exit(0);
    }
}

const populateWalletSafetyScore = new PopulateWalletSafetyScore();

populateWalletSafetyScore
    .perform()
    .then((rsp) => { })
    .catch((err) => {
        Logger.info(
            `ExceptionAlerts::error::Error occured during populate wallet safety score : ${err.message}, Stacktrace: ${err.stack}`
        );
    });
