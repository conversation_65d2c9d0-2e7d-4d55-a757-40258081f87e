import ResponseHelper from "../../lib/helper/ResponseHelper";
import Logger from "../../lib/Logger";
import { ErrorResponse, SuccessResponse } from "../../lib/Types";
import ServiceBase from "../Base";
import moment from "moment";
import AIAgentConstant from "../../lib/constant/AIAgentConstant";
import Postgres from "../../lib/Postgres";

export default class PopulateGenesisTokenData extends ServiceBase {

    private subgraphEndpoint: string;

    private genesisAddresses: string[];

    private tokensDataByAddress: Map<string, any>;

    constructor(params: any) {
        super(params);
        this.subgraphEndpoint = 'https://graph-api.dapplooker.com/subgraphs/name/genesis-agent-v4';
        this.genesisAddresses = [];
        this.tokensDataByAddress = new Map();
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        await this.fetchAllGenesisTokens();

        if (this.genesisAddresses.length > 0) {
            await this.getGenesisTokenMarketData();
            await this.ingestGenesisTokenData();
        }

        return this.prepareResponse();
    }

    private async fetchAllGenesisTokens(): Promise<void> {
        Logger.info('PopulateGenesisTokenData::fetchAllGenesisTokens::Fetching ALL genesis tokens in batches...');
        const batchSize = 100;
        let skip = 0;
        let hasMoreTokens = true;
        const allTokens: any[] = [];

        const query = `
            query FetchTokens($first: Int!, $skip: Int!) {
                geneses(
                    orderBy: createdAt
                    orderDirection: asc
                    first: $first
                    skip: $skip
                ) {
                    id
                    agentGenesisAddress
                    associatedTokenAddress
                    createdAt
                    status
                }
            }
        `;

        try {
            while (hasMoreTokens) {
                const response = await fetch(this.subgraphEndpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query,
                        variables: { first: batchSize, skip },
                    }),
                });

                if (!response.ok) {
                    throw new Error(`Failed to fetch data: ${response.statusText}`);
                }

                const responseBody = await response.json();
                const tokens = responseBody.data?.geneses || [];

                allTokens.push(...tokens);
                skip += batchSize;
                hasMoreTokens = tokens.length === batchSize;
            }
            this.genesisAddresses = allTokens.map((item: any) => item.id?.toLowerCase());
            console.log(this.genesisAddresses.length)
            Logger.info(`Fetched ${this.genesisAddresses.length} tokens from the subgraph.`);
        } catch (error: any) {
            Logger.error(`Error: ${error.message}`);
            throw error;
        }
    }

    private async getGenesisTokenMarketData(): Promise<void> {
        let allTokensData: any[] = [];
        let page = 1;

        while (true) {
            try {
                const url = AIAgentConstant.genesisDataUrl(page);
                console.log(url)
                const response = await fetch(url);
                const res = await response.json();
                if (res.data.length === 0) break;
                allTokensData = allTokensData.concat(res.data)
                page++;
            } catch (error: any) {
                console.log(error, error.message)
                Logger.error(`getGenesisTokenMarketData::Error while fetching tokens data, error: ${JSON.stringify(error)}`);
                break;
            }
        }

        await this.formatTokenData(allTokensData);
    }

    private async formatTokenData(allTokensData: any[]): Promise<void> {
        const tokenDetailsModel = (await Postgres.getDbModels()).tokenDetails;
        const virtualProtocolData = await tokenDetailsModel.getByTokenId(AIAgentConstant.virtualProtocolToken);
        const virtualUSDPrice = virtualProtocolData?.dataValues?.usd_price || 0;

        for (const token of allTokensData) {
            try {
                const genesisAddress = token?.genesisAddress?.toLowerCase();
                let tokenInfo: any = {};
                if (!genesisAddress || !this.genesisAddresses.includes(genesisAddress)) continue;

                if (token?.virtual?.tokenAddress) {
                    const genesisTokenInfoUrl = AIAgentConstant.genesisTokensInfoUrlByAddress(token?.virtual?.tokenAddress.toLowerCase());
                    const response = await fetch(genesisTokenInfoUrl);
                    tokenInfo = await response.json();
                }

                const tokenDetails = {
                    token: token.virtual.symbol,
                    token_id: `${genesisAddress}-genesis`,
                    token_name: token.virtual.name,
                    token_address: token?.virtual?.tokenAddress ? token.virtual?.tokenAddress?.toLowerCase() : null,
                    genesis_address: token.genesisAddress.toLowerCase(),
                    result: token.result,
                    holder_count: tokenInfo?.holderCount ? tokenInfo?.holderCount : null,
                    mcap: tokenInfo?.mcapInVirtual ? tokenInfo.mcapInVirtual * virtualUSDPrice : null,
                    chain: AIAgentConstant.networkMap[AIAgentConstant.network.BASE],
                    description: token.virtual.description,
                    image: token.virtual.image.url,
                    tokenomics: token.virtual.tokenomics,
                    has_unlocked: token.virtual.tokenomicsStatus?.hasUnlocked,
                    days_from_first_unlock: token.virtual.tokenomicsStatus?.daysFromFirstUnlock,
                    virtual_id: token.virtual.id,
                    points_pledged: token.totalPoints,
                    committed_virtuals: token.totalVirtuals,
                    participants: token.totalParticipants,
                    status: AIAgentConstant.genesisTokenStatusMap[token.status] ?? AIAgentConstant.genesisTokenStatusMap.INITIALIZED,
                    committed_percentage: null,
                    tpp: null,
                    max_allocation: null,
                    roi: null,
                    updated_at: moment.utc().toDate()
                };

                this.tokensDataByAddress.set(genesisAddress, tokenDetails);
            } catch (error: any) {
                console.log(error, error.message)
                Logger.error(`Error formatting data for token ${token.virtual.tokenAddress}: ${JSON.stringify(error)}`);
            }
        }
    }

    private async ingestGenesisTokenData(): Promise<void> {
        const genesisRawData = (await Postgres.getDbModels()).genesisRawData;
        const genesisLqaData = (await Postgres.getDbModels()).genesisLqaData;

        const detailsArray = [];
        const metricsArray = [];
        console.log(this.tokensDataByAddress)

        for (const [_, tokenData] of this.tokensDataByAddress.entries()) {
            const details = {
                token: tokenData.token,
                tokenId: tokenData.token_id,
                tokenName: tokenData.token_name,
                tokenAddress: tokenData.token_address,
                genesisAddress: tokenData.genesis_address,
                result: tokenData.result,
                chain: tokenData.chain,
                description: tokenData.description,
                image: tokenData.image,
                tokenomics: tokenData.tokenomics,
                hasUnlocked: tokenData.has_unlocked,
                daysFromFirstUnlock: tokenData.days_from_first_unlock,
                virtualId: tokenData.virtual_id,
                holderCount: tokenData.holder_count,
                mcap: tokenData.mcap,
                handle: tokenData.handle,
                updatedAt: tokenData.updated_at
            };

            const metrics = {
                token: tokenData.token,
                tokenId: tokenData.token_id,
                tokenName: tokenData.token_name,
                tokenAddress: tokenData.token_address,
                genesisAddress: tokenData.genesis_address,
                image: tokenData.image,
                status: tokenData.status,
                participants: tokenData.participants,
                pointsPledged: tokenData.points_pledged,
                committedVirtuals: tokenData.committed_virtuals,
                committedPercentage: tokenData.committed_percentage,
                tpp: tokenData.tpp,
                maxAllocation: tokenData.max_allocation,
                roi: tokenData.roi,
                updatedAt: tokenData.updated_at
            };

            detailsArray.push(details);
            metricsArray.push(metrics);
        }
        console.log(detailsArray);

        try {
            if (detailsArray.length) {
                await genesisRawData.bulkCreate(detailsArray);
            }
            if (metricsArray.length) {
                await genesisLqaData.bulkCreate(metricsArray);
            }
            Logger.info('PopulateGenesisTokenData::ingestGenesisTokenData::Successfully ingested all token data.');
        } catch (error: any) {
            Logger.error(`PopulateGenesisTokenData::ingestGenesisTokenData::Bulk insert error: ${JSON.stringify(error)}`);
        }
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`PopulateGenesisTokenData::prepareResponse::Service execution completed`);
        return ResponseHelper.success({});
    }
}
