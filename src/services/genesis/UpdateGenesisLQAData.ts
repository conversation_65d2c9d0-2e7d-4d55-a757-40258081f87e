import GenesisLQADataByGenesisIds from "../../lib/cache/GenesisLQADataByGenesisIds";
import TokenDetailsByTicker from "../../lib/cache/TokenDetailsByTickers";
import TokenDetailsByTokenAddresses from "../../lib/cache/TokenDetailsByTokenAddresses";
import TokenDetailsByTokenIdsAndCategory from "../../lib/cache/TokenDetailsByTokenIdsAndCategory";
import VirtualTokenDetailsByTokenAddresses from "../../lib/cache/VirtualTokenDetailsByTokenAddresses";
import VirtualTokenDetailsByTokenIds from "../../lib/cache/VirtualTokenDetailsByTokenIds";
import AIAgentConstant from "../../lib/constant/AIAgentConstant";
import ResponseHelper from "../../lib/helper/ResponseHelper";
import Logger from "../../lib/Logger";
import Postgres from "../../lib/Postgres";
import { ErrorResponse, SuccessResponse } from "../../lib/Types";
import GenesisLqaData from "../../models/GenesisLqaData";
import TokenDetails from "../../models/TokenDetails";
import ServiceBase from "../Base";

export default class UpdateGenesisLQAData extends ServiceBase {

    private genesisLqaDataModel: GenesisLqaData;
    private tokenDetailsModel: TokenDetails;
    private subgraphEndpoint: string;
    private genesisById: Record<number, any>;
    private firstRun?: boolean;
    private updatedTokenAddresses: string[] = [];

    constructor(params: any) {
        super(params);
        this.firstRun = params.firstRun || false;
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        Logger.info(`UpdateGenesisLQAData::servicePerform:: Starting service with firstRun: ${oThis.firstRun || false}`);

        const dbModels = await Postgres.getDbModels();
        oThis.genesisLqaDataModel = dbModels.genesisLqaData;
        oThis.tokenDetailsModel = dbModels.tokenDetails;
        oThis.subgraphEndpoint = 'https://graph-api.dapplooker.com/subgraphs/name/genesis-agent-v4';
        oThis.genesisById = {};

        await oThis.updateGenesisTokenMetrics();
        await oThis.updateGenesisTokenROIsAndMcap();

        return oThis.prepareResponse();
    }

    private async updateGenesisTokenMetrics() {
        const oThis = this;
        const tokensToUpdate = await oThis.genesisLqaDataModel.getTokensByStatus([AIAgentConstant.genesisTokenStatusMap.INITIALIZED, AIAgentConstant.genesisTokenStatusMap.STARTED, AIAgentConstant.genesisTokenStatusMap.FINALIZED]);
        Logger.info(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Found ${tokensToUpdate.length} tokens to update`);

        const tokensMap = new Map();
        tokensToUpdate.forEach((token: any) => {
            tokensMap.set(token.genesisId, token);
        });

        const genesisIds = tokensToUpdate.map((token: any) => token.genesisId);
        oThis.genesisById = await oThis.getGenesisStatusFromSubgraph(genesisIds);
        Logger.info(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Genesis by id::${JSON.stringify(oThis.genesisById)}`);
        await oThis.updateGenesisStatusAfterEndDate(tokensToUpdate);

        const allGenesisDataMap = await oThis.getAllGenesisDataFromBulkAPI();
        Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenMetrics::All genesis data map: ${JSON.stringify(Object.fromEntries(allGenesisDataMap))}`);

        const emptyDescriptionTokens = await oThis.tokenDetailsModel.getEmptyDescriptionTokensByCategories([AIAgentConstant.categoryVirtualEcosystemGenesis]);
        const emptyDescriptionTokenMap: Map<string, any> = new Map(emptyDescriptionTokens.map((token: any) => [token.token_address?.toLowerCase(), token]));

        try {
            for (const token of tokensToUpdate) {
                const updatedToken = await oThis.updateTokenStatusAndVirtualsPrice(token);

                const formattedGenesisTokenMetrics = await oThis.formatGenesisTokenMetrics(updatedToken, updatedToken.endDate, allGenesisDataMap);
                Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Formatted genesis token metrics fot genesisId: ${updatedToken.genesisId}: ${JSON.stringify(formattedGenesisTokenMetrics)}`);

                await oThis.genesisLqaDataModel.update(formattedGenesisTokenMetrics, { genesisId: updatedToken.genesisId });
                Logger.info(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Updated genesis token metrics for genesisId: ${updatedToken.genesisId}.`);

                if (emptyDescriptionTokenMap.has(updatedToken.tokenAddress?.toLowerCase())) {
                    try {
                        const genesisData = allGenesisDataMap.get(updatedToken.genesisId.toString());
                        if (genesisData) {
                            await oThis.tokenDetailsModel.update({
                                description: genesisData?.virtual?.description
                            }, { token_address: updatedToken.tokenAddress?.toLowerCase() });
                            oThis.updatedTokenAddresses.push(updatedToken.tokenAddress?.toLowerCase());
                            Logger.info(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Updated description for token with tokenAddress: ${updatedToken.tokenAddress}`);
                        }
                    } catch (error: any) {
                        Logger.error(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Error updating description for token with tokenAddress: ${updatedToken.tokenAddress}`, { error: error.message });
                    }
                }
            }
            await oThis.flushCache(genesisIds, emptyDescriptionTokenMap);
        } catch (error: any) {
            Logger.error(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Error: ${typeof error === 'object' ? JSON.stringify(error) : error}`);
        }
    }

    private async updateGenesisStatusAfterEndDate(tokensToUpdate: any[]) {
        const oThis = this;
        // Update failed genesis
        const failedGenesisIds = Object.entries(oThis.genesisById)
            .filter(([_, genesis]: [string, any]) => genesis.status === 'FAILED')
            .map(([id]) => Number(id));
        Logger.info(`UpdateGenesisLQAData::updateGenesisStatusAfterEndDate::Failed genesis: ${JSON.stringify(failedGenesisIds)}`);
        if (failedGenesisIds.length > 0) {
            await oThis.genesisLqaDataModel.updateByGenesisId({ status: AIAgentConstant.genesisTokenStatusMap.FAILED }, failedGenesisIds);
        }

        // Update success genesis
        const successGenesisIds = Object.values(oThis.genesisById).filter((genesis: any) => genesis.status === 'SUCCESS');
        Logger.info(`UpdateGenesisLQAData::updateGenesisStatusAfterEndDate::Success genesis: ${JSON.stringify(successGenesisIds)}`);
        for (const genesis of successGenesisIds) {
            const genesisId = genesis.id;
            const tokenAddress = genesis.associatedTokenAddress;

            const token = tokensToUpdate.find((token: any) => token.genesisId == genesisId);
            if (!token) {
                Logger.error(`UpdateGenesisLQAData::updateGenesisStatusAfterEndDate::Token not found for genesisId: ${genesisId}`);
                continue;
            }
            const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000); // Current time minus 30 minutes
            if (token.endDate < thirtyMinutesAgo) {
                Logger.info(`UpdateGenesisLQAData::updateGenesisStatusAfterEndDate::Token has ended more than 30 minutes ago. genesisId: ${genesisId} . Updating status from ${token.status} to ${AIAgentConstant.genesisTokenStatus.FINALIZED}`);
                await oThis.genesisLqaDataModel.update(
                    {
                        status: AIAgentConstant.genesisTokenStatusMap.FINALIZED,
                        tokenAddress: tokenAddress
                    },
                    { genesisId: genesisId }
                );
            }
        }
    }

    private async getGenesisStatusFromSubgraph(genesisIds: string[]) {
        Logger.info(`UpdateGenesisLQAData::getGenesisStatusFromSubgraph::Fetching genesis status from subgraph for ${JSON.stringify(genesisIds)} genesis`);
        const query = `
            query FetchGenesisStatus($ids: [Int!]!) {
                geneses(first: 1000, where: {id_in: $ids}) {
                    agentGenesisAddress
                    associatedTokenAddress
                    createdAt
                    createdBlockNumber
                    createdTxHash
                    id
                    lastUpdatedAt
                    status
                    updatedBlockNumber
                    updatedTxHash
                    virtualInternalId
                }
            }
        `;

        const response = await fetch(this.subgraphEndpoint, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                query,
                variables: { ids: genesisIds },
            })
        });

        if (!response.ok) {
            throw new Error(`getGenesisStatusFromSubgraph::Failed to fetch data: ${response.statusText}`);
        }

        const responseBody = await response.json();

        if (responseBody.errors) {
            throw new Error(`UpdateGenesisLQAData::getGenesisStatusFromSubgraph::Errors: ${JSON.stringify(responseBody.errors)}`);
        }

        const genesisStatusFromSubgraph = responseBody.data?.geneses || [];
        const genesisById: Record<number, any> = {};
        genesisStatusFromSubgraph.forEach((genesis: any) => {
            genesisById[genesis.id] = genesis;
        });

        return genesisById;
    }

    private async formatGenesisTokenMetrics(token: any, endDate: Date, allGenesisDataMap: Map<number, any>) {
        const oThis = this;
        const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000); // Current time minus 30 minutes
        const isTokenEnded = endDate < thirtyMinutesAgo && token.participants && token.pointsPledged && token.committedVirtuals;

        let genesisMetrics = {}
        if (!isTokenEnded || oThis.firstRun) {
            const genesisData = allGenesisDataMap.get(token.genesisId.toString());

            if (genesisData) {
                Logger.debug(`UpdateGenesisLQAData::formatGenesisTokenMetrics::Genesis data from bulk API: ${JSON.stringify(genesisData)}`);
                const pointsPledged = genesisData.totalPoints || 0;
                const committedVirtuals = genesisData.totalVirtuals || 0;
                const totalParticipants = genesisData.totalParticipants || 0;

                genesisMetrics = {
                    participants: totalParticipants,
                    pointsPledged: pointsPledged,
                    committedVirtuals: committedVirtuals,
                    committedPercentage: committedVirtuals ? oThis.calculateCommittedPercentage(committedVirtuals) : null,
                    tpp: pointsPledged && pointsPledged > 0 ? oThis.calculateTPP(pointsPledged) : null,
                    maxAllocation: pointsPledged && pointsPledged > 0 ? oThis.calculateMaxAllocation(pointsPledged) : null,
                }
            } else {
                Logger.warn(`UpdateGenesisLQAData::formatGenesisTokenMetrics::No data found in bulk API for genesisId: ${token.genesisId}`);
                // Fallback to existing values if bulk API doesn't have data
                genesisMetrics = {
                    participants: token.participants || 0,
                    pointsPledged: token.pointsPledged || 0,
                    committedVirtuals: token.committedVirtuals || 0,
                    committedPercentage: token.committedVirtuals ? oThis.calculateCommittedPercentage(token.committedVirtuals) : null,
                    tpp: token.pointsPledged && token.pointsPledged > 0 ? oThis.calculateTPP(token.pointsPledged) : null,
                    maxAllocation: token.pointsPledged && token.pointsPledged > 0 ? oThis.calculateMaxAllocation(token.pointsPledged) : null,
                }
            }
        }

        return genesisMetrics;
    }

    private async updateTokenStatusAndVirtualsPrice(token: any) {
        const oThis = this;
        const updatedToken = { ...token };

        if (token.startDate < new Date() && token.endDate > new Date() && token.status === AIAgentConstant.genesisTokenStatusMap.INITIALIZED) {
            Logger.info(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Token is live with genesisAddress: ${token.genesisAddress} . Updating status from ${AIAgentConstant.genesisTokenStatus.INITIALIZED} to ${AIAgentConstant.genesisTokenStatus.STARTED}`);
            await oThis.genesisLqaDataModel.update(
                { status: AIAgentConstant.genesisTokenStatusMap.STARTED },
                { genesisAddress: token.genesisAddress }
            );
            updatedToken.status = AIAgentConstant.genesisTokenStatusMap.STARTED;
            token.status = AIAgentConstant.genesisTokenStatusMap.STARTED;
        }

        if (token.endDate <= new Date() && !token.virtualsPriceOnLaunch) {
            Logger.info(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Token has ended. genesisAddress: ${token.genesisAddress} . Updating Virtuals Price On Launch`);

            if (token.status !== AIAgentConstant.genesisTokenStatusMap.FINALIZED) {
                Logger.info(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Token is not finalized. Current status: ${token.status}  genesisAddress: ${token.genesisAddress} . Skipping update of virtuals price on launch`);
                return updatedToken;
            }

            if (!token.tokenAddress) {
                Logger.error(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Token has ended and has no token address. genesisAddress: ${token.genesisAddress} . Skipping update of virtuals price on launch`);
                return updatedToken;
            }

            const virtualDetails = await oThis.tokenDetailsModel.getByTokenId('virtual-protocol');
            const priceOnLaunch = virtualDetails?.usd_price || null;

            if (priceOnLaunch) {
                await oThis.genesisLqaDataModel.update(
                    { virtualsPriceOnLaunch: priceOnLaunch },
                    { tokenAddress: token.tokenAddress }
                );
                Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Updated virtuals price on launch for token address: ${token.tokenAddress} to ${priceOnLaunch}`);
                updatedToken.virtualsPriceOnLaunch = priceOnLaunch;
            } else {
                Logger.error(`UpdateGenesisLQAData::updateGenesisTokenMetrics::Token Address: ${token.tokenAddress} has no virtuals price on launch. Skipping update`);
            }
        }

        return updatedToken;
    }

    private async getAllGenesisDataFromBulkAPI(): Promise<Map<number, any>> {
        const oThis = this;
        Logger.info(`UpdateGenesisLQAData::getAllGenesisDataFromBulkAPI::Fetching all genesis data from bulk API`);

        const genesisDataMap = new Map<number, any>();
        let page = 1;
        let hasMore = true;

        while (hasMore) {
            try {
                const apiUrl = AIAgentConstant.genesisDataUrl(page);
                Logger.debug(`UpdateGenesisLQAData::getAllGenesisDataFromBulkAPI::Fetching page: ${page}, URL: ${apiUrl}`);

                const response = await fetch(apiUrl);

                if (!response.ok) {
                    throw new Error(`UpdateGenesisLQAData::getAllGenesisDataFromBulkAPI::Failed to fetch data: ${response.statusText}`);
                }

                const res = await response.json();
                const data = res.data;

                if (data.length === 0) {
                    Logger.warn('UpdateGenesisLQAData::getAllGenesisDataFromBulkAPI::No more data from API, exiting loop.');
                    hasMore = false;
                    break;
                }

                for (const item of data) {
                    const genesisId = item.genesisId;
                    if (genesisId) {
                        genesisDataMap.set(genesisId, item);
                    }
                }

                Logger.info(`UpdateGenesisLQAData::getAllGenesisDataFromBulkAPI::Page ${page}: Fetched ${data.length} items. Total collected: ${genesisDataMap.size}`);
                page += 1;
            } catch (error: any) {
                Logger.error(`UpdateGenesisLQAData::getAllGenesisDataFromBulkAPI::Error while fetching data from API, error: ${typeof error === 'object' ? JSON.stringify(error) : error}`);
                throw error;
            }
        }

        Logger.info(`UpdateGenesisLQAData::getAllGenesisDataFromBulkAPI::Successfully fetched all genesis data. Total items: ${genesisDataMap.size}`);
        return genesisDataMap;
    }

    private async getGenesisParticipantsDataFromSubgraph(genesisIds: string[]) {
        const pageSize = 1000;
        let allParticipants: any[] = [];
        let skip = 0;
        let hasMore = true;

        Logger.info(`UpdateGenesisLQAData::getGenesisParticipantsDataFromSubgraph::Starting to fetch participants for genesis IDs: ${JSON.stringify(genesisIds)}`);

        while (hasMore) {
            const query = `
                query FetchGenesisParticipants($ids: [Int!]!, $first: Int!, $skip: Int!) {
                    participateds(
                        where: {genesis_: {id_in: $ids}},
                        first: $first,
                        skip: $skip,
                        orderBy: createdAt,
                        orderDirection: asc
                    ) {
                        genesis {
                            id
                        }
                        point
                        user
                        virtuals
                    }
                }
            `;

            Logger.debug(`UpdateGenesisLQAData::getGenesisParticipantsDataFromSubgraph::Fetching page with skip: ${skip}, first: ${pageSize}`);

            const response = await fetch(this.subgraphEndpoint, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query,
                    variables: {
                        ids: genesisIds,
                        first: pageSize,
                        skip: skip
                    },
                })
            });

            if (!response.ok) {
                throw new Error(`getGenesisParticipantsDataFromSubgraph::Failed to fetch data: ${response.statusText}`);
            }

            const responseBody = await response.json();

            if (responseBody.errors) {
                throw new Error(`UpdateGenesisLQAData::getGenesisParticipantsDataFromSubgraph::Errors: ${JSON.stringify(responseBody.errors)}`);
            }

            const participants = responseBody.data?.participateds || [];
            allParticipants = allParticipants.concat(participants);

            Logger.info(`UpdateGenesisLQAData::getGenesisParticipantsDataFromSubgraph::Fetched ${participants.length} participants in this batch. Total so far: ${allParticipants.length}`);

            // Check if we got fewer results than requested, indicating we've reached the end
            if (participants.length < pageSize) {
                hasMore = false;
                Logger.info(`UpdateGenesisLQAData::getGenesisParticipantsDataFromSubgraph::Reached end of data. Final total: ${allParticipants.length} participants`);
            } else {
                skip += pageSize;
                Logger.debug(`UpdateGenesisLQAData::getGenesisParticipantsDataFromSubgraph::More data available, continuing with skip: ${skip}`);
            }
        }

        Logger.info(`UpdateGenesisLQAData::getGenesisParticipantsDataFromSubgraph::Successfully fetched all ${allParticipants.length} participants for genesis IDs: ${JSON.stringify(genesisIds)}`);
        Logger.debug(`UpdateGenesisLQAData::getGenesisParticipantsDataFromSubgraph::Participants for genesis IDs: ${JSON.stringify(genesisIds)} : ${JSON.stringify(allParticipants)}`);
        return allParticipants;
    }

    private async updateGenesisTokenROIsAndMcap() {
        const oThis = this;
        try {
            const tokensToUpdate = await oThis.genesisLqaDataModel.getTokensByStatus([AIAgentConstant.genesisTokenStatusMap.FINALIZED]);
            Logger.info(`UpdateGenesisLQAData::updateGenesisTokenROIs::Found ${tokensToUpdate.length} tokens to update`);

            const tokenAddresses = tokensToUpdate.map((token: any) => token.tokenAddress).filter((token: any) => token.tokenAddress !== null);
            const tokenDetails = await oThis.tokenDetailsModel.getByTokenAddresses(tokenAddresses, AIAgentConstant.categoryVirtualEcosystemGenesis);
            Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenROIs::Token details: ${JSON.stringify(tokenDetails)}`);

            const tokenDetailsMap = new Map();
            Object.entries(tokenDetails).forEach(([tokenAddress, token]: [string, any]) => {
                tokenDetailsMap.set(tokenAddress, token);
            });

            const mcapData = []

            for (const token of tokensToUpdate) {
                const tokenDetail = await tokenDetailsMap.get(token.tokenAddress);
                if (!tokenDetail) {
                    Logger.error(`UpdateGenesisLQAData::updateGenesisTokenROIs::Token detail not found for genesisId: ${token.genesisId} with token address: ${token.tokenAddress}`);
                }

                if (tokenDetail?.mcap) {
                    mcapData.push({
                        genesisId: token.genesisId,
                        token: token.token,
                        genesisAddress: token.genesisAddress,
                        marketCap: tokenDetail?.mcap
                    })
                }

                let roi = oThis.calculateROI(tokenDetail?.mcap, token?.virtualsPriceOnLaunch);
                let roiAth = oThis.calculateROI(tokenDetail?.mcap_ath, token?.virtualsPriceOnLaunch);
                Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenROIs::Roi: ${roi}, Roi ATH: ${roiAth}, Token: ${JSON.stringify(token)}`);
                if (roi && (!roiAth || roi > roiAth)) {
                    Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenROIs::Roi ATH cannot be lesser than ROI, updating ROI ATH = ROI = ${roi} for genesisId: ${token.genesisId} `);
                    roiAth = roi;
                }

                if (!roiAth && !roi) {
                    Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenROIs::Roi and Roi ATH not found for genesisId: ${token.genesisId} with token address: ${token.tokenAddress}`);
                    continue;
                }

                await oThis.genesisLqaDataModel.update(
                    {
                        roi: roi,
                        roiAth: roiAth
                    },
                    { genesisId: token.genesisId }
                );
                Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenROIs::Updated roi and roi ath for genesisId: ${token.genesisId}: roi: ${roi}, roiAth: ${roiAth}`);
            }

            await oThis.genesisLqaDataModel.bulkUpsert(mcapData, ['marketCap']);
            Logger.debug(`UpdateGenesisLQAData::updateGenesisTokenROIs::Updated market cap for genesisIds: ${JSON.stringify(mcapData)}`);

            const genesisIds = tokensToUpdate.map((token: any) => token.genesisId);
            await oThis.flushCache(genesisIds, new Map());
            Logger.info(`UpdateGenesisLQAData::updateGenesisTokenROIs::Flushed cache for genesisIds: ${genesisIds}`);
        } catch (error: any) {
            Logger.error(`UpdateGenesisLQAData::updateGenesisTokenROIs::Error: ${typeof error === 'object' ? JSON.stringify(error) : error}`);
        }
    }

    private getNumberOfUniqueParticipants(participants: any[]) {
        const uniqueParticipants = new Set(participants.map((participant: any) => participant.user));
        return uniqueParticipants.size;
    }

    private calculatePointsPledged(participants: any[]) {
        const ONE_BILLION = 1_000_000_000;
        const pointsPledged = participants.reduce((acc, participant) => {
            const points = parseInt(participant.point);
            return points > ONE_BILLION ? acc : acc + points; // Ignore points greater than 1 billion
        }, 0);
        return pointsPledged;
    }

    private calculateCommittedVirtuals(participants: any[]) {
        const committedVirtuals = participants.reduce((acc, participant) => acc + (parseFloat(participant.virtuals) / Math.pow(10, 18)), 0);
        return committedVirtuals;
    }

    private calculateTPP(pointsPledged: number) {
        const totalSupplyForPublicSale = AIAgentConstant.genesisPublicSaleSupplyPercentage * 1_000_000_000 / 100; // 375M
        const tpp = totalSupplyForPublicSale / pointsPledged;
        return tpp;
    }

    private calculateCommittedPercentage(committedVirtuals: number) {
        const totalCommittedVirtuals = AIAgentConstant.genesisMaxCommitableVirtualsForAgent;
        const committedPercentage = (committedVirtuals / totalCommittedVirtuals) * 100;
        return committedPercentage;
    }

    private calculateMaxAllocation(totalPointPledged: number) {
        const baseTokenPriceOnFixedFDV = AIAgentConstant.genesisFixFDVInVirtuals / 1_000_000_000;
        const tokensPurchaseable = AIAgentConstant.genesisMaxCommitableVirtualsForAgentByUser / baseTokenPriceOnFixedFDV;
        const totalSupplyForPublicSale = AIAgentConstant.genesisPublicSaleSupplyPercentage * 1_000_000_000 / 100;
        const percentageOfPublicSaleSupply = tokensPurchaseable / totalSupplyForPublicSale;
        const maxAllocation = totalPointPledged * percentageOfPublicSaleSupply;
        return maxAllocation;
    }

    private calculateROI(currentMcapOfAgent: number, virtualsPriceOnLaunch: number) {
        if (!currentMcapOfAgent || !virtualsPriceOnLaunch) {
            return null;
        }
        const roi = currentMcapOfAgent / (AIAgentConstant.genesisFixFDVInVirtuals * virtualsPriceOnLaunch);
        return roi;
    }

    private async flushCache(genesisIds: number[], tokenDetailsMap: Map<string, any>) {
        await (new GenesisLQADataByGenesisIds({
            genesisIds: genesisIds
        })).clear();

        if (tokenDetailsMap.size > 0) {
            // Filter tokenDetailsMap to only include tokens with addresses in updatedTokenAddresses
            const updatedTokenDetails = Array.from(tokenDetailsMap.entries())
                .filter(([tokenAddress, _]) => this.updatedTokenAddresses.includes(tokenAddress))
                .map(([_, tokenDetail]) => tokenDetail);

            const tokenIds = updatedTokenDetails.map((token: any) => token.token_id).filter((id: any) => id);

            Logger.info(`TokenMarketDetails:: flushCache:: Flushing token cache | tokenIds :: ${JSON.stringify(tokenIds)} `);
            await (new VirtualTokenDetailsByTokenIds({ tokenIds: tokenIds })).clear();
            await (new TokenDetailsByTokenIdsAndCategory({ tokenIds: tokenIds, categories: AIAgentConstant.publicEcosystemToCategoriesMap[AIAgentConstant.ecosystemVirtuals] })).clear();

            const filteredList: string[] = this.updatedTokenAddresses.filter((item: string): item is string => item != null);
            Logger.info(`TokenMarketDetails:: flushCache:: Flushing token cache | tokenAddresses :: ${JSON.stringify(filteredList)} | length :: ${filteredList?.length} `);
            await (new TokenDetailsByTokenAddresses({
                tokenAddresses: filteredList,
                category: AIAgentConstant.categoryVirtualEcosystemGenesis
            })).clear();
            await (new VirtualTokenDetailsByTokenAddresses({ tokenAddresses: filteredList })).clear();

            const updatedTokenTickers = updatedTokenDetails.map((token: any) => token.token).filter((ticker: any) => ticker);
            Logger.info(`TokenMarketDetails:: flushCache:: Flushing token cache | tokenTickers :: ${JSON.stringify(updatedTokenTickers)} `);
            await (new TokenDetailsByTicker({ tickers: updatedTokenTickers })).clear();
        }
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`UpdateGenesisLQAData::prepareResponse::Service execution completed`);
        return ResponseHelper.success({});
    }
}
