import ResponseHelper from "../../lib/helper/ResponseHelper";
import Logger from "../../lib/Logger";
import Postgres from "../../lib/Postgres";
import { SuccessResponse, WalletSafetyScoreModelAttributes } from "../../lib/Types";
import ServiceBase from "../Base";
import moment from 'moment';
import AIAgentConstant from "../../lib/constant/AIAgentConstant";
import Basic from "../../lib/helper/Basic";
import WalletSafetyScoreByGenesisAddresses from "../../lib/cache/WalletSafetyScoreByGenesisAddresses";
import LQAHelper from "../lqa/LQAHelper";

type WalletMetrics = {
    walletAgePoints: number;
    walletTxActivityPoints: number;
};

export default class PopulateWalletSafetyScoreService extends ServiceBase {

    private genesisMemberDetailsByWalletAddress: Record<string, any>;

    private readonly POINTS_RULES: Record<keyof WalletMetrics, (value: number) => number> = {
        walletAgePoints: (months) =>
            months > 12 ? 30 :
                months > 6 ? 25 :
                    months > 3 ? 20 :
                        months > 1 ? 15 : 10,

        walletTxActivityPoints: (months) =>
            months >= 6 ? 15 :
                months >= 3 ? 10 :
                    months > 0 ? 5 : 0,
    };

    private readonly MAX_RAW_POINTS = 45; // Max possible score (30 + 15 = 45), We will normalize it to 100

    constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.genesisMemberDetailsByWalletAddress = {};
    }

    public async servicePerform(): Promise<any> {
        const oThis = this;

        await oThis.fetchGenesisMemberDetails();

        await oThis.populateWalletSafetyScore();

        await oThis.flushCache();

        return oThis.prepareResponse();
    }

    private async fetchGenesisMemberDetails() {
        const oThis = this;
        Logger.info(`PopulateWalletSafetyScoreService::fetchGenesisMemberDetails::Fetching genesis member details...`);
        const genesisMembersListEndpoint = `https://api.dapplooker.com/chart/f26ad872-4bae-4898-a12e-94c7ce9c7b91?api_key=${AIAgentConstant.aiUserAPIKey}&output_format=json`;
        const genesisMembersListResponse = await fetch(genesisMembersListEndpoint);
        const genesisMembersList = await genesisMembersListResponse.json() as any;
        Logger.info(`PopulateWalletSafetyScoreService::fetchGenesisMemberDetails::Fetched ${genesisMembersList.length} genesis member details.`);
        Logger.debug(`PopulateWalletSafetyScoreService::fetchGenesisMemberDetails::genesisMembersList: ${JSON.stringify(genesisMembersList)}`);
        for (const genesisMember of genesisMembersList) {
            oThis.genesisMemberDetailsByWalletAddress[genesisMember.member_wallet_address] = genesisMember;
        }
        Logger.info(`PopulateWalletSafetyScoreService::fetchGenesisMemberDetails::Fetched ${Object.keys(oThis.genesisMemberDetailsByWalletAddress).length} genesis member details.`);
        Logger.debug(`PopulateWalletSafetyScoreService::fetchGenesisMemberDetails::genesisMemberDetailsByWalletAddress: ${JSON.stringify(oThis.genesisMemberDetailsByWalletAddress)}`);
    }

    private async fetchWalletTransactions(walletAddress: string) {
        const oThis = this;
        Logger.info(`PopulateWalletSafetyScoreService::fetchWalletTransactions::Fetching wallet transactions for ${walletAddress}`);
        const walletTransactionsEndpoint = `https://api.dapplooker.com/chart/80dd98dc-92c5-4243-85c5-e286ab490c1d?api_key=${AIAgentConstant.aiUserAPIKey}&output_format=json&filterParams={"member_wallet_address":"${walletAddress}"}`;
        const walletTransactionsResponse = await fetch(walletTransactionsEndpoint);

        if (!walletTransactionsResponse.ok) {
            const text = await walletTransactionsResponse.text();
            Logger.error(`Failed to fetch transactions. Status: ${walletTransactionsResponse.status}, Body: ${text}`);
            return [];
        }
        const walletTransactions = await walletTransactionsResponse.json() as any;
        Logger.info(`PopulateWalletSafetyScoreService::fetchWalletTransactions::Fetched ${walletTransactions.length} wallet transactions for ${walletAddress}`);
        Logger.debug(`PopulateWalletSafetyScoreService::fetchWalletTransactions::walletTransactions: ${JSON.stringify(walletTransactions)}`);
        return walletTransactions;
    }

    private async populateWalletSafetyScore() {
        const oThis = this;
        const walletSafetyScoreModel = (await Postgres.getDbModels()).walletSafetyScore;
        const memberWalletAddresses = Object.keys(oThis.genesisMemberDetailsByWalletAddress);
        let dataToInsert: WalletSafetyScoreModelAttributes[] = [];
        const batchSize = 10;
        const totalBatches = Math.ceil(memberWalletAddresses.length / batchSize);
        for (let i = 0; i < memberWalletAddresses.length; i += batchSize) {
            const walletAddressesBatch = memberWalletAddresses.slice(i, i + batchSize);
            Logger.info(`PopulateWalletSafetyScoreService::populateWalletSafetyScore::Processing batch ${i / batchSize + 1} of ${totalBatches} with ${walletAddressesBatch.length} wallet addresses.`);
            for (const walletAddress of walletAddressesBatch) {
                Logger.info(`PopulateWalletSafetyScoreService::populateWalletSafetyScore::Processing wallet address ${walletAddress}`);
                const genesisMemberDetails = oThis.genesisMemberDetailsByWalletAddress[walletAddress];
                const walletFundedAt = genesisMemberDetails?.walletFundedAt;
                const walletTransactions = await oThis.fetchWalletTransactions(walletAddress);
                const score = oThis.calculateSafetyScore(walletFundedAt, walletTransactions);

                const resoningData = {
                    ProjectTokenTicker: genesisMemberDetails.symbol,
                    memberTitle: genesisMemberDetails.title,
                    memberBio: genesisMemberDetails.bio,
                    memberSocials: genesisMemberDetails.socials,
                    memberWalletAddress: walletAddress,
                    walletFundedAt: walletFundedAt,
                    walletSafetyScore: score.safetyScore,
                }

                const prompt = `You are an AI project creator wallet safety score expert. You need to generate a reasoning summary for the wallet safety score. You are given a project token ticker, member title, member bio, member socials, member wallet address, wallet funded at, and wallet safety score as following:\n${JSON.stringify(resoningData)}`;

                const reasoning = await new LQAHelper().generateReasoningSummary(prompt);
                Logger.info(`PopulateWalletSafetyScoreService::populateWalletSafetyScore::Calculated safety score for ${walletAddress}: ${JSON.stringify(score)}`);
                dataToInsert.push({
                    genesisId: genesisMemberDetails.genesis_id,
                    genesisAddress: genesisMemberDetails.genesis_address,
                    memberWalletAddress: walletAddress,
                    walletTitle: genesisMemberDetails.title,
                    safetyScore: score.safetyScore,
                    reasoning: reasoning,
                    walletAgePoints: score.pointsMap.walletAgePoints,
                    walletTxActivityPoints: score.pointsMap.walletTxActivityPoints,
                });
                await Basic.sleep(300);
            }
            await walletSafetyScoreModel.bulkCreate(dataToInsert);
            Logger.info(`PopulateWalletSafetyScoreService::populateWalletSafetyScore::Populated wallet safety score for ${Object.keys(oThis.genesisMemberDetailsByWalletAddress).length} genesis member details.`);
            Logger.info(`PopulateWalletSafetyScoreService::populateWalletSafetyScore::Processed batch ${i / batchSize + 1} of ${totalBatches} with ${walletAddressesBatch.length} wallet addresses.`);
        }
    }

    private calculateSafetyScore(walletFundedAt: string, transactions: any[]): { pointsMap: WalletMetrics, safetyScore: number } {
        const walletAgeInMonths = walletFundedAt ? moment().diff(moment(walletFundedAt), 'months') : 0;
        const activeMonthKeys = new Set<string>();
        for (const tx of transactions) {
            const key = moment(tx.timestamp).format('YYYY-MM');
            activeMonthKeys.add(key);
        }

        const activeMonths = activeMonthKeys.size;

        const metrics: WalletMetrics = {
            walletAgePoints: walletAgeInMonths,
            walletTxActivityPoints: activeMonths,
        };

        const pointsMap: WalletMetrics = {
            walletAgePoints: 0,
            walletTxActivityPoints: 0,
        }

        let rawPoints = 0;
        for (const key of Object.keys(metrics) as (keyof WalletMetrics)[]) {
            const points = this.POINTS_RULES[key](metrics[key]);
            pointsMap[key] = points;
            rawPoints += points;
        }

        // Scale to 1–10 range
        const scaledScore = Math.round((rawPoints / this.MAX_RAW_POINTS) * 10);
        const safetyScore = Math.max(1, Math.min(scaledScore, 10)); // Clamp between 1–10

        return { pointsMap, safetyScore };
    }

    private async flushCache() {
        const oThis = this;
        Logger.info(`PopulateWalletSafetyScoreService::flushCache::Flushing cache for genesis addresses`);
        await (new WalletSafetyScoreByGenesisAddresses({ genesisContractAddresses: Object.keys(oThis.genesisMemberDetailsByWalletAddress) })).clear();
    }

    private prepareResponse(): SuccessResponse {
        const oThis = this;
        Logger.info(`PopulateWalletSafetyScoreService::prepareResponse::Service execution completed.`);
        return ResponseHelper.success({});
    }
}
