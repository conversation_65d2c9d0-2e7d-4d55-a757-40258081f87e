import AIAgentConstant from "../../lib/constant/AIAgentConstant";
import SignalConstant from "../../lib/constant/SignalConstant";
import TelegramConstant from "../../lib/constant/TelegramConstant";
import GenesisSignalHelper from "../../lib/helper/GenesisSignalHelper";
import { LokyTerminalHelper } from "../../lib/helper/LokyTerminalHelper";
import ResponseHelper from "../../lib/helper/ResponseHelper";
import Logger from "../../lib/Logger";
import Postgres from "../../lib/Postgres";
import { ErrorResponse, SuccessResponse } from "../../lib/Types";
import Utils from "../../lib/Utils";
import GenesisLqaData from "../../models/GenesisLqaData";
import GenesisRawData from "../../models/GenesisRawData";
import Signal from "../../models/Signal";
import XAgentBase from "../XAgentBase";

export default class GenesisSignalProcessor extends XAgentBase {
    private genesisLqaDataModel: GenesisLqaData;
    private genesisRawDataModel: GenesisRawData;
    private signalModel: Signal;

    // Genesis Post-Launch Analysis Time Window (in minutes)
    private readonly POST_LAUNCH_MIN_WINDOW = 60; // Post should be sent 1 hour after launch when data is available
    private readonly POST_LAUNCH_MAX_WINDOW = 180; // 3 hours

    constructor(params: any) {
        super(params);
    }

    public async servicePerform(): Promise<SuccessResponse | ErrorResponse> {
        const oThis = this;
        const dbModels = await Postgres.getDbModels();
        oThis.genesisLqaDataModel = dbModels.genesisLqaData;
        oThis.genesisRawDataModel = dbModels.genesisRawData;
        oThis.signalModel = dbModels.signal;

        await oThis.checkAndSendGenesisPreLaunchAlert();
        await oThis.checkAndSendGenesisPostLaunchAnalysis();

        return oThis.prepareResponse();
    }

    private async checkAndSendGenesisPreLaunchAlert(): Promise<void> {
        const oThis = this;
        try {
            Logger.info('GenesisSignalProcessor::checkAndSendGenesisPreLaunchAlert::Starting 1 hour launch alert check');

            const activeTokens = await oThis.genesisLqaDataModel.getTokensByStatus([AIAgentConstant.genesisTokenStatusMap.STARTED]);
            Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPreLaunchAlert::Found ${activeTokens.length} active tokens to check`);

            const now = new Date();

            for (const token of activeTokens) {
                if (!token.endDate) {
                    Logger.warn(`GenesisSignalProcessor::checkAndSendGenesisPreLaunchAlert::Genesis id ${token.genesisId} has no end date, skipping`);
                    continue;
                }

                const endDateTime = new Date(token.endDate);
                const minutesUntilEnd = (endDateTime.getTime() - now.getTime()) / (1000 * 60);

                // Check if token is in the 70-45 minute window before end date
                if (minutesUntilEnd <= 70 && minutesUntilEnd >= 45) {
                    Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPreLaunchAlert::Genesis id ${token.genesisId} is ${minutesUntilEnd.toFixed(1)} minutes from launch`);

                    const genesisData = await oThis.getGenesisData(token.genesisId);
                    if (!genesisData) continue;

                    const signalExists = await GenesisSignalHelper.checkIfSignalExists(genesisData, oThis.signalModel, SignalConstant.genesisPreLaunchAlertStrId);
                    if (signalExists) {
                        Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPreLaunchAlert::Signal already exists for genesis id ${token.genesisId}`);
                        continue;
                    }

                    Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPreLaunchAlert::Genesis id ${token.genesisId} has ${genesisData.committedPercentage}% commitment, proceeding with signal`);
                    await oThis.sendGenesisPreLaunchAlert(genesisData);
                } else {
                    Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPreLaunchAlert::Genesis id ${token.genesisId} is ${minutesUntilEnd.toFixed(1)} minutes from launch, outside alert window`);
                }
            }
        } catch (error: any) {
            Logger.error(`GenesisSignalProcessor::checkAndSendGenesisPreLaunchAlert::Error: ${error.message}`);
        }
    }

    private async checkAndSendGenesisPostLaunchAnalysis(): Promise<void> {
        const oThis = this;
        try {
            Logger.info('GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::Starting post-launch analysis check');

            const now = new Date();
            const maxWindowInMs = oThis.POST_LAUNCH_MAX_WINDOW * 60 * 1000;
            const minWindowInMs = oThis.POST_LAUNCH_MIN_WINDOW * 60 * 1000;
            const maxWindowAgo = new Date(now.getTime() - maxWindowInMs);
            const minWindowAgo = new Date(now.getTime() - minWindowInMs);

            const recentlyFinalizedTokens = await oThis.genesisLqaDataModel.getTokensByStatusAndDateRange(
                [AIAgentConstant.genesisTokenStatusMap.FINALIZED],
                maxWindowAgo,
                minWindowAgo
            );
            Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::Found ${recentlyFinalizedTokens.length} recently finalized tokens to check`);

            for (const token of recentlyFinalizedTokens) {
                if (!token.endDate || !token.tokenAddress) {
                    Logger.warn(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::Genesis id ${token.genesisId} missing endDate or tokenAddress, skipping`);
                    continue;
                }

                const endDateTime = new Date(token.endDate);
                const minutesSinceEnd = (now.getTime() - endDateTime.getTime()) / (1000 * 60);

                // Check if token is in the configured time window after end date
                if (minutesSinceEnd >= oThis.POST_LAUNCH_MIN_WINDOW &&
                    minutesSinceEnd <= oThis.POST_LAUNCH_MAX_WINDOW) {
                    Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::Genesis id ${token.genesisId} ended ${(minutesSinceEnd / 60).toFixed(1)} hours ago`);

                    const genesisData = await oThis.getGenesisData(token.genesisId);
                    if (!genesisData) continue;

                    const signalExists = await GenesisSignalHelper.checkIfSignalExists(genesisData, oThis.signalModel, SignalConstant.genesisPostLaunchAnalysisStrId);
                    if (signalExists) {
                        Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::Post-launch signal already exists for genesis id ${token.genesisId}`);
                        continue;
                    }

                    const marketData = await GenesisSignalHelper.getMarketData(genesisData.tokenAddress);
                    if (!marketData) {
                        Logger.warn(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::No market data available for genesis id ${token.genesisId}, skipping`);
                        continue;
                    }

                    const participantAnalysis = await GenesisSignalHelper.getParticipantAnalysis(genesisData.tokenAddress);
                    if (!participantAnalysis) {
                        Logger.warn(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::No participant analysis available for genesis id ${token.genesisId}, skipping`);
                        continue;
                    }

                    Logger.info(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::All data available for genesis id ${token.genesisId}, sending post-launch analysis`);
                    await oThis.sendGenesisPostLaunchAnalysis(genesisData, marketData, participantAnalysis);
                } else {
                    Logger.debug(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::Genesis id ${token.genesisId} ended ${(minutesSinceEnd / 60).toFixed(1)} hours ago, outside analysis window`);
                }
            }
        } catch (error: any) {
            Logger.error(`GenesisSignalProcessor::checkAndSendGenesisPostLaunchAnalysis::Error: ${error.message}`);
        }
    }

    private async sendGenesisPreLaunchAlert(tokenData: any): Promise<void> {
        const oThis = this;
        try {
            const description = await oThis.genesisRawDataModel.getTokenDescription(tokenData.genesisId);
            Logger.info(`GenesisSignalProcessor::sendGenesisPreLaunchAlert::Sending 30-minute launch alert for genesis id ${tokenData.genesisId}`);

            if (!GenesisSignalHelper.isValidForPreLaunchAlert(tokenData)) {
                Logger.warn(`GenesisSignalProcessor::sendGenesisPreLaunchAlert::Invalid data for genesis id ${tokenData.genesisId}, skipping`);
                return;
            }

            const signalData = {
                token: tokenData.token,
                participants: Utils.millify(Number(tokenData.participants), Utils.getPrecision(Number(tokenData.participants))),
                committedVirtuals: Utils.millify(Number(tokenData.committedVirtuals), Utils.getPrecision(Number(tokenData.committedVirtuals))),
                pointsPledged: Utils.millify(Number(tokenData.pointsPledged), Utils.getPrecision(Number(tokenData.pointsPledged))),
                committedPercentage: Number(tokenData.committedPercentage).toFixed(2),
                tpp: Utils.millify(Number(tokenData.tpp), Utils.getPrecision(Number(tokenData.tpp))),
                maxAllocation: Utils.millify(Number(tokenData.maxAllocation), Utils.getPrecision(Number(tokenData.maxAllocation))),
                daysFromFirstUnlock: tokenData.daysFromFirstUnlock,
                description: description || null
            }

            const telegramText = TelegramConstant.genesisPreLaunchAlertText('Telegram', signalData);
            const xText = TelegramConstant.genesisPreLaunchAlertText('X', signalData);

            await oThis.sendGenesisPost(tokenData, telegramText, xText, SignalConstant.genesisPreLaunchAlertStrId);
        } catch (error: any) {
            Logger.error(`GenesisSignalProcessor::sendGenesisPreLaunchAlert::Error sending signal for genesis id ${tokenData.genesisId}: ${error.message}`);
        }
    }

    private async sendGenesisPostLaunchAnalysis(genesisData: any, marketData: any, participantAnalysis: any): Promise<void> {
        const oThis = this;
        try {
            Logger.info(`GenesisSignalProcessor::sendGenesisPostLaunchAnalysis::Sending post-launch analysis for genesis id ${genesisData.genesisId}`);

            if (!GenesisSignalHelper.isValidForPostLaunchAnalysis(genesisData, marketData, participantAnalysis)) {
                Logger.warn(`GenesisSignalProcessor::sendGenesisPostLaunchAnalysis::Invalid data for genesis id ${genesisData.genesisId}, skipping`);
                return;
            }

            const dpp = GenesisSignalHelper.calculateDPP(genesisData.tpp, marketData.usd_price);
            if (!dpp) {
                Logger.warn(`GenesisSignalProcessor::sendGenesisPostLaunchAnalysis::No DPP found for genesis id ${genesisData.genesisId}`);
                return;
            }

            const signalData = {
                token: genesisData.token,
                marketCap: Utils.millify(marketData.mcap, Utils.getPrecision(marketData.mcap)),
                currentRoi: Utils.millify(genesisData.roi, Utils.getPrecision(genesisData.roi)),
                athRoi: Utils.millify(genesisData.roiAth, Utils.getPrecision(genesisData.roiAth)),
                unlockDays: genesisData.daysFromFirstUnlock,
                tpp: Utils.millify(genesisData.tpp, Utils.getPrecision(genesisData.tpp)),
                dpp: dpp,
                subscriptionPct: Number(genesisData.committedPercentage).toFixed(2),
                soldAll: Utils.millify(participantAnalysis.soldAll, Utils.getPrecision(participantAnalysis.soldAll)),
                stakedAmount: Utils.millify(participantAnalysis.stakedAmount, Utils.getPrecision(participantAnalysis.stakedAmount)),
                jeetersPct: (((participantAnalysis.soldAll + participantAnalysis.soldPartially) / participantAnalysis.totalParticipants) * 100).toFixed(2),
                holdingPct: ((participantAnalysis.holdingInitialAllocation / participantAnalysis.totalParticipants) * 100).toFixed(2),
                initialHoldingPct: ((participantAnalysis.initialSupply / 1_000_000_000) * 100).toFixed(2),
                currentHoldingPct: ((participantAnalysis.currentSupply / 1_000_000_000) * 100).toFixed(2),
                top25Pct: ((participantAnalysis.top25ParticipantsHolding / 1_000_000_000) * 100).toFixed(2)
            }

            const telegramText = TelegramConstant.genesisPostLaunchAnalysisText('Telegram', signalData);
            const xPostText = TelegramConstant.genesisPostLaunchAnalysisText('X', signalData);

            await oThis.sendGenesisPost(genesisData, telegramText, xPostText, SignalConstant.genesisPostLaunchAnalysisStrId);

        } catch (error: any) {
            Logger.error(`GenesisSignalProcessor::sendGenesisPostLaunchAnalysis::Error sending post-launch analysis for genesis id ${genesisData.genesisId}: ${error.message}`);
        }
    }

    private async sendGenesisPost(tokenData: any, telegramText: string, xText: string, signalType: string): Promise<void> {
        const oThis = this;
        const includeTokenAddress = signalType === SignalConstant.genesisPostLaunchAnalysisStrId;

        const { signalObject, telegramObject } = GenesisSignalHelper.createGenesisSignalAndTelegramObjects(tokenData, telegramText, signalType, includeTokenAddress);
        await LokyTerminalHelper.sendSignal(oThis.signalModel, signalObject, telegramObject);

        try {
            Logger.info(`GenesisSignalProcessor::sendGenesisPost::Publishing X post for genesis id ${tokenData.genesisId}`);
            const sanitizedXText = GenesisSignalHelper.sanitizeTwitterText(xText);
            Logger.debug(`GenesisSignalProcessor::sendGenesisPost::X post text: ${sanitizedXText}`);
            await oThis.publishPost({ content: sanitizedXText, imageUrl: tokenData.image }, tokenData.genesisId.toString());
            Logger.info(`GenesisSignalProcessor::sendGenesisPost::Successfully published X post for genesis ${tokenData.genesisId}`);
        } catch (error: any) {
            Logger.error(`GenesisSignalProcessor::sendGenesisPost::Error publishing X post for genesis ${tokenData.genesisId}: ${error.message}`);
        }
    }

    private async getGenesisData(genesisId: number): Promise<any | null> {
        const oThis = this;
        const tokenData = await oThis.genesisLqaDataModel.getByGenesisIds([genesisId.toString()]);

        if (!tokenData || tokenData.length === 0) {
            Logger.warn(`GenesisSignalProcessor::getGenesisData::No data found for genesis id ${genesisId}`);
            return null;
        }

        return tokenData[0];
    }

    private prepareResponse(): SuccessResponse {
        Logger.info(`GenesisSignalProcessor::prepareResponse::Service execution completed`);
        return ResponseHelper.success({});
    }
}
