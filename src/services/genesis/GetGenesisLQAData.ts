import GenesisLqaData from "../../models/GenesisLqaData";
import AIAgentConstant from "../../lib/constant/AIAgentConstant";
import ExplorerConstant from "../../lib/constant/ExplorerConstant";
import ResponseHelper from "../../lib/helper/ResponseHelper";
import Logger from "../../lib/Logger";
import Postgres from "../../lib/Postgres";
import { ErrorResponse, SuccessResponse, UserModelAttributes } from "../../lib/Types";
import ServiceBase from "../Base";
import GenesisLQADataByGenesisIds from "../../lib/cache/GenesisLQADataByGenesisIds";
import Utils from "../../lib/Utils";
import { Sequelize } from "sequelize";
import { LokyTerminalHelper } from "../../lib/helper/LokyTerminalHelper";
import TokenDetailsByTicker from "../../lib/cache/TokenDetailsByTickers";
import VirtualTokenDetailsByTokenAddresses from "../../lib/cache/VirtualTokenDetailsByTokenAddresses";
import UserWatchlistGenesisByUserIds from "../../lib/cache/UserWatchlistGenesisByUserIds";
import Basic from "../../lib/helper/Basic";

export default class GetGenesisLQAData extends ServiceBase {

    private currentUser: UserModelAttributes;

    private genesisLqaDataModel: GenesisLqaData;

    private page: number;

    private sortBy: string;

    private sortOrder: string;

    private search: string;

    private tokens: any[];

    private genesisCategory: 'all' | 'live' | 'upcoming' | 'ended';

    private pagination: { currentPage: number, totalPages: number, size: number, count: number };

    private status: string;

    private isTerminalAccess: boolean;

    private watchlistedGenesisIdsSet: Set<number>;

    private watchlist: boolean;

    private readonly sortTypeColumnIdentifierMap: Record<string, string> = {
        UPDATEDAT: 'updatedAt',
        ROI: 'roi',
        ROIATH: 'roiAth',
        MCAP: 'marketCap',
        TOKENUNLOCK: 'daysFromFirstUnlock',
        POINTSPLEDGED: 'pointsPledged',
        VIRTUALCOMMITTED: 'committedVirtuals',
        TPP: 'tpp',
        FORMAXALLOCATION: 'maxAllocation',
        PARTICIPANTS: 'participants',
        LAUNCHDATE: 'endDate'
    }

    private readonly internalStatusToApiStatusMap: Record<number, string> = {
        [AIAgentConstant.genesisTokenStatusMap.INITIALIZED]: "Upcoming",
        [AIAgentConstant.genesisTokenStatusMap.STARTED]: "Live",
        [AIAgentConstant.genesisTokenStatusMap.FINALIZED]: "Succeeded",
        [AIAgentConstant.genesisTokenStatusMap.FAILED]: "Failed",
        [AIAgentConstant.genesisTokenStatusMap.CANCELLED]: "Cancelled"
    }

    constructor(params: any) {
        super(params);
        const oThis = this;
        oThis.currentUser = params.internalDecodedParams.currentUser! || {};
        oThis.isTerminalAccess = params.internalDecodedParams.isTerminalAccess! || false;

        oThis.page = Number(params.page) || ExplorerConstant.defaultPage;
        oThis.sortBy = params.sortBy?.toUpperCase() || 'UPDATEDAT';
        oThis.sortOrder = params.sortOrder?.toUpperCase() || ExplorerConstant.sortOrderDESC;
        oThis.search = params.search;
        oThis.genesisCategory = params.genesisCategory?.toLowerCase();
        oThis.tokens = [];
        oThis.watchlist = params.watchlist === 'true';
        oThis.status = params.status || '';
        oThis.watchlistedGenesisIdsSet = new Set();
    }

    public async servicePerform(): Promise<any | ErrorResponse> {
        const oThis = this;
        oThis.genesisLqaDataModel = (await Postgres.getDbModels()).genesisLqaData;

        await oThis.validateParams();

        if (oThis.currentUser.id) {
            await oThis.fetchWatchlistedGenesisTokens();
        }

        await oThis.fetchGenesisTokens();

        return oThis.prepareResponse();
    }

    private async validateParams() {
        const oThis = this;

        if (oThis.sortBy && !Object.keys(oThis.sortTypeColumnIdentifierMap).includes(oThis.sortBy)) {
            return oThis.unauthorizedResponse('invalidSortBy', 's_s_g_glqa_vp_1');
        }

        if (oThis.status) {
            const validApiStatuses = Object.values(oThis.internalStatusToApiStatusMap).map(status => status.toLowerCase());
            const providedStatuses = oThis.status.split(',').map(status => status.trim().toLowerCase());

            if (providedStatuses.some(status => !validApiStatuses.includes(status))) {
                return oThis.unauthorizedResponse('invalidStatus', 's_s_g_glqa_vp_2');
            }
        }

        if (oThis.genesisCategory && !['all', 'live', 'upcoming', 'ended'].includes(oThis.genesisCategory)) {
            return oThis.unauthorizedResponse('invalidGenesisCategory', 's_s_g_glqa_vp_3');
        }

        if (!oThis.isTerminalAccess && oThis.page > 1) {
            return oThis.unauthorizedResponse('terminalAccessRequired', 's_s_g_glqa_vp_4');
        }

        // Validate watchlist param requires user to be logged in
        if (oThis.watchlist && !oThis.currentUser.id) {
            return oThis.unauthorizedResponse('userNotLoggedIn', 's_s_g_glqa_vp_5');
        }
    }

    private async fetchWatchlistedGenesisTokens() {
        const oThis = this;
        if (!oThis.currentUser.id) return;
        try {
            const userWatchlistGenesisByUserIdsCache = await new UserWatchlistGenesisByUserIds({
                userIds: [oThis.currentUser.id!]
            }).fetch();
            const userWatchListedGenesis: any[] = userWatchlistGenesisByUserIdsCache.data[oThis.currentUser.id!];
            let watchlistedGenesisIds: number[] = [];
            if (!Basic.isEmptyObject(userWatchListedGenesis)) {
                watchlistedGenesisIds = userWatchListedGenesis.map((genesis: any) => genesis.genesisId);
            }
            oThis.watchlistedGenesisIdsSet = new Set(watchlistedGenesisIds);
            Logger.info(`GetGenesisLQAData::fetchWatchlistedGenesisTokens:: Found ${oThis.watchlistedGenesisIdsSet.size} watchlisted genesis tokens for user ${oThis.currentUser.id}`);
        } catch (error) {
            Logger.error(`GetGenesisLQAData::fetchWatchlistedGenesisTokens:: Error fetching watchlisted genesis tokens: ${JSON.stringify(error)}`);
        }
    }

    private async fetchGenesisTokens() {
        const oThis = this;
        const limit = ExplorerConstant.pageSize;
        const offset = oThis.page > 1 ? limit * (oThis.page - 1) : ExplorerConstant.defaultOffset;

        try {
            let sortColumn = oThis.sortTypeColumnIdentifierMap[oThis.sortBy];
            let sortDirection = oThis.sortOrder == ExplorerConstant.sortOrderASC ? 'asc' : 'desc';

            const watchlistedGenesisIds = (oThis.watchlist && oThis.currentUser.id)
                ? Array.from(oThis.watchlistedGenesisIdsSet)
                : undefined;

            const { customOrder, statusFilters } = oThis.getCustomSortingOptions();

            if (statusFilters.length > 0) {
                Logger.info(`GetGenesisLQAData::fetchGenesisTokens:: Filtering by statuses: ${JSON.stringify(statusFilters)}`);
            }

            const td = await oThis.genesisLqaDataModel.getSortedGenesisIds(
                limit,
                offset,
                oThis.search,
                customOrder || sortColumn,
                customOrder ? null : sortDirection,
                statusFilters.length > 0 ? statusFilters : undefined,
                watchlistedGenesisIds
            );

            const genesisIds = td?.genesisIds || [];
            const totalGenesisCount = td?.count || 0;

            Logger.info(`GetGenesisLQAData::fetchGenesisTokens:: totalGenesisCount: ${totalGenesisCount} | genesisIds: ${JSON.stringify(genesisIds)}`);

            oThis.setPaginationAttributes(totalGenesisCount);

            if (genesisIds.length > 0) {
                const genesisLqaDataByGenesisIds = await (new GenesisLQADataByGenesisIds({
                    genesisIds: genesisIds,
                })).fetch();

                Logger.info(`GetGenesisLQAData::fetchGenesisTokens::genesisLqaDataByGenesisIds::${JSON.stringify(genesisLqaDataByGenesisIds.data)}`);

                const tokens = genesisLqaDataByGenesisIds.data;

                let formattedGenesisTokensData: any = [];

                for (const genesisId of genesisIds) {
                    const genesisToken = tokens[genesisId.toString()];
                    if (genesisToken) {
                        const formattedGenesisData = await oThis.getFormattedGenesisData(genesisToken);
                        formattedGenesisTokensData.push(formattedGenesisData);
                    }
                }

                oThis.tokens = formattedGenesisTokensData;
            }
        } catch (error) {
            Logger.error(`GetGenesisLQAData::fetchGenesisTokens::Error fetching genesis data: ${error}`);
        }
    }

    private async calculateDPP(token: any): Promise<any> {
        let tokenDPP: any = null;

        if (token?.status !== AIAgentConstant.genesisTokenStatusMap.FINALIZED) {
            return tokenDPP;
        }

        try {
            if (token?.tokenAddress) {
                const tokenDetailsByTokenAddressesCache = await new VirtualTokenDetailsByTokenAddresses({
                    tokenAddresses: [token?.tokenAddress]
                }).fetch();

                const tokenDetails = tokenDetailsByTokenAddressesCache.data || {};

                const usdPrice = tokenDetails[token.tokenAddress.toLowerCase()]?.usd_price || null;

                tokenDPP = (token?.tpp && usdPrice) ? (token?.tpp * usdPrice) : null;
            }
        } catch (error) {
            Logger.error(`GetGenesisLQAData::calculateDPP::Error fetching USD price for token ${token?.tokenAddress}: ${error}`);
        }

        return tokenDPP;
    }

    private async getFormattedGenesisData(token: any) {
        const oThis = this;

        const dpp = await oThis.calculateDPP(token);

        return {
            genesisId: token.genesisId,
            name: token.name,
            ticker: token.token,
            icon: token.image,
            network: AIAgentConstant.networkMap[token.chain]?.toLowerCase(),
            tokenAddress: token?.tokenAddress,
            isVerified: token.isVerified ?? false,
            status: oThis.internalStatusToApiStatusMap[token.status],
            marketCap: Utils.isValidChange(token.marketCap) ? Utils.millify(Number(token.marketCap), Utils.getPrecision(Number(token.marketCap))) : null,
            tokenUnlock: token.daysFromFirstUnlock === 0 || token.hasUnlocked ? 'Unlocked' : token.daysFromFirstUnlock === 1 ? '1 day' : `${token.daysFromFirstUnlock} days`,
            participants: Utils.isValidChange(token.participants) ? Utils.millify(Number(token.participants), Utils.getPrecision(Number(token.participants))) : null,
            pointsPledged: Utils.isValidChange(token.pointsPledged) ? Utils.millify(Number(token.pointsPledged), Utils.getPrecision(Number(token.pointsPledged))) : null,
            virtualsCommitted: {
                total: token.committedVirtuals ? Utils.millify(Number(token.committedVirtuals), Utils.getPrecision(Number(token.committedVirtuals))) : null,
                subscribed: token.committedPercentage ? Number(token.committedPercentage).toFixed(2) + '%' : null
            },
            tpp: token.tpp ? Utils.millify(Number(token.tpp), Utils.getPrecision(Number(token.tpp))) : null,
            dpp: dpp ? Utils.millify(Number(dpp), Utils.getPrecision(Number(dpp))) : null,
            forMaxAllocation: token.maxAllocation ? Number(Number(token.maxAllocation).toFixed(2)) : null,
            roi: token.roi ? (Number(Number(token.roi).toFixed(1)) === 0 ? null : Number(Number(token.roi).toFixed(1))) : null,
            roiAth: token.roiAth ? (Number(Number(token.roiAth).toFixed(1)) === 0 ? null : Number(Number(token.roiAth).toFixed(1))) : null,
            virtualsForMaxAllocation: token.maxAllocation ? Number((await LokyTerminalHelper.calculateEstimateAllocation(token.genesisId.toString(), Number(token.maxAllocation))).virtualsValue) : null,
            startsAt: token?.startDate ? token?.startDate : null,
            endsAt: token?.endDate ? token.endDate : null,
            isWatchlisted: oThis.watchlistedGenesisIdsSet.has(token?.genesisId)
        }
    }

    private getCustomSortingOptions(): { customOrder: any[] | null, statusFilters: string[] } {
        const oThis = this;
        let customOrder = null;
        let statusFilters: string[] = [];

        // Set custom ordering based on genesisCategory
        if (oThis.genesisCategory) {
            switch (oThis.genesisCategory) {
                case 'all':
                    // Sort by status order (live, upcoming, success, failed, cancelled) → marketCap → updatedAt
                    customOrder = [
                        Sequelize.literal(`CASE 
                            WHEN status = ${AIAgentConstant.genesisTokenStatusMap.STARTED} THEN 1
                            WHEN status = ${AIAgentConstant.genesisTokenStatusMap.INITIALIZED} THEN 2
                            WHEN status = ${AIAgentConstant.genesisTokenStatusMap.FINALIZED} THEN 3
                            WHEN status = ${AIAgentConstant.genesisTokenStatusMap.FAILED} THEN 4
                            WHEN status = ${AIAgentConstant.genesisTokenStatusMap.CANCELLED} THEN 5
                            ELSE 6
                        END`),
                        'market_cap DESC NULLS LAST',
                        'committed_percentage DESC NULLS LAST',
                        'start_date NULLS LAST',
                        'updated_at DESC'
                    ];
                    break;
                case 'live':
                    customOrder = [
                        'committed_percentage DESC NULLS LAST',
                        'updated_at DESC'
                    ];
                    statusFilters = [`${AIAgentConstant.genesisTokenStatusMap.STARTED.toString()}`];
                    break;
                case 'upcoming':
                    customOrder = ['start_date NULLS LAST'];
                    statusFilters = [`${AIAgentConstant.genesisTokenStatusMap.INITIALIZED.toString()}`];
                    break;
                case 'ended':
                    customOrder = [
                        'market_cap DESC NULLS LAST',
                        'updated_at DESC'
                    ];
                    statusFilters = [
                        `${AIAgentConstant.genesisTokenStatusMap.FINALIZED.toString()}`,
                        `${AIAgentConstant.genesisTokenStatusMap.FAILED.toString()}`,
                        `${AIAgentConstant.genesisTokenStatusMap.CANCELLED.toString()}`
                    ];
                    break;
            }
        }

        // Apply status filters from status parameter if provided
        if (oThis.status && statusFilters.length === 0) {
            const apiStatusValues = oThis.status.split(',').map(status => status.trim().toLowerCase());
            const apiToInternalMap: Record<string, string> = {};

            Object.entries(oThis.internalStatusToApiStatusMap).forEach(([internalStatus, apiStatus]) => {
                apiToInternalMap[apiStatus.toLowerCase()] = internalStatus;
            });

            statusFilters = apiStatusValues
                .filter(apiStatus => apiToInternalMap[apiStatus])
                .map(apiStatus => apiToInternalMap[apiStatus]);
        }

        return { customOrder, statusFilters };
    }

    private setPaginationAttributes(totalTokenCount: number) {
        const oThis = this;
        const totalPages = Math.ceil(totalTokenCount / ExplorerConstant.pageSize);
        oThis.page = oThis.page > totalPages ? totalPages : oThis.page;
        oThis.pagination = {
            currentPage: oThis.page,
            totalPages: totalPages,
            size: ExplorerConstant.pageSize,
            count: totalTokenCount
        }
    }

    private prepareResponse(): SuccessResponse {
        const oThis = this;
        Logger.info(`GetGenesisLQAData:: prepareResponse::Service execution completed`);
        return ResponseHelper.success({
            pagination: oThis.pagination,
            tokens: oThis.isTerminalAccess ? oThis.tokens : oThis.tokens.slice(0, 2)
        });
    }
}
