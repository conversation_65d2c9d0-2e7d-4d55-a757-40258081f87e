import { DataTypes, InitOptions, Model, ModelIndexesOptions, Op, QueryTypes, Sequelize, WhereOptions } from 'sequelize';
import Logger from '../lib/Logger';
import { ErrorResponse, SuccessResponse, LQAHistoricalMetricModelAttributes } from '../lib/Types';
import ResponseHelper from '../lib/helper/ResponseHelper';
import moment from 'moment';

class SequelizeModel extends Model { }

export default class LQAHistoricalMetric {
    public constructor(initOptions: InitOptions) {
        SequelizeModel.init(
            this.getSchema(),
            {
                ...initOptions,
                modelName: 'LQAHistoricalMetric',
                tableName: 'lqa_historical_metrics',
                indexes: this.getIndexes(),
                updatedAt: false
            },
        );
    }

    public async bulkCreate(records: LQAHistoricalMetricModelAttributes[]): Promise<SuccessResponse | ErrorResponse> {
        let savedResults;
        try {
            savedResults = await SequelizeModel.bulkCreate(records, {
                ignoreDuplicates: true,
            });
            Logger.debug(`LQAHistoricalMetric::bulkCreate::savedResults::${JSON.stringify(savedResults)}`);
        } catch (e: any) {
            Logger.error(`LQAHistoricalMetric::bulkCreate::Error saving records. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
        return ResponseHelper.success({ savedResults });
    }

    public async create(record: LQAHistoricalMetricModelAttributes): Promise<any | ErrorResponse> {
        let savedResult;
        try {
            const result = await SequelizeModel.create(record);
            savedResult = result.get({ plain: true });
            Logger.debug(`LQAHistoricalMetric::create::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`LQAHistoricalMetric::create::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
        return ResponseHelper.success({ savedResult });
    }

    public async update(values: any, where: WhereOptions) {
        try {
            await SequelizeModel.update(
                values,
                {
                    where,
                },
            );
        } catch (e: any) {
            Logger.error(`LQAHistoricalMetric::update::Error updating record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
    }

    public async getByTokenIds(tokenIds: string[]): Promise<any> {
        const oThis = this;
        const query = `SELECT DISTINCT ON (token_id) * FROM lqa_historical_metrics WHERE token_id IN (:tokenIds) ORDER BY token_id ASC, created_at DESC`;

        const tokenData = await SequelizeModel.sequelize?.query(query, {
            replacements: { tokenIds: tokenIds },
            type: QueryTypes.SELECT
        });
        let tokenMap: Record<string, any> = {}
        for (let i = 0; i < tokenData!.length; i++) {
            let token: any = tokenData![i]
            if (token) {
                tokenMap[token.token_id] = token;
            }
        }
        Logger.debug(`LQAHistoricalMetric::getByTokenId::Tokens Data:${JSON.stringify(tokenMap)}`);
        return tokenMap;
    }

    public async getConfidenceChangeDatabyTokenIds(tokenIds: string[]) {
        const confidenceChangeData: Record<string, any> = {};
        // Fetch the latest record for each tokenId
        const latestMetricByTokenId: any = await this.getByTokenIds(tokenIds);
        const latestTokenData: any[] = Object.values(latestMetricByTokenId);
        // Process each tokenId individually
        for (const latestToken of latestTokenData) {
            const tokenId = latestToken.token_id;
            const timeIntervals = [
                { frequency: '15m', delta: 17 },
                { frequency: '1h', delta: 1 * 60 },
                { frequency: '6h', delta: 6 * 60 },
                { frequency: '24h', delta: 24 * 60 }
            ];
            const confidenceChanges: Record<string, number> = {};
            for (const { frequency, delta } of timeIntervals) {
                const targetTime = moment(latestToken.created_at).subtract(delta, 'minutes');
                // Fetch the previous record that matches the target time window
                const previousRecord: any = await SequelizeModel.findOne({
                    where: {
                        tokenId: tokenId,
                        createdAt: {
                            [Op.lt]: latestToken.created_at,
                            [Op.gte]: targetTime
                        }
                    },
                    order: [['createdAt', 'DESC']],
                    limit: 1
                });

                Logger.info(`====[ latestToken: ${latestToken} \npreviousRecord: ${previousRecord} ]====`)

                // If previous record is found, calculate the percentage change, otherwise set to 0
                if (previousRecord) {
                    const currentConfidence = latestToken[`confidence_${frequency}`];
                    const previousConfidence = previousRecord[`confidence_${frequency}`];
                    const percentageChange = ((currentConfidence - previousConfidence) / previousConfidence) * 100;
                    confidenceChanges[`changePercentage${frequency}`] = percentageChange;
                } else {
                    confidenceChanges[`changePercentage${frequency}`] = 0;
                }
            }
            confidenceChanges['reasoningSummary'] = latestToken.reasoningSummary;
            confidenceChanges['summaryCreatedAt'] = latestToken.summaryCreatedAt;
            confidenceChangeData[tokenId] = confidenceChanges;
        }
        return confidenceChangeData;
    }

    public async delete(where: WhereOptions) {
        const deleteResult: any = await SequelizeModel.destroy(
            {
                where,
            },
        );
        return deleteResult;
    }

    private getSchema() {
        return {
            tokenId: {
                type: DataTypes.STRING,
                allowNull: false,
                primaryKey: true,
            },
            confidence_15m: {
                type: DataTypes.DECIMAL(6, 2),
                allowNull: false
            },
            confidence_1h: {
                type: DataTypes.DECIMAL(6, 2),
                allowNull: false
            },
            confidence_6h: {
                type: DataTypes.DECIMAL(6, 2),
                allowNull: false
            },
            confidence_24h: {
                type: DataTypes.DECIMAL(6, 2),
                allowNull: false
            },
            confidence_change_15m: {
                type: DataTypes.DECIMAL(6, 2),
                allowNull: false
            },
            confidence_change_1h: {
                type: DataTypes.DECIMAL(6, 2),
                allowNull: false
            },
            confidence_change_6h: {
                type: DataTypes.DECIMAL(6, 2),
                allowNull: false
            },
            confidence_change_24h: {
                type: DataTypes.DECIMAL(6, 2),
                allowNull: false
            },
            reasoningSummary: {
                type: DataTypes.STRING(500),
                allowNull: true
            },
            summaryCreatedAt: {
                type: DataTypes.DATE,
                allowNull: true
            },
            createdAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW,
            }
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ["token_id", "created_at"]
            }
        ];
    }
}
