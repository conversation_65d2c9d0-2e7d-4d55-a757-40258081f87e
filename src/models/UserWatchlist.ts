import { DataTypes, InitOptions, Model, ModelIndexesOptions, Op } from "sequelize";
import Logger from "../lib/Logger";
import { ErrorResponse, SuccessResponse } from "../lib/Types";
import ResponseHelper from "../lib/helper/ResponseHelper";

class SequelizeModel extends Model { }

export default class UserWatchlist {

    public constructor(initOptions: InitOptions) {
        SequelizeModel.init(this.getSchema(), {
            ...initOptions,
            modelName: "UserWatchlist",
            tableName: "user_watchlist",
            indexes: this.getIndexes(),
            timestamps: false,
        });
    }

    public async bulkCreate(records: any[]): Promise<SuccessResponse | ErrorResponse> {
        let savedResults;
        try {
            savedResults = await SequelizeModel.bulkCreate(records, {
                ignoreDuplicates: true
            });
            Logger.debug(`UserWatchlist::bulkCreate::savedResults::${JSON.stringify(savedResults)}`);
        } catch (e: any) {
            Logger.error(`UserWatchlist::bulkCreate::Error saving records. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                records: JSON.stringify(records)
            }));
        }
        return ResponseHelper.success({ savedResults });
    }

    public async create(record: { userId: number; tokenId: string; }): Promise<any | ErrorResponse> {
        let savedResult;
        try {
            const result = await SequelizeModel.create(record);
            savedResult = result.get({ plain: true });
            Logger.debug(`UserWatchlist::save::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`UserWatchlist::save::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(["generalError"], { error: e.message }));
        }
        return ResponseHelper.success({ savedResult });
    }

    public async deleteByUserAndTokenId(userId: number, tokenId: string): Promise<any> {
        try {
            const result = await SequelizeModel.destroy({
                where: {
                    userId: userId,
                    tokenId: tokenId
                }
            });
            Logger.debug(`UserWatchlist::deleteByUserAndTokenId::Deleted record with userId: ${userId} and tokenId: ${tokenId}`);
            return result;
        } catch (error) {
            Logger.error(`UserWatchlist::deleteByUserAndTokenId::Error deleting record. Exception ${JSON.stringify(error)}`);
            throw ResponseHelper.error(['generalError'], { error: (error as Error).message });
        }
    }

    public async getByUserAndTokenId(userId: number, tokenIds: string[]): Promise<any> {
        try {
            const result = await SequelizeModel.findAll({
                where: {
                    userId: userId,
                    tokenId: {
                        [Op.in]: tokenIds
                    }
                },
                raw: true
            });
            Logger.debug(`UserWatchlist::getByUserAndTokenId::result:${JSON.stringify(result)}`);
            return result;
        } catch (error) {
            Logger.error(`UserWatchlist::getByUserAndTokenId::Error getting record. Exception ${JSON.stringify(error)}`);
            throw ResponseHelper.error(['generalError'], { error: (error as Error).message });
        }
    }

    public async getWatchlistedTokenIdsByUserId(userId: number): Promise<string[]> {
        try {
            const results = await SequelizeModel.findAll({
                where: { userId },
                attributes: ['tokenId'],
                raw: true
            });
            const tokenIds = results.map((item: any) => item.tokenId);
            return tokenIds;
        } catch (error) {
            Logger.error(`UserWatchlist::getWatchlistedTokenIdsByUserId::Error fetching records. Exception ${JSON.stringify(error)}`);
            return [];
        }
    }

    public async getByUserIds(userIds: number[]): Promise<Record<number, any[]>> {
        try {
            const watchlists: any[] = await SequelizeModel.findAll({
                where: { userId: { [Op.in]: userIds } },
                raw: true
            });
            const watchlistByUserIds: Record<number, any[]> = {}
            for (const watchlist of watchlists) {
                if (!watchlistByUserIds[watchlist.userId]) watchlistByUserIds[watchlist.userId] = [];
                watchlistByUserIds[watchlist.userId].push(watchlist);
            }
            Logger.debug(`UserWatchlist::getByUserIds::Watchlist by user ids map: ${JSON.stringify(watchlistByUserIds)}`);
            return watchlistByUserIds;
        } catch (error: any) {
            Logger.error(`UserWatchlist::getByUserIds::Error fetching records. Exception ${JSON.stringify(error.message)}`);
            return {};
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            tokenId: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            createdAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW,
            }
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["userId"]
            },
            {
                unique: false,
                fields: ["tokenId"]
            },
        ];
    }
}
