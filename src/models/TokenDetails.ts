import { DataTypes, InitOptions, Model, ModelIndexesOptions, Op, WhereOptions } from 'sequelize';
import Logger from '../lib/Logger';
import { SuccessResponse, ErrorResponse, TokenDetailsObject } from '../lib/Types';
import ResponseHelper from '../lib/helper/ResponseHelper';
import moment from "moment/moment";
import Sequelize from 'sequelize';
import AIAgentConstant from "../lib/constant/AIAgentConstant";

class SequelizeModel extends Model {
}

export default class TokenDetails {
    public constructor(initOptions: InitOptions) {
        SequelizeModel.init(
            this.getSchema(),
            {
                ...initOptions,
                modelName: 'TokenDetails',
                tableName: 'token_details',
                indexes: this.getIndexes(),
            },
        );
    }

    public async create(record: {
        id: number | undefined,
        token: string,
        token_name: string,
        context: string,
        sent: boolean
    }): Promise<SuccessResponse | ErrorResponse> {
        let savedResult;
        try {
            savedResult = await SequelizeModel.create(
                record,
            );
            Logger.debug(`TokenDetails::save::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`TokenDetails::save::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
        return ResponseHelper.success({});
    }

    private async removeDuplicates(arr: TokenDetailsObject[]): Promise<TokenDetailsObject[]> {
        const seen = new Set<string>();

        return arr.filter((item) => {
            if (seen.has(item.token_id)) {
                return false;
            }
            seen.add(item.token_id);
            return true;
        });
    }

    public async bulkCreate(records: TokenDetailsObject[]): Promise<SuccessResponse | ErrorResponse> {
        let savedResult;

        try {
            const uniqueArray = await this.removeDuplicates(records);
            Logger.debug(`TokenDetails::bulkCreate::Unique tokens::${JSON.stringify(uniqueArray)}`);
            Logger.debug(`TokenDetails::bulkCreate::Unique tokens length::${JSON.stringify(uniqueArray.length)}`);
            savedResult = await SequelizeModel.bulkCreate(
                uniqueArray as any[],
                { ignoreDuplicates: true }
            );
            Logger.debug(`TokenDetails::bulkCreate::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`TokenDetails::bulkCreate::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                input: JSON.stringify(records),
            }));
        }
        return ResponseHelper.success({});
    }

    public async update(values: any, where: WhereOptions) {
        const updateResult: any = await SequelizeModel.update(
            values,
            {
                where,
            },
        );
        if (updateResult.length === 0 || updateResult[0] < 1) {
            Logger.error(`TokenDetails::update::Error updating token details:`, {
                values: JSON.stringify(values),
                where: JSON.stringify(where)
            });
            Promise.reject(
                ResponseHelper.error(['generalError'],
                    {
                        error: 'Update operation failed',
                        values: JSON.stringify(values),
                        where: JSON.stringify(where)
                    }
                ));
        }
        return updateResult;
    }

    public async getPostContext(categories: string[], mcapCondition?: any, getAnyByMCap: boolean = false): Promise<any> {
        const skipTaValuesCheck: boolean = categories.includes(AIAgentConstant.categoryBaseEcosystem);

        let whereCondition: WhereOptions = {
            sent: false,
            category: { [Op.in]: categories }
        };

        if (!skipTaValuesCheck) {
            Object.assign(whereCondition, {
                rsi: { [Op.ne]: null },
                support: { [Op.ne]: null },
                sma: { [Op.ne]: null },
                resistance: { [Op.ne]: null },
                usd_price: { [Op.ne]: null }
            });
        }

        whereCondition.mcap = mcapCondition
            ? {
                [Op.gte]: mcapCondition.min,
                [mcapCondition.max ? Op.lte : Op.gte]: mcapCondition.max || mcapCondition.min
            }
            : { [Op.gt]: categories.includes(AIAgentConstant.categoryVirtualEcosystem) ? 1000000 : 100000000 };

        const metrics = [
            'mcap_change_percentage_24h',
            'volume_24h'
        ];
        const selectedMetric = metrics[Math.floor(Math.random() * metrics.length)];
        const orderDirection = Math.random() < 0.5 ? 'ASC' : 'DESC';

        if (getAnyByMCap) {
            whereCondition = {
                sent: false,
                category: { [Op.in]: categories },
                rsi: { [Op.ne]: null },
                support: { [Op.ne]: null },
                sma: { [Op.ne]: null },
                resistance: { [Op.ne]: null },
                usd_price: { [Op.ne]: null },
                mcap: { [Op.gt]: 1000000 }
            };
        }
        const tokenDetails = await SequelizeModel.findOne({
            where: whereCondition,
            order: [[Sequelize.col(selectedMetric), orderDirection]],
            raw: true
        });
        Logger.debug(`TokenDetails::getPostContext::selectedMetric:${selectedMetric}, orderDirection:${orderDirection}, whereCondition: ${JSON.stringify(whereCondition)} tokenDetails:${JSON.stringify(tokenDetails)}`);
        return tokenDetails;
    }

    public async getMCapFrequencyCount(categories: string[], mcapRange: any): Promise<number> {
        let whereCondition: any = {
            mcap: mcapRange
                ? { [Op.gte]: mcapRange.min, [mcapRange.max ? Op.lte : Op.gte]: mcapRange.max || mcapRange.min }
                : { [Op.gt]: categories.includes(AIAgentConstant.categoryVirtualEcosystem) ? 1000000 : 100_000_000 },
            sent: true,
            category: { [Sequelize.Op.in]: categories }
        }
        const count: number = await SequelizeModel.count({ where: whereCondition });
        return count;
    }

    public async getTokenDetails(limit: number, skip: number, categories: string[], extraWhere = {}): Promise<any> {
        try {
            const tokenDetails = await SequelizeModel.findAll({
                raw: true,
                order: [
                    ['id', 'asc']
                ],
                where: { category: { [Op.in]: categories }, ...extraWhere },
                limit: limit,
                offset: skip
            });
            Logger.debug(`TokenDetails::getTokenDetails::tokenDetails:${JSON.stringify(tokenDetails)}`);
            return tokenDetails;
        } catch (e: any) {
            Logger.error(`TokenDetails::bulkCreate::Error saving record. Exception ${JSON.stringify(e.message)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
            }));
        }
    }

    public async getTokenHandle(tokenId: string[]): Promise<any> {
        const tokenDetails: any = await SequelizeModel.findAll({
            where: {
                token: { [Op.in]: tokenId }
            },
            raw: true
        });
        Logger.debug(`TokenDetails::getTokenHandle::tokenDetails:${JSON.stringify(tokenDetails)}`);

        function createTokenHandleMap(data: any): Record<string, string | null> {
            return data.reduce((acc: any, item: any) => {
                acc[item.token] = item.handle;
                return acc;
            }, {} as Record<string, string | null>);
        }

        // Call the function and store the result
        const tokenHandleMap = createTokenHandleMap(tokenDetails);
        return tokenHandleMap;
    }

    public async getByTokenId(tokenId: string): Promise<any> {
        const oThis = this;
        const tokenData = await SequelizeModel.findOne({
            where: {
                token_id: tokenId
            }
        });
        Logger.debug(`TokenDetails::getByTokenId::tokenData:${JSON.stringify(tokenData)}`);
        return tokenData;
    }

    public async getByTokenIds(tokenIds: string[]): Promise<any> {
        const oThis = this;
        const tokenData = await SequelizeModel.findAll({
            where: {
                token_id: { [Op.in]: tokenIds }
            },
            raw: true
        });

        let tokenMap: Record<string, any> = {}
        for (let i = 0; i < tokenData.length; i++) {
            let token: any = tokenData[i]
            if (token) {
                tokenMap[token.token_id] = token;
            }
        }
        Logger.debug(`TokenDetails::getByTokenId::Tokens Data:${JSON.stringify(tokenMap)}`);
        return tokenMap;
    }

    public async getByAddresses(addresses: string[]): Promise<any> {
        const oThis = this;
        const tokenData = await SequelizeModel.findAll({
            attributes: ['token_id', 'token_address', 'total_supply'],
            where: {
                token_address: { [Op.in]: addresses }
            },
            raw: true
        });

        let tokenMap: Record<string, any> = {}
        for (let i = 0; i < tokenData.length; i++) {
            let token: any = tokenData[i]
            if (token) {
                tokenMap[token.token_address] = token;
            }
        }
        Logger.debug(`TokenDetails::getByAddresses::Tokens Data:${JSON.stringify(tokenMap)}`);
        return tokenMap;
    }

    public async getByTokenAddresses(addresses: string[], category: string = AIAgentConstant.categoryVirtualEcosystem): Promise<any> {
        const oThis = this;
        const isVirtualEcosystem = (category === AIAgentConstant.categoryVirtualEcosystem || category === AIAgentConstant.categoryVirtualEcosystemPrototype || category === AIAgentConstant.categoryVirtualEcosystemGenesis);
        const tokenData = await SequelizeModel.findAll({
            where: {
                category: category,
                [Op.or]: addresses.map((address: string) => ({
                    token_address: { [isVirtualEcosystem ? Op.iLike : Op.like]: address.toLowerCase() }
                }))
            },
            raw: true
        });

        let tokenMap: Record<string, any> = {}
        for (let i = 0; i < tokenData.length; i++) {
            let token: any = tokenData[i]
            if (token) {
                const tokenAddress = AIAgentConstant.getFormattedAddress(token.token_address, token.network);
                tokenMap[tokenAddress] = token;
            }
        }
        Logger.debug(`TokenDetails::getByTokenAddresses::Tokens Data:${JSON.stringify(tokenMap)}`);
        return tokenMap;
    }


    public async getByVirtualsTokenAddresses(addresses: string[]): Promise<any> {
        const isVirtualEcosystem = true
        const tokenData = await SequelizeModel.findAll({
            where: {
                [Op.or]: addresses.map((address: string) => ({
                    token_address: { [isVirtualEcosystem ? Op.iLike : Op.like]: address.toLowerCase() }
                }))
            },
            raw: true
        });

        let tokenMap: Record<string, any> = {}
        for (let i = 0; i < tokenData.length; i++) {
            let token: any = tokenData[i]
            if (token) {
                const tokenAddress = AIAgentConstant.getFormattedAddress(token.token_address, token.network);
                tokenMap[tokenAddress] = token;
            }
        }
        Logger.debug(`TokenDetails::getByVirtualsTokenAddresses::Tokens Data:${JSON.stringify(tokenMap)}`);
        return tokenMap;
    }

    public async getByTokenAddressesAndCategories(addresses: string[], categories: string[]): Promise<any> {
        const tokenData = await SequelizeModel.findAll({
            where: {
                category: { [Op.in]: categories },
                [Op.or]: addresses.map((address: string) => ({
                    token_address: { [Op.iLike]: address.toLowerCase() }
                }))
            },
            raw: true
        });
        Logger.debug(`TokenDetails::getByTokenAddressesAndCategories::Tokens Data:${JSON.stringify(tokenData)}`);
        return tokenData;
    }

    public async getTokenIdsWithNullColumnData(column: string): Promise<any> {
        const virtualTokens = await SequelizeModel.findAll({
            order: [
                ['token_id', 'desc']
            ],
            where: {
                category: AIAgentConstant.categoryVirtualEcosystem,
                [column]: {
                    [Op.or]: [null, ''], // Check for both null and empty string
                },
            },
            raw: true,
        });

        Logger.debug(`TokenDetails::getTokenIdsWithNullColumnData::virtual Tokens:${JSON.stringify(virtualTokens)}`);
        return virtualTokens;
    }

    public async getRandomToken(): Promise<{ token: string, handle: string | null } | null> {
        const randomToken = await SequelizeModel.findOne({
            attributes: ['token', 'handle'],
            order: Sequelize.literal('RANDOM()'),
            where: { token: { [Op.ne]: AIAgentConstant.lokyTicker } },
            raw: true
        }) as { token: string, handle: string | null } | null;

        Logger.debug(`TokenDetails::getRandomToken::randomToken:${JSON.stringify(randomToken)}`);
        return randomToken;
    }


    public async getVirtualTokensByMarketCap(limit: number): Promise<any> {
        const virtualTokens = await SequelizeModel.findAll({
            order: [
                ['mcap', 'desc']
            ],
            where: {
                category: AIAgentConstant.categoryVirtualEcosystem,
                mcap: { [Op.gt]: 100000 }
            },
            raw: true,
            limit: limit
        });

        const loky = await SequelizeModel.findOne({
            where: {
                token_id: AIAgentConstant.lokyTokenId,
                mcap: { [Op.gt]: 100000 }
            },
            raw: true
        });

        if (loky) {
            virtualTokens.push(loky);
            virtualTokens.sort((a: any, b: any) =>
                b.mcap - a.mcap
            );
        }
        Logger.debug(`TokenDetails::getVirtualTokensByMarketCap::virtual Tokens:${JSON.stringify(virtualTokens)}`);
        return virtualTokens;
    }

    public async getVirtualTokensGainer(limit: number): Promise<any> {
        const virtualTokens: any = await SequelizeModel.findAll({
            order: [
                ['mcap', 'desc'],
                ['mcap_change_percentage_24h', 'desc']
            ],
            where: {
                mcap_change_percentage_24h: { [Op.gt]: 0.5 },
                category: AIAgentConstant.categoryVirtualEcosystem,
                mcap: { [Op.gt]: 100000 }
            },
            raw: true,
            limit: limit
        });
        const loky = await this.getLokyIfGainer();
        if (loky) {
            const isLokyPresent = virtualTokens.some((token: any) => token.token_id === loky.token_id);
            if (!isLokyPresent) {
                virtualTokens.push(loky);
                virtualTokens.sort((a: any, b: any) =>
                    b.mcap_change_percentage_24h - a.mcap_change_percentage_24h
                );
            }
        }
        Logger.debug(`TokenDetails::getVirtualTokensGainer::virtual Tokens:${JSON.stringify(virtualTokens)}`);
        return virtualTokens;
    }

    public async getLokyIfGainer(): Promise<any> {
        const loky = await SequelizeModel.findOne({
            where: {
                [Op.and]: [
                    { token_id: AIAgentConstant.lokyTokenId },
                    { mcap_change_percentage_24h: { [Op.gt]: 0 } },
                ]
            },
            raw: true
        });
        Logger.debug(`TokenDetails::getLokyIfGainer:: Loky:${JSON.stringify(loky)}`);
        return loky;
    }

    public async getCryptoTokenGainer(limit: number): Promise<any> {
        const virtualTokens = await SequelizeModel.findAll({
            order: [
                ['mcap_change_percentage_24h', 'desc']
            ],
            where: {
                mcap_change_percentage_24h: { [Op.gt]: 0.5 },
                category: 'token-details',
                mcap: { [Op.gt]: 100000 }
            },
            limit: limit
        });

        Logger.debug(`TokenDetails::getCryptoTokenGainer::virtual Tokens:${JSON.stringify(virtualTokens)}`);
        return virtualTokens;
    }

    public async getMemeTokenByMarketCap(limit: number): Promise<any> {
        const virtualTokens = await SequelizeModel.findAll({
            order: [
                ['mcap', 'desc']
            ],
            where: {
                category: {
                    [Op.in]: ['top-meme-tokens', 'top-ai-meme-tokens']
                },
                mcap: { [Op.gt]: 100000 }
            },
            raw: true,
            limit: limit
        });

        Logger.debug(`TokenDetails::getMemeTokenByMarketCap::virtual Tokens:${JSON.stringify(virtualTokens)}`);
        return virtualTokens;
    }

    public async getMemeTokenGainer(limit: number): Promise<any> {
        const virtualTokens = await SequelizeModel.findAll({
            order: [
                ['mcap', 'desc'],
                ['mcap_change_percentage_24h', 'desc']
            ],
            where: {
                mcap_change_percentage_24h: { [Op.gt]: 0.5 },
                category: 'top-meme-tokens',
                mcap: { [Op.gt]: 100000 }
            },
            raw: true,
            limit: limit
        });

        Logger.debug(`TokenDetails::getMemeTokenGainer::virtual Tokens:${JSON.stringify(virtualTokens)}`);
        return virtualTokens;
    }

    public async getSmartMoneyByNetflow(limit: number): Promise<any[]> {
        try {
            const virtualTokens = await SequelizeModel.findAll({
                attributes: [
                    'token',
                    'top_25_holder_buy_24h',
                    'top_25_holder_sold_24h',
                    [
                        Sequelize.literal("top_25_holder_buy_24h - top_25_holder_sold_24h"),
                        "netflow",
                    ],
                    'usd_price',
                    'token_address',
                    'token_id',
                    'mcap'
                ],
                where: {
                    mcap: {
                        [Op.gt]: 1000000
                    },
                    usd_price: {
                        [Op.gt]: 0
                    },
                    category: AIAgentConstant.categoryVirtualEcosystem,
                    [Sequelize.Op.and]: Sequelize.literal("top_25_holder_buy_24h - top_25_holder_sold_24h > 0"),
                },
                order: [
                    ['mcap', 'desc'],
                    [Sequelize.literal("netflow"), "DESC"]
                ],
                limit,
                raw: true
            });
            Logger.debug(`TokenDetails::getSmartMoneyByNetflow::Virtual Tokens:${JSON.stringify(virtualTokens)}`);
            return virtualTokens;
        } catch (error: any) {
            Logger.error(`TokenDetails::getSmartMoneyByNetflow::Error in getSmartMoneyByNetflow: ${error.message}`);
            return [];
        }
    }

    public async resetManualTokenStatus() {
        let where = {
            type: 2,
            updatedAt: {
                [Op.lte]: moment().subtract(1, 'days').toDate()
            },
        }
        try {
            const updateResult: any = await SequelizeModel.update(
                {
                    sent: false
                },
                {
                    where
                }
            );
            Logger.debug(`BotPrompts::resetManualTokenStatus::updatedResult::${JSON.stringify(updateResult)}`);
        } catch (e: any) {
            Logger.error(`BotPrompts::resetManualTokenStatus::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                input: "Error updating manual data",
            }));
        }
    }

    public async resetAllTokenStatus(categories?: string[]) {
        try {
            let where: WhereOptions = {
                sent: true
            }
            if (Array.isArray(categories) && categories.length > 0) {
                where.category = { [Op.in]: categories };
            }

            const updateResult: any = await SequelizeModel.update(
                {
                    sent: false
                },
                { where }
            );
            Logger.debug(`TokenDetails::resetAllTokenStatus::updatedResult::${JSON.stringify(updateResult)}`);
        } catch (e: any) {
            Logger.error(`TokenDetails::resetAllTokenStatus::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                input: "Error updating all data when sent is true for all",
            }));
        }
    }

    public async getTokenDetailsByCategoryAndTokenSymbol(category: string, tokenSymbol: string): Promise<any> {
        const tokenDetails = await SequelizeModel.findOne({
            where: {
                category: category,
                [Op.or]: [
                    { token: { [Op.iLike]: tokenSymbol } },
                    { token: { [Op.iLike]: `$${tokenSymbol}` } },
                ]
            },
            order: [
                [Sequelize.literal('mcap IS NOT NULL'), 'DESC'],
                ['mcap', 'DESC'],
                ['createdAt', 'ASC']
            ]
        });
        Logger.debug(`TokenDetails::getTokenDetailsByCategoryAndTokenSymbol::tokenDetails:${JSON.stringify(tokenDetails)}`);
        return tokenDetails;
    }

    public async getTokenDetailsByCategory(category: string): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            order: [
                ['mcap', 'desc']
            ],
            where: {
                category: category,
                mcap: { [Op.ne]: null }
            },
            limit: 35
        });
        Logger.debug(`TokenDetails::getTokenDetailsByCategory::tokenDetails:${JSON.stringify(tokenDetails)}`);
        return tokenDetails;
    }

    public async bulkDeleteNonManual(): Promise<SuccessResponse | ErrorResponse> {
        let noOfRowsAffected;
        try {
            noOfRowsAffected = await SequelizeModel.destroy({
                where: {
                    type: 1,
                    updatedAt: {
                        [Op.lte]: moment().subtract(1, 'days').toDate()
                    },
                }
            })
            Logger.debug(`TokenDetails::bulkDelete::No of rows affected is ${JSON.stringify(noOfRowsAffected)}`);
        } catch (e: any) {
            Logger.error(`TokenDetails::bulkCreate::Error destroying record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                input: "Failed to clear older data",
            }));
        }
        return ResponseHelper.success({});
    }

    public async getTopGainersByPrice(categories: string[]): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                category: { [Op.in]: categories },
                price_change_percentage_1h: {
                    [Op.gte]: 5
                },
            },
            order: [['price_change_percentage_1h', 'desc']]
        });
        return tokenDetails;
    }

    public async getTopLosersByPrice(categories: string[]): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                category: { [Op.in]: categories },
                price_change_percentage_1h: {
                    [Op.lte]: -5
                }
            },
            order: [['price_change_percentage_1h', 'asc']]
        });
        return tokenDetails;
    }

    public async getTopGainerByMcap(categories: string[]): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                category: { [Op.in]: categories },
                mcap_change_percentage_24h: {
                    [Op.gt]: 0
                }
            },
            order: [['mcap_change_percentage_24h', 'desc']],
            limit: 1
        });
        return tokenDetails[0];
    }

    public async getTopLoserByMcap(categories: string[]): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                category: { [Op.in]: categories },
                mcap_change_percentage_24h: {
                    [Op.lt]: 0
                }
            },
            order: [['mcap_change_percentage_24h', 'asc']],
            limit: 1
        });
        return tokenDetails[0];
    }

    public async getRedpilledTokens(category: string, limit: number): Promise<any> {
        const twentyFourHoursAgo = new Date();
        twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                category: category,
                createdAt: {
                    [Op.gte]: twentyFourHoursAgo
                },
                usd_price: {
                    [Op.gt]: 0.0001
                }
            },
            order: [['mcap', 'desc']],
            limit: limit,
            raw: true
        });
        return tokenDetails;
    }

    public async truncateTable(): Promise<void> {
        try {
            await SequelizeModel.truncate({
                restartIdentity: true,
                cascade: true
            });
        } catch (error) {
            throw ResponseHelper.error(
                ['generalError'],
                {
                    error: 'Failed to truncate the table',
                    details: error
                }
            );
        }
    }

    public async getTokenIdsByCategoryAndTokens(category: string, tokens: string[]): Promise<string[]> {

        if (!tokens || tokens.length === 0) {
            return [];
        }

        const tokenDetails: any = await SequelizeModel.findAll({
            where: {
                token: {
                    [Op.iLike]: {
                        [Op.any]: tokens.map(token => `%${token}%`),
                    },
                },
                category: category
            },
            attributes: ["token", "token_id", "mcap", "createdAt"],
            order: [
                [Sequelize.literal('mcap IS NOT NULL'), 'DESC'],
                ['mcap', 'DESC'],
                ['createdAt', 'ASC']
            ]
        });
        Logger.debug(`TokenDetails::getTokenIdsByCategoryAndTokens::tokenDetails:${JSON.stringify(tokenDetails)}`);

        if (!tokenDetails || tokenDetails.length === 0) {
            Logger.debug("No matching tokens found.");
            return [];
        }

        const tokenMap: Record<string, { token_id: string; mcap: number; createdAt: Date }> = {};

        tokenDetails.forEach(({ token, token_id, mcap, createdAt }: {
            token: any,
            token_id: any,
            mcap: any,
            createdAt: any
        }) => {
            const existing = tokenMap[token];
            if (!existing ||
                (mcap && existing.mcap < +mcap) ||
                (mcap && existing.mcap === +mcap && createdAt < existing.createdAt)) {
                tokenMap[token] = { token_id, mcap: +mcap, createdAt };
            }
        });

        const tokenIds = Object.values(tokenMap).map((entry) => entry.token_id);

        Logger.debug(`Fetched Token IDs: ${JSON.stringify(tokenIds)}`);
        return tokenIds;
    }

    public async getTokenIdsByCategory(categories: string[]): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                category: { [Op.in]: categories }
            },
            attributes: ['token_id']
        });
        return tokenDetails.map((token: any) => token.token_id);
    }

    public async getTokenDetailsByTokenIdsAndCategory(tokenIds: string[], categories: string[]): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                token_id: { [Op.in]: tokenIds },
                category: { [Op.in]: categories }
            },
            order: [
                ['mcap', 'desc']
            ],
        });
        return tokenDetails;
    }

    public async getTokenIdsByDataSource(dataSource: number): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                data_population_source: dataSource
            },
            attributes: ['token_id']
        });
        return tokenDetails.map((token: any) => token.token_id);
    }

    public async deleteRecordsByTokenIds(tokenIds: string[]): Promise<any> {
        const tokenDetails = await SequelizeModel.destroy({
            where: {
                token_id: { [Op.in]: tokenIds }
            }
        });
        return tokenDetails;
    }

    public async getTokenAddressesByCategory(category: string): Promise<any> {
        const tokenDetails = await SequelizeModel.findAll({
            where: {
                category: category
            },
            attributes: ['token_id', 'token_address']
        });
        return tokenDetails.map((token: any) => ({ token_id: token.token_id, token_address: token.token_address }));
    }

    public async getSortedTokenIds(categories: string[], limit: number, skip: number, search?: string, sortColumn = 'mcap', sortDirection = 'desc', watchlistedTokenIds?: string[], network?: number): Promise<{
        tokenIds: string[],
        count: number
    }> {
        try {
            const whereCondition: any = {
                category: { [Op.in]: categories },
                mcap: { [Op.ne]: null },
                ...(search ? {
                    [Op.or]: [
                        { token: { [Op.iLike]: `%${search}%` } },
                        { token_name: { [Op.iLike]: `%${search}%` } },
                        { token_address: { [Op.iLike]: `%${search}%` } }
                    ]
                } : {})
            };

            // Add filtering by watchlisted token IDs if provided
            if (watchlistedTokenIds) {
                whereCondition.token_id = { [Op.in]: watchlistedTokenIds };
            }

            if (network) {
                whereCondition.network = network;
            }

            const tokenDetails = await SequelizeModel.findAndCountAll({
                attributes: ['token_id'],
                where: whereCondition,
                order: [
                    [sortColumn, sortDirection, Sequelize.literal(' NULLS LAST')],
                    ['token_id', 'ASC']
                ],
                limit: limit,
                offset: skip,
                raw: true
            });

            const rows: any = tokenDetails?.rows || [];
            const count = tokenDetails?.count || 0;
            const tokenIds = rows.map((row: { token_id: string; }) => row.token_id);
            Logger.debug(`TokenDetails::getSortedTokenIds::tokenIds:${JSON.stringify(tokenIds)}`);
            return { tokenIds, count };
        } catch (e: any) {
            Logger.error(`TokenDetails::getSortedTokenIds::Error getting token ids. Exception ${JSON.stringify(e.message)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
            }));
        }
    }

    public async getTokenDetailByTokenAndCategories(categories: string[], tokens: string[]): Promise<any[]> {
        if (!tokens || tokens.length === 0) {
            return [];
        }
        let tokenDetails: any;
        try {
            tokenDetails = await SequelizeModel.findAll({
                where: {
                    token: {
                        [Op.in]: tokens,
                    },
                    category: { [Op.in]: categories }
                },
                order: [
                    [Sequelize.literal('mcap IS NOT NULL'), 'DESC'],
                    ['mcap', 'DESC'],
                    ['createdAt', 'ASC']
                ]
            });
        } catch (error: any) {
            Logger.error(`TokenDetails::getTokenDetailByTokenAndCategories::Error fetching records. Exception ${JSON.stringify(error.message)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: error.message,
            }));
        }
        Logger.debug(`TokenDetails::getTokenDetailByTokenAndCategories::tokenDetails:${JSON.stringify(tokenDetails)}`);

        if (!tokenDetails || tokenDetails.length === 0) {
            Logger.debug("No matching tokens found.");
            return [];
        }

        const tokenMap: Record<string, { token_id: string; mcap: number; createdAt: Date }> = {};

        tokenDetails.forEach(({ token, token_id, mcap, createdAt }: {
            token: any,
            token_id: any,
            mcap: any,
            createdAt: any
        }) => {
            const existing = tokenMap[token];
            if (!existing ||
                (mcap && existing.mcap < +mcap) ||
                (mcap && existing.mcap === +mcap && existing.createdAt > createdAt)) {
                tokenMap[token] = { token_id, mcap: +mcap, createdAt };
            }
        });

        const tokenIds = Object.values(tokenMap).map((entry) => entry.token_id);

        const tokensDetails: any[] = [];

        tokenDetails.forEach((data: any) => {
            if (tokenIds.includes(data.token_id)) {
                tokensDetails.push(data);
            }
        })

        Logger.debug(`TokenDetails::getTokenDetailByTokenAndCategories::tokenDetails returning:${JSON.stringify(tokenDetails)}`);
        return tokensDetails;
    }

    public async getTokensToFindUniqueTokensInPostText(categories: string[]): Promise<any[]> {
        let tokens = [];
        try {
            tokens = await SequelizeModel.findAll({
                attributes: ['token', 'token_name', 'token_id', 'handle'],
                where: { category: { [Op.in]: categories } },
            });
        } catch (error: any) {
            Logger.error(`TokenDetails::getTokensToFindUniqueTokensInPostText::Error fetching records. Exception ${JSON.stringify(error.message)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: error.message,
            }));
        }
        return tokens;
    }

    public async getTokenDetailsForAgentMetainfoAPI(network: number, categories: string[], limit: number, offset: number, tokenAddresses?: string[], tokenTickers?: string[]) {
        const whereClause: any = { network };
        if (categories.length > 0) {
            whereClause.category = { [Op.in]: categories };
        }
        const addressesFilter = tokenAddresses && tokenAddresses.length > 0 ? tokenAddresses : undefined;
        if (addressesFilter) {
            whereClause.token_address = {
                [Op.in]: addressesFilter
            };
        }
        if (tokenTickers && tokenTickers.length > 0) {
            whereClause.token = {
                [Op.in]: tokenTickers
            };
        }

        const tokenDetails = await SequelizeModel.findAll({
            where: whereClause,
            attributes: ['token_id', 'token', 'token_name', 'category', 'token_address', 'network'],
            order: [
                ['token_id', 'asc']
            ],
            limit: limit,
            offset: offset
        });

        return tokenDetails.map((token: any) => ({
            id: token.token_id,
            symbol: token.token,
            name: token.token_name,
            address: token.token_address,
            network: AIAgentConstant.networkMap[token.network] || null
        }));
    }

    public async getByCategories(categories?: string[]): Promise<any> {
        const oThis = this;
        const td = await SequelizeModel.findAll({
            where: {
                ...(categories ? { category: { [Op.in]: categories } } : {}),
            },
            raw: true
        });
        const tokenDetailsByTokenIds: Record<string, any> = {};
        for (let i = 0; i < td.length; i++) {
            const t: any = td[i];
            tokenDetailsByTokenIds[t.token_id] = t;
        }
        Logger.debug(`TokenDetails::getByCategories::tokenDetailsByTokenIds:${JSON.stringify(tokenDetailsByTokenIds)}`);
        return tokenDetailsByTokenIds;
    }

    public async getTokenIdsByAddressAndTickerWithCount(categories: string[], tokenAddresses: string[], tokenTickers: string[], limit?: number, offset?: number, network?: number): Promise<{ tokenIds: string[], count: number }> {
        try {
            const whereCondition = {
                ...(categories?.length > 0 ? {
                    category: { [Op.in]: categories }
                } : {}),
                ...(tokenAddresses?.length > 0 ? {
                    [Op.or]: tokenAddresses.map(addr => ({
                        token_address: { [Op.iLike]: addr }
                    }))
                } : {}),
                ...(tokenTickers?.length > 0 ? {
                    [Op.or]: tokenTickers.flatMap(ticker => ([
                        { token: { [Op.iLike]: ticker } },
                        { token: { [Op.iLike]: `$${ticker}` } }
                    ]))
                } : {}),
                ...(network ? { network: network } : {})
            };

            const result = await SequelizeModel.findAndCountAll({
                attributes: ['token_id'],
                where: whereCondition,
                order: [
                    [Sequelize.literal('mcap IS NOT NULL'), 'DESC'],
                    ['mcap', 'DESC'],
                    ['createdAt', 'ASC']
                ],
                ...(limit ? { limit: limit } : {}),
                ...(offset ? { offset: offset } : {}),
                raw: true
            });

            const tokenIds = result.rows.map((token: any) => token.token_id);
            Logger.debug(`TokenDetails::getTokenIdsByAddressAndTickerWithCount::tokenIds:${JSON.stringify(tokenIds)}, count:${result.count}`);
            return { tokenIds, count: result.count };
        } catch (e: any) {
            Logger.error(`TokenDetails::getTokenIdsByAddressAndTickerWithCount::Error getting token ids. Exception ${JSON.stringify(e.message)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
            }));
        }
    }

    public async getTokenIdsByAddressAndTicker(categories: string[], tokenAddresses: string[], tokenTickers: string[], limit?: number, offset?: number, network?: number): Promise<any> {
        const result = await this.getTokenIdsByAddressAndTickerWithCount(categories, tokenAddresses, tokenTickers, limit, offset, network);
        return result.tokenIds;
    }

    public async getExistingTokens(category: string) {
        const oThis = this;
        const td = await SequelizeModel.findAll({
            attributes: ['token_id', 'token_address'],
            where: { category: category },
            raw: true
        });
        const tokenIds: Record<string, boolean> = {};
        const tokenAddresses: Record<string, boolean> = {};
        for (let i = 0; i < td.length; i++) {
            const t: any = td[i];
            tokenIds[t.token_id] = true;
            tokenAddresses[t.token_address] = true;
        }
        Logger.debug(`TokenDetails::getExistingTokens::tokenIds:${JSON.stringify(tokenIds)} | tokenAddresses:${JSON.stringify(tokenAddresses)}`);
        return { tokenIds, tokenAddresses };
    }

    public async getAllTokenDetailsByTicker(ticker: string): Promise<any[]> {
        try {
            const tokens = await SequelizeModel.findAll({
                where: {
                    [Op.or]: [
                        { token: { [Op.iLike]: ticker } },
                        { token: { [Op.iLike]: `$${ticker}` } }
                    ]
                },
                order: [
                    [Sequelize.literal('mcap IS NOT NULL'), 'DESC'],
                    ['mcap', 'DESC'],
                    ['createdAt', 'ASC']
                ],
                raw: true
            });

            Logger.debug(`TokenDetails::getAllTokenDetailsByTicker::tokens:: ${JSON.stringify(tokens)}`);
            return tokens;
        } catch (e: any) {
            Logger.error(`TokenDetails::getAllTokenDetailsByTicker::Error fetching tokens. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                ticker
            }));
        }
    }

    public async getRugScannerData(
        page: number,
        pageSize: number,
        searchQuery: string,
        sortBy: string,
        sortOrder: string,
        statuses?: number[],
        categories?: number[]
    ): Promise<{ data: any[], totalCount: number }> {
        const offset = (page - 1) * pageSize;
        const whereClause: any = {};

        if (categories && categories.length > 0) {
            whereClause.category = {
                [Op.in]: categories.map(category => AIAgentConstant.categoryMapInvert[category])
            };
        } else {
            whereClause.category = {
                [Op.in]: [
                    AIAgentConstant.categoryVirtualEcosystem,
                    AIAgentConstant.categoryVirtualEcosystemPrototype,
                    AIAgentConstant.categoryVirtualEcosystemGenesis
                ]
            };
        }

        if (statuses && statuses.length > 0) {
            whereClause.rug_status = {
                [Op.in]: statuses
            };
        }

        if (searchQuery) {
            whereClause[Op.or] = [
                { token_name: { [Op.iLike]: `%${searchQuery}%` } },
                { token: { [Op.iLike]: `%${searchQuery}%` } },
                { token_address: { [Op.iLike]: `%${searchQuery}%` } }
            ];
        }

        const [data, totalCount] = await Promise.all([
            SequelizeModel.findAll({
                attributes: ['token_name', 'token', 'token_address', 'bundle_wallet_sold_percentage', 'bundle_wallet_supply_percentage', 'rug_status', 'launch_date', 'category', 'dev_wallet', 'image'],
                where: whereClause,
                order: [[sortBy, sortOrder, 'NULLS LAST']],
                limit: pageSize,
                offset: offset,
                raw: true
            }),
            SequelizeModel.count({ where: whereClause })
        ]);

        return { data, totalCount };
    }

    public async getRugStatusSummary(): Promise<any[]> {
        const results = await SequelizeModel.findAll({
            where: {
                category: {
                    [Op.in]: [
                        AIAgentConstant.categoryVirtualEcosystem,
                        AIAgentConstant.categoryVirtualEcosystemPrototype,
                        AIAgentConstant.categoryVirtualEcosystemGenesis
                    ]
                }
            },
            attributes: [
                'category',
                'rug_status',
                [Sequelize.fn('COUNT', Sequelize.col('rug_status')), 'count']
            ],
            group: ['category', 'rug_status'],
            raw: true
        });

        Logger.debug(`TokenDetails::getRugStatusSummary::results:${JSON.stringify(results)}`);
        return results;
    }

    public async getByTokenAddress(tokenAddress: string): Promise<any> {
        const tokenData = await SequelizeModel.findAll({
            where: {
                token_address: { [Op.iLike]: tokenAddress.toLowerCase() }
            },
            raw: true
        });
        return tokenData;
    }

    public async getTokensByDevWallet(devWallet: string): Promise<any> {
        try {
            const tokens = await SequelizeModel.findAll({
                where: {
                    dev_wallet: { [Op.iLike]: devWallet.toLowerCase() }
                },
                attributes: ['token_id', 'token', 'token_name', 'token_address', 'rug_status'],
                raw: true
            });

            Logger.debug(`TokenDetails::getTokensByDevWallet::tokens:: ${JSON.stringify(tokens)}`);
            return tokens;
        } catch (e: any) {
            Logger.error(`TokenDetails::getTokensByDevWallet::Error fetching tokens. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                devWallet
            }));
        }
    }

    public async getByCategoriesForDevWalletAgeOneTimer(categories?: string[]): Promise<any> {
        const oThis = this;
        const td = await SequelizeModel.findAll({
            where: {
                ...(categories ? { category: { [Op.in]: categories } } : {}),
                dev_wallet: { [Op.not]: null },
                dev_wallet_funded_on: { [Op.is]: null }
            },
            raw: true
        });
        const tokenDetailsByTokenIds: Record<string, any> = {};
        for (let i = 0; i < td.length; i++) {
            const t: any = td[i];
            tokenDetailsByTokenIds[t.token_id] = t;
        }
        Logger.debug(`TokenDetails::getByCategories::tokenDetailsByTokenIds:${JSON.stringify(tokenDetailsByTokenIds)}`);
        return tokenDetailsByTokenIds;
    }

    public async getValidTokenIdsForExtendedTAData(categories: string[], minimumLiquidity: number): Promise<any> {
        const tokenIds = await SequelizeModel.findAll({
            where: {
                category: { [Op.in]: categories },
                total_liquidity: { [Op.gte]: minimumLiquidity },
            },
            attributes: ['token_id'],
            raw: true
        });
        Logger.debug(`TokenDetails::getValidTokenIdsForExtendedTAData::tokenIds:${JSON.stringify(tokenIds)}`);
        return tokenIds;
    }

    public async getEmptyDescriptionTokensByCategories(categories: string[]): Promise<any> {
        const tokenAddresses = await SequelizeModel.findAll({
            where: {
                category: { [Op.in]: categories },
                description: { [Op.eq]: null }
            },
            attributes: ['token_id', 'token_address', 'token'],
            raw: true
        });
        Logger.debug(`TokenDetails::getEmptyDescriptionTokensByCategories::tokenAddresses:${JSON.stringify(tokenAddresses)}`);
        return tokenAddresses;
    }

    public async getTopTokensByCategory(
      category: string,
      limit: number = 500,
      skip: number = 0,
      sortColumn: string = 'mcap',
      sortDirection: string = 'desc'
    ): Promise<{
      tokens: any[];
      count: number;
    }> {
      try {
        const tokenDetails = await SequelizeModel.findAndCountAll({
          attributes: ['token_id', 'token', 'token_name', 'token_address', 'network'],
          where: {
            category,
            mcap: { [Op.ne]: null }
          },
          order: [
            [sortColumn, sortDirection, Sequelize.literal(' NULLS LAST')],
            ['token_id', 'ASC']
          ],
          limit: limit,
          offset: skip,
          raw: true
        });
  
        const rows: any = tokenDetails?.rows || [];
        const count = tokenDetails?.count || 0;
        Logger.debug(`TokenDetails::getTopTokensByCategory::tokens:${JSON.stringify(rows)}`);
        return { tokens: rows, count };
      } catch (e: any) {
        Logger.error(`TokenDetails::getTopTokensByCategory::Error getting tokens. Exception ${JSON.stringify(e.message)}`);
        return Promise.reject(
          ResponseHelper.error(['generalError'], {
            error: e.message
          })
        );
      }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            token: {
                type: DataTypes.STRING(80),
                allowNull: false,
            },
            token_id: {
                type: DataTypes.STRING(80),
                unique: true,
                allowNull: false,
            },
            token_name: {
                type: DataTypes.STRING(80),
                allowNull: true,
            },
            category: {
                type: DataTypes.STRING(50),
                allowNull: false,
            },
            handle: {
                type: DataTypes.STRING(50),
                allowNull: true,
            },
            image: {
                type: DataTypes.STRING(255),
                allowNull: true,
            },
            sent: {
                type: DataTypes.BOOLEAN,
                allowNull: false
            },
            type: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            mcap: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true
            },
            mcap_change_percentage_15m: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            mcap_change_percentage_1h: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            mcap_change_percentage_6h: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            mcap_change_percentage_24h: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            mcap_change_percentage_7d: {
                type: DataTypes.DECIMAL(7, 2),
                allowNull: true
            },
            mcap_change_percentage_30d: {
                type: DataTypes.DECIMAL(7, 2),
                allowNull: true
            },
            fully_diluted_valuation: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true
            },
            volume_24h: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true
            },
            volume_change_percentage_7d: {
                type: DataTypes.DECIMAL(7, 2),
                allowNull: true
            },
            volume_change_percentage_30d: {
                type: DataTypes.DECIMAL(7, 2),
                allowNull: true
            },
            usd_price: {
                type: DataTypes.DECIMAL(20, 10),
                allowNull: true
            },
            price_change_percentage_1h: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            price_change_percentage_24h: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            price_change_percentage_7d: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            price_change_percentage_30d: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            price_high_24h: {
                type: DataTypes.DECIMAL(20, 10),
                allowNull: true
            },
            price_ath: {
                type: DataTypes.DECIMAL(20, 10),
                allowNull: true
            },
            circulating_supply: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true
            },
            total_supply: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            data_population_source: {
                type: DataTypes.SMALLINT,
                allowNull: true,
                defaultValue: 2
            },
            token_address: {
                type: DataTypes.STRING(80),
                allowNull: true
            },
            support: {
                type: DataTypes.DECIMAL(20, 10),
                allowNull: true
            },
            resistance: {
                type: DataTypes.DECIMAL(20, 10),
                allowNull: true
            },
            rsi: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            sma: {
                type: DataTypes.DECIMAL(20, 10),
                allowNull: true
            },
            total_liquidity: {
                type: DataTypes.DECIMAL(30, 5),
                allowNull: true,
            },
            total_holder_count: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            holder_count_change_percentage_24h: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true,
            },
            dev_wallet: {
                type: DataTypes.STRING(50),
                allowNull: true,
            },
            dev_wallet_balance: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true,
            },
            dev_wallet_purchase_usd: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true,
            },
            dev_wallet_balance_eth: {
                type: DataTypes.DECIMAL(10, 6),
                allowNull: true
            },
            dev_wallet_total_holding_percentage: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true,
            },
            dev_wallet_outflow_txs_count_24h: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            dev_wallet_outflow_amount_24h: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            dev_wallet_inflow_amount_24h: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            top_25_holder_buy_15m: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_sold_15m: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_buy_1h: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_sold_1h: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_buy_6h: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_sold_6h: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_buy_24h: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_sold_24h: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_buy_30min: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_sold_30min: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_buy_30min_usd: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            top_25_holder_sold_30min_usd: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true,
            },
            fifty_percentage_holding_wallet_count: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            },
            updatedAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            },
            network: {
                type: DataTypes.SMALLINT,
                allowNull: true,
            },
            bundle_wallet_count: {
                type: DataTypes.INTEGER,
                allowNull: true,
            },
            mindshare_3d: {
                type: DataTypes.DECIMAL(10, 5),
                allowNull: true,
            },
            mindshare_7d: {
                type: DataTypes.DECIMAL(10, 5),
                allowNull: true,
            },
            dev_wallet_funded_on: {
                type: DataTypes.DATE,
                allowNull: true,
            },
            buyback_amount: {
                type: DataTypes.DECIMAL(20, 10),
                allowNull: true
            },
            launch_date: {
                type: DataTypes.DATE,
                allowNull: true,
            },
            rug_status: {
                type: DataTypes.SMALLINT,
                allowNull: true,
            },
            bundle_wallet_supply_percentage: {
                type: DataTypes.DECIMAL(7, 2),
                allowNull: true,
            },
            bundle_wallet_outflow: {
                type: DataTypes.DECIMAL(25, 2),
                allowNull: true,
            },
            token_burn_amount: {
                type: DataTypes.DECIMAL(25, 2),
                allowNull: true
            },
            dev_initial_supply: {
                type: DataTypes.DECIMAL(25, 2),
                allowNull: true
            },
            bundle_wallet_sold_percentage: {
                type: DataTypes.DECIMAL(7, 2),
                allowNull: true,
            },
            first_100_buyers_initial_bought: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            first_100_buyers_initial_bought_percentage: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            first_100_buyers_current_holding: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            first_100_buyers_current_holding_percentage: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            top_10_holder_balance: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            top_10_holder_percentage: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            top_50_holder_balance: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            top_50_holder_percentage: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            top_100_holder_balance: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            top_100_holder_percentage: {
                type: DataTypes.DECIMAL(20, 6),
                allowNull: true
            },
            bundle_wallet_inflow: {
                type: DataTypes.DECIMAL(25, 2),
                allowNull: true
            },
            bundle_wallet_netflow: {
                type: DataTypes.DECIMAL(25, 2),
                allowNull: true
            },
            dev_total_buy: {
                type: DataTypes.DECIMAL(25, 2),
                allowNull: true
            },
            mcap_ath: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true
            }
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ["token", "token_id"]
            },
            {
                unique: true,
                fields: ["token_id"]
            },
            {
                unique: true,
                fields: ["token_address"]
            },
            {
                fields: ['category']
            },
            {
                fields: ['token']
            },
            {
                fields: ['created_at']
            }
        ];
    }
}
