import { DataTypes, InitOptions, Model, ModelIndexesOptions, Op, WhereOptions } from "sequelize";
import Logger from "../lib/Logger";
import { ErrorResponse, SuccessResponse } from "../lib/Types";
import ResponseHelper from "../lib/helper/ResponseHelper";

class SequelizeModel extends Model { }

export default class UserWatchlistGenesis {

    public constructor(initOptions: InitOptions) {
        SequelizeModel.init(this.getSchema(), {
            ...initOptions,
            modelName: "UserWatchlistGenesis",
            tableName: "user_watchlist_genesis",
            indexes: this.getIndexes(),
            timestamps: false,
        });
    }

    public async bulkCreate(records: any[]): Promise<SuccessResponse | ErrorResponse> {
        let savedResults;
        try {
            savedResults = await SequelizeModel.bulkCreate(records, {
                ignoreDuplicates: true
            });
            Logger.debug(`UserWatchlistGenesis::bulkCreate::savedResults::${JSON.stringify(savedResults)}`);
        } catch (e: any) {
            Logger.error(`UserWatchlistGenesis::bulkCreate::Error saving records. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                records: JSON.stringify(records)
            }));
        }
        return ResponseHelper.success({ savedResults });
    }

    public async create(record: { userId: number; genesisId: number; }): Promise<any | ErrorResponse> {
        let savedResult;
        try {
            const result = await SequelizeModel.create(record);
            savedResult = result.get({ plain: true });
            Logger.debug(`UserWatchlistGenesis::save::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`UserWatchlistGenesis::save::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(["generalError"], { error: e.message }));
        }
        return ResponseHelper.success({ savedResult });
    }

    public async update(values: any, where: WhereOptions) {
        try {
            await SequelizeModel.update(
                values,
                {
                    where,
                },
            );
        } catch (e: any) {
            Logger.error(`UserWatchlistGenesis::update::Error updating record. Exception ${JSON.stringify(e)}`);
        }
    }

    public async deleteByUserAndGenesisId(userId: number, genesisId: number): Promise<any> {
        try {
            const result = await SequelizeModel.destroy({
                where: {
                    userId: userId,
                    genesisId: genesisId
                },
            });
            Logger.debug(`UserWatchlistGenesis::deleteByUserAndGenesisId::Deleted record with userId: ${userId} and genesisId: ${genesisId}`);
            return result;
        } catch (error) {
            Logger.error(`UserWatchlistGenesis::deleteByUserAndGenesisId::Error deleting record. Exception ${JSON.stringify(error)}`);
            throw ResponseHelper.error(['generalError'], { error: (error as Error).message });
        }
    }

    public async deleteByUserAndTokenId(userId: number, tokenId: string): Promise<any> {
        try {
            const result = await SequelizeModel.destroy({
                where: {
                    userId: userId,
                    tokenId: tokenId
                },
            });
            Logger.debug(`UserWatchlistGenesis::deleteByUserAndTokenId::Deleted record with userId: ${userId} and tokenId: ${tokenId}`);
            return result;
        } catch (error) {
            Logger.error(`UserWatchlistGenesis::deleteByUserAndTokenId::Error deleting record. Exception ${JSON.stringify(error)}`);
            throw ResponseHelper.error(['generalError'], { error: (error as Error).message });
        }
    }

    public async getByUserAndGenesisId(userId: number, genesisIds: number[]): Promise<any> {
        try {
            const result = await SequelizeModel.findAll({
                where: {
                    userId: userId,
                    genesisId: {
                        [Op.in]: genesisIds
                    }
                },
                raw: true
            });
            Logger.debug(`UserWatchlistGenesis::getByUserAndGenesisId::result:${JSON.stringify(result)}`);
            return result;
        } catch (error) {
            Logger.error(`UserWatchlistGenesis::getByUserAndGenesisId::Error getting record. Exception ${JSON.stringify(error)}`);
            throw ResponseHelper.error(['generalError'], { error: (error as Error).message });
        }
    }

    public async getWatchlistedGenesisIdsByUserId(userId: number): Promise<number[]> {
        try {
            const results = await SequelizeModel.findAll({
                where: { userId },
                attributes: ['genesisId'],
                raw: true
            });
            const genesisIds = results.map((item: any) => item.genesisId);
            return genesisIds;
        } catch (error) {
            Logger.error(`UserWatchlistGenesis::getWatchlistedGenesisIdsByUserId::Error fetching records. Exception ${JSON.stringify(error)}`);
            return [];
        }
    }

    public async getTokenIdsByUserId(userId: number): Promise<string[]> {
        try {
            const results = await SequelizeModel.findAll({
                where: {
                    userId,
                    tokenId: { [Op.ne]: null }
                },
                attributes: ['tokenId'],
                raw: true
            });
            const tokenIds = results.map((item: any) => item.tokenId);
            return tokenIds;
        } catch (error) {
            Logger.error(`UserWatchlistGenesis::getTokenIdsByUserId::Error fetching records. Exception ${JSON.stringify(error)}`);
            return [];
        }
    }

    public async getByUserIds(userIds: number[]): Promise<Record<number, any[]>> {
        try {
            const watchlists: any[] = await SequelizeModel.findAll({
                where: { userId: { [Op.in]: userIds } },
                raw: true
            });
            const watchlistByUserIds: Record<number, any[]> = {}
            for (const watchlist of watchlists) {
                if (!watchlistByUserIds[watchlist.userId]) watchlistByUserIds[watchlist.userId] = [];
                watchlistByUserIds[watchlist.userId].push(watchlist);
            }
            return watchlistByUserIds;
        } catch (error) {
            Logger.error(`UserWatchlistGenesis::getByUserIds::Error fetching records. Exception ${JSON.stringify(error)}`);
            return {};
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            userId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            genesisId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            tokenId: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW,
            }
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: false,
                fields: ["userId"]
            },
            {
                unique: false,
                fields: ["genesisId"]
            },
            {
                unique: false,
                fields: ["tokenId"]
            },
        ];
    }
}
