import { DataTypes, InitOptions, Model, ModelIndexesOptions, Op, WhereOptions } from "sequelize";
import { SuccessResponse, ErrorResponse, WalletSafetyScoreModelAttributes } from "../lib/Types";
import ResponseHelper from "../lib/helper/ResponseHelper";
import Logger from "../lib/Logger";
import Utils from "../lib/Utils";

class SequelizeModel extends Model {
}

export default class WalletSafetyScore {

    public constructor(initOptions: InitOptions) {
        const oThis = this;
        SequelizeModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: "WalletSafetyScore",
                tableName: "wallet_safety_score",
                indexes: oThis.getIndexes(),
            },
        );
    }

    public async bulkCreate(records: WalletSafetyScoreModelAttributes[]): Promise<SuccessResponse | ErrorResponse> {
        let savedResults;
        try {
            savedResults = await SequelizeModel.bulkCreate(records, {
                updateOnDuplicate: ['safetyScore', 'walletAgePoints', 'walletTxActivityPoints', 'reasoning'],
            });
            Logger.debug(`WalletSafetyScore::bulkCreate::savedResults::${JSON.stringify(savedResults)}`);
        } catch (e: any) {
            Logger.error(`WalletSafetyScore::bulkCreate::Error saving records. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
        return ResponseHelper.success({ savedResults });
    }

    public async create(walletSafetyScoreObject: any): Promise<SuccessResponse | ErrorResponse> {
        let savedResult;
        try {
            savedResult = await SequelizeModel.create(walletSafetyScoreObject);
            Logger.debug(`WalletSafetyScore::create::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`WalletSafetyScore::create::Error saving wallet safety score record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputWalletSafetyScore: JSON.stringify(walletSafetyScoreObject)
            }));
        }
        return ResponseHelper.success({ walletSafetyScore: savedResult });
    }

    public async update(values: any, where: WhereOptions,) {
        const updateResult: any = await SequelizeModel.update(
            values,
            {
                where
            }
        );
        if (updateResult.length === 0 || updateResult[0] < 1) {
            Promise.reject(ResponseHelper.error(['generalError'], {
                error: 'Update operation failed in wallet safety score model',
                values: JSON.stringify(values),
                WhereOptions: JSON.stringify(where)
            }));
        }
    }

    public async getByGenesisId(id: number): Promise<any> {
        const oThis = this;
        const walletSafetyScore = await SequelizeModel.findOne({
            where: {
                genesis_id: id
            }
        });
        Logger.debug(`WalletSafetyScore::getByGenesisId::walletSafetyScore:${JSON.stringify(walletSafetyScore)}`);
        return walletSafetyScore;
    }

    public async getByGenesisAddress(address: string): Promise<any> {
        const oThis = this;
        const walletSafetyScore = await SequelizeModel.findOne({
            where: {
                genesis_address: address
            }
        });
        Logger.debug(`WalletSafetyScore::getByGenesisAddress::walletSafetyScore:${JSON.stringify(walletSafetyScore)}`);
        return walletSafetyScore;
    }

    public async getByGenesisAddresses(genesisAddresses: string[]): Promise<Record<string, any[]>> {
        try {
            const walletSafetyScores: any[] = await SequelizeModel.findAll({
                where: { genesis_address: { [Op.in]: genesisAddresses } },
                raw: true
            });
            const walletSafetyScoreByGenesisAddresses: Record<string, any[]> = {}
            for (const walletSafetyScore of walletSafetyScores) {
                if (!walletSafetyScoreByGenesisAddresses[walletSafetyScore.genesisAddress]) walletSafetyScoreByGenesisAddresses[walletSafetyScore.genesisAddress] = [];
                walletSafetyScoreByGenesisAddresses[walletSafetyScore.genesisAddress].push(walletSafetyScore);
            }
            Logger.debug(`WalletSafetyScore::getByGenesisAddresses::Wallet safety score by genesis addresses map: ${JSON.stringify(walletSafetyScoreByGenesisAddresses)}`);
            return walletSafetyScoreByGenesisAddresses;
        } catch (error: any) {
            Logger.error(`WalletSafetyScore::getByGenesisAddresses::Error fetching records. Exception ${JSON.stringify(error.message)}`);
            return {};
        }
    }

    private getSchema() {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            genesisId: {
                type: DataTypes.INTEGER,
                allowNull: false,
            },
            genesisAddress: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            memberWalletAddress: {
                type: DataTypes.STRING,
                allowNull: false,
            },
            walletTitle: {
                type: DataTypes.STRING,
                allowNull: true,
            },
            safetyScore: {
                type: DataTypes.SMALLINT,
                allowNull: true,
            },
            walletAgePoints: {
                type: DataTypes.SMALLINT,
                allowNull: true,
            },
            walletTxActivityPoints: {
                type: DataTypes.SMALLINT,
                allowNull: true,
            },
            createdAt: {
                type: DataTypes.DATE,
            },
            updatedAt: {
                type: DataTypes.DATE,
            },
            reasoning: {
                type: DataTypes.STRING,
                allowNull: true,
            },
        };
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ["genesis_address", "member_wallet_address"],
            },
            {
                unique: false,
                fields: ["genesis_id"],
            },
        ];
    }
}
