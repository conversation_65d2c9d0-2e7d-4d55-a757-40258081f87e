import { DataTypes, InitOptions, Model, Op, WhereOptions } from 'sequelize';
import Logger from '../lib/Logger';
import Sequelize from 'sequelize';
import { ErrorResponse, SuccessResponse } from '../lib/Types';
import ResponseHelper from '../lib/helper/ResponseHelper';

class SequelizeModel extends Model {
}

export default class GenesisRawData {
    public constructor(initOptions: InitOptions) {
        SequelizeModel.init(
            this.getSchema(),
            {
                ...initOptions,
                modelName: 'GenesisRawData',
                tableName: 'genesis_raw_data',
                indexes: this.getIndexes(),
            },
        );
    }

    public async create(record: any): Promise<SuccessResponse | ErrorResponse> {
        let savedResult;
        try {
            savedResult = await SequelizeModel.create(
                record
            );
            Logger.debug(`GenesisRawData::save::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`GenesisRawData::save::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
        return ResponseHelper.success({});
    }

    public async bulkCreate(records: any[]): Promise<SuccessResponse | ErrorResponse> {
        try {
            await SequelizeModel.bulkCreate(records, {
                updateOnDuplicate: [
                    'name',
                    'token',
                    'genesisAddress',
                    'chain',
                    'image',
                    'description',
                    'result',
                    'tokenAddress',
                    'tokenomics',
                    'hasUnlocked',
                    'daysFromFirstUnlock',
                    'virtualsAppId',
                    'isVerified',
                    'updatedAt'
                ]
            });
            Logger.debug(`GenesisRawData::bulkCreate::Successfully upserted ${records.length} records`);
            return ResponseHelper.success({});
        } catch (e: any) {
            Logger.error(`GenesisRawData::bulkCreate::Error bulk inserting records. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
    }

    public async update(values: any, where: WhereOptions,) {
        try {
            const updateResult: any = await SequelizeModel.update(
                values,
                {
                    where
                }
            );
            if (updateResult.length === 0 || updateResult[0] < 1) {
                Promise.reject(ResponseHelper.error(['generalError'], {
                    error: 'Update operation failed',
                    values: JSON.stringify(values),
                    WhereOptions: JSON.stringify(where)
                }));
            }
        } catch (e: any) {
            Logger.error(`GenesisRawData::update::Error updating record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputValues: JSON.stringify(values),
                inputWhere: JSON.stringify(where)
            }));
        }
    }

    public async getEmptyTokenInfoTokens(): Promise<any[]> {
        try {
            const tokens = await SequelizeModel.findAll({
                attributes: ['genesisId', 'tokenAddress'],
                where: {
                    tokenAddress: {
                        [Op.ne]: null
                    },
                    [Op.and]: [
                        { virtualId: null },
                        { handle: null }
                    ]
                },
                raw: true
            });
            Logger.info(`GenesisRawData::getEmptyTokenInfoTokens::Found ${tokens.length} tokens needing additional info`);
            Logger.debug(`GenesisRawData::getEmptyTokenInfoTokens::Tokens: ${JSON.stringify(tokens)}`);
            return tokens;
        } catch (e: any) {
            Logger.error(`GenesisRawData::getEmptyTokenInfoTokens::Error fetching tokens. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
    }

    public async getTokenDescription(genesisId: number): Promise<string | null> {
        try {
            const tokenDescription: any = await SequelizeModel.findOne({
                attributes: ['description'],
                where: { genesisId }
            });
            return tokenDescription?.description;
        } catch (e: any) {
            Logger.error(`GenesisRawData::getTokenDescription::Error fetching token description. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
    }

    private getSchema() {
        return {
            token: {
                type: DataTypes.STRING(80),
                allowNull: false,
            },
            genesisId: {
                type: Sequelize.INTEGER,
                primaryKey: true,
                allowNull: false
            },
            virtualsAppId: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            name: {
                type: DataTypes.STRING(200),
                allowNull: true,
            },
            genesisAddress: {
                type: DataTypes.STRING(80),
                allowNull: false,
                unique: true
            },
            tokenAddress: {
                type: DataTypes.STRING(80),
                allowNull: true,
                unique: true
            },
            result: {
                type: DataTypes.JSON,
                allowNull: true
            },
            chain: {
                type: DataTypes.SMALLINT,
                allowNull: true
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            image: {
                type: DataTypes.STRING(255),
                allowNull: true
            },
            tokenomics: {
                type: DataTypes.JSON,
                allowNull: true
            },
            hasUnlocked: {
                type: DataTypes.BOOLEAN,
                allowNull: true
            },
            daysFromFirstUnlock: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            virtualId: {
                type: DataTypes.INTEGER,
                allowNull: true,
                unique: true
            },
            handle: {
                type: DataTypes.JSON,
                allowNull: true
            },
            isVerified: {
                type: DataTypes.BOOLEAN,
                allowNull: true
            },
            createdAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            },
            updatedAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            }
        }
    }

    private getIndexes() {
        return [
            {
                fields: ['token']
            },
            {
                fields: ['updated_at']
            }
        ];
    }
}
