
import { DataTypes, InitOptions, ModelIndexesOptions, Op, WhereOptions, Sequelize } from "sequelize";
import Logger from "../lib/Logger";
import { Model } from "sequelize";
import ResponseHelper from "../lib/helper/ResponseHelper";
import { SuccessResponse, ErrorResponse, TokenCandleDataObject } from "../lib/Types";

class SequelizeModel extends Model {
}

export default class EthereumCandleData {

    public constructor(initOptions: InitOptions) {
        const oThis = this;
        SequelizeModel.init(
            oThis.getSchema(),
            {
                ...initOptions,
                modelName: "EthereumCandleData",
                tableName: "ethereum_candle_data",
                indexes: oThis.getIndexes(),
                timestamps: false
            },
        );
    }

    public async create(tokenCandleDataObject: TokenCandleDataObject): Promise<SuccessResponse | ErrorResponse> {
        let savedResult;
        try {
            savedResult = await SequelizeModel.create(tokenCandleDataObject as any);
            Logger.debug(`EthereumCandleData::create::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`EthereumCandleData::create::Error saving token candle data record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputTokenCandleData: JSON.stringify(tokenCandleDataObject)
            }));
        }
        return ResponseHelper.success({ tokenCandleData: savedResult });
    }

    public async update(values: any, where: WhereOptions,) {
        try {
            const updateResult: any = await SequelizeModel.update(
                values,
                {
                    where
                }
            );
            if (updateResult.length === 0 || updateResult[0] < 1) {
                Promise.reject(ResponseHelper.error(['generalError'], {
                    error: 'Update operation failed in model',
                    values: JSON.stringify(values),
                    WhereOptions: JSON.stringify(where)
                }));
            }
        } catch (e: any) {
            Logger.error(`EthereumCandleData::update::Error updating token candle data record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputTokenCandleData: JSON.stringify(values),
                where: JSON.stringify(where)
            }));
        }
    }

    public async bulkCreate(tokenCandleDataObjects: any[]): Promise<SuccessResponse | ErrorResponse> {
        let savedResults;
        try {
            savedResults = await SequelizeModel.bulkCreate(tokenCandleDataObjects, {
                ignoreDuplicates: true,
            });
            Logger.debug(`EthereumCandleData::bulkCreate::savedResults::${JSON.stringify(savedResults)}`);
        } catch (e: any) {
            Logger.error(`EthereumCandleData::bulkCreate::Error saving token candle data records. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputTokenCandleData: JSON.stringify(tokenCandleDataObjects)
            }));
        }
        return ResponseHelper.success({ tokenCandleData: savedResults });
    }

    public async getExistingTokenIds(): Promise<any> {
        try {
            const tokenIds = await SequelizeModel.findAll({
                attributes: ['tokenId'],
                group: ['tokenId']
            });
            return tokenIds.map((token: any) => token.tokenId);
        } catch (e: any) {
            Logger.error(`EthereumCandleData::getExistingTokenIds::Error fetching existing token ids. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message
            }));
        }
    }

    public async getLastNCandlesByTokenId(tokenId: string, limit: number): Promise<any> {
        try {
            const tokenData = await SequelizeModel.findAll({
                where: {
                    tokenId: tokenId
                },
                order: [['timestamp', 'DESC']],
                limit: limit
            });
            return tokenData.map((item: any) => item.dataValues);
        } catch (e: any) {
            Logger.error(`EthereumCandleData::getLastNCandlesByTokenId::Error fetching last n candles by token id. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputTokenId: tokenId,
                inputLimit: limit
            }));
        }
    }

    public async getExistingTokenIdsWithLastTimestamp(): Promise<{ tokenId: string, lastTimestamp: Date }[]> {
        try {
            const result = await SequelizeModel.findAll({
                attributes: [
                    'tokenId',
                    [Sequelize.fn('MAX', Sequelize.col('timestamp')), 'lastTimestamp']
                ],
                group: ['tokenId'],
                raw: true
            });
            return result.map((item: any) => ({
                tokenId: item.tokenId,
                lastTimestamp: item.lastTimestamp
            }));
        } catch (e: any) {
            Logger.error(`EthereumCandleData::getExistingTokenIdsWithLastTimestamp::Error fetching token ids with timestamps. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message
            }));
        }
    }

    public async delete(where: WhereOptions) {
        const deleteResult: any = await SequelizeModel.destroy(
            {
                where,
            },
        );
        return deleteResult;
    }

    private getSchema(): any {
        return {
            id: {
                type: DataTypes.INTEGER,
                autoIncrement: true,
                primaryKey: true,
            },
            tokenId: {
                type: DataTypes.STRING(80),
                allowNull: false
            },
            timestamp: {
                type: DataTypes.DATE,
                allowNull: false
            },
            open: {
                type: DataTypes.DECIMAL(25, 10),
                allowNull: false
            },
            high: {
                type: DataTypes.DECIMAL(25, 10),
                allowNull: false
            },
            low: {
                type: DataTypes.DECIMAL(25, 10),
                allowNull: false
            },
            close: {
                type: DataTypes.DECIMAL(25, 10),
                allowNull: false
            },
            volume: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: false
            }
        }
    }

    private getIndexes(): ModelIndexesOptions[] {
        return [
            {
                unique: true,
                fields: ["tokenId", "timestamp"]
            },
        ];
    }
}
