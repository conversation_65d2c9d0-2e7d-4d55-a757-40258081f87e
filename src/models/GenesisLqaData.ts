import { DataTypes, InitOptions, Model, Op, WhereOptions } from 'sequelize';
import Logger from '../lib/Logger';
import Sequelize from 'sequelize';
import { ErrorResponse, SuccessResponse } from '../lib/Types';
import ResponseHelper from '../lib/helper/ResponseHelper';

class SequelizeModel extends Model {
}

export default class GenesisLqaData {
    public constructor(initOptions: InitOptions) {
        SequelizeModel.init(
            this.getSchema(),
            {
                ...initOptions,
                modelName: 'GenesisLqaData',
                tableName: 'genesis_lqa_data',
                indexes: this.getIndexes(),
                timestamps: true
            },
        );
    }

    public async create(record: any): Promise<SuccessResponse | ErrorResponse> {
        let savedResult;
        try {
            savedResult = await SequelizeModel.create(
                record,
            );
            Logger.debug(`GenesisTokenDetails::save::savedResult::${JSON.stringify(savedResult)}`);
        } catch (e: any) {
            Logger.error(`GenesisTokenDetails::save::Error saving record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
        return ResponseHelper.success({});
    }

    public async update(values: any, where: WhereOptions,) {
        try {
            const updateResult: any = await SequelizeModel.update(
                values,
                {
                    where
                }
            );
            if (updateResult.length === 0 || updateResult[0] < 1) {
                Promise.reject(ResponseHelper.error(['generalError'], {
                    error: 'Update operation failed',
                    values: JSON.stringify(values),
                    WhereOptions: JSON.stringify(where)
                }));
            }
        } catch (e: any) {
            Logger.error(`GenesisTokenMetrics::update::Error updating record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputValues: JSON.stringify(values),
                inputWhere: JSON.stringify(where)
            }));
        }
    }

    public async bulkCreate(records: any[]): Promise<SuccessResponse | ErrorResponse> {
        try {
            const savedResults = await SequelizeModel.bulkCreate(records, {
                updateOnDuplicate: [
                    'name',
                    'token',
                    'genesisAddress',
                    'tokenAddress',
                    'chain',
                    'image',
                    'status',
                    'startDate',
                    'endDate',
                    'hasUnlocked',
                    'daysFromFirstUnlock',
                    'virtualsAppId',
                    'participants',
                    'pointsPledged',
                    'committedVirtuals',
                    'isVerified',
                    'updatedAt'
                ]
            });
            Logger.debug(`GenesisTokenMetrics::bulkCreate::Upserted ${savedResults.length} records`);
            return ResponseHelper.success({});
        } catch (e: any) {
            Logger.error(`GenesisTokenMetrics::bulkCreate::Error in bulk upsert. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
    }

    public async updateByGenesisId(values: any, genesisIds: number[]) {
        try {
            const updateResult: any = await SequelizeModel.update(
                values,
                {
                    where: {
                        genesisId: { [Op.in]: genesisIds }
                    }
                }
            );
            if (updateResult.length === 0 || updateResult[0] < 1) {
                Promise.reject(ResponseHelper.error(['generalError'], {
                    error: 'Update operation failed',
                    values: JSON.stringify(values),
                    WhereOptions: JSON.stringify({ genesisId: genesisIds })
                }));
            }
        } catch (e: any) {
            Logger.error(`GenesisLqaData::updateByGenesisId::Error updating record. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
                inputValues: JSON.stringify(values),
                inputWhere: JSON.stringify({ genesisId: genesisIds })
            }));
        }
    }

    public async getTokensByStatus(status: number[]): Promise<any> {
        try {
            const tokens = await SequelizeModel.findAll({
                attributes: ['genesisId', 'genesisAddress', 'tokenAddress', 'virtualsPriceOnLaunch', 'startDate', 'endDate', 'marketCap', 'status', 'roi', 'token'],
                where: {
                    status: {
                        [Op.in]: status
                    }
                },
                raw: true
            });
            Logger.debug(`GenesisTokenMetrics::getTokensByStatus::Tokens: ${JSON.stringify(tokens)}`)
            return tokens;
        } catch (e: any) {
            Logger.error(`GenesisTokenMetrics::getTokensByStatus::Error fetching tokens. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message
            }));
        }
    }

    public async getTokensByStatusAndDateRange(status: number[], startDate: Date, endDate: Date): Promise<any> {
        try {
            const tokens = await SequelizeModel.findAll({
                attributes: ['genesisId', 'genesisAddress', 'tokenAddress', 'virtualsPriceOnLaunch', 'startDate', 'endDate', 'marketCap', 'status', 'roi', 'token'],
                where: {
                    status: {
                        [Op.in]: status
                    },
                    endDate: {
                        [Op.gte]: startDate,
                        [Op.lte]: endDate
                    }
                },
                raw: true
            });
            Logger.debug(`GenesisLqaData::getTokensByStatusAndDateRange::Found ${tokens.length} tokens with status ${status} and endDate between ${startDate} and ${endDate}`);
            return tokens;
        } catch (e: any) {
            Logger.error(`GenesisLqaData::getTokensByStatusAndDateRange::Error fetching tokens. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message
            }));
        }
    }

    public async getByGenesisIds(genesisIds: string[] | number[]): Promise<any> {
        try {
            const genesisLqaData = await SequelizeModel.findAll({
                where: {
                    genesisId: { [Op.in]: genesisIds }
                },
                raw: true
            });
            Logger.debug(`GenesisLqaData::getByGenesisIds::genesisLqaData:${JSON.stringify(genesisLqaData)}`);
            return genesisLqaData;
        } catch (e: any) {
            Logger.error(`GenesisLqaData::getByGenesisIds::Error fetching genesis lqa data. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message
            }));
        }
    }

    public async getByGenesisAddresses(genesisAddresses: string[]): Promise<any> {
        try {
            const genesisLqaData = await SequelizeModel.findAll({
                where: {
                    genesisAddress: { [Op.in]: genesisAddresses.map(addr => addr.toLowerCase()) }
                },
                raw: true
            });
            Logger.debug(`GenesisLqaData::getByGenesisAddresses::genesisLqaData:${JSON.stringify(genesisLqaData)}`);
            return genesisLqaData;
        } catch (e: any) {
            Logger.error(`GenesisLqaData::getByGenesisAddresses::Error fetching genesis lqa data. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message
            }));
        }
    }

    public async getByTicker(ticker: string): Promise<any> {
        const tokenData = await SequelizeModel.findOne({
            where: {
                token: { [Op.iLike]: ticker }
            },
            order: [
                [Sequelize.literal('market_cap IS NULL'), 'ASC'],
                ['marketCap', 'DESC'],
                ['participants', 'DESC']
            ],
            raw: true
        });
        Logger.debug(`GenesisLqaData::getByTicker::tokenData:${JSON.stringify(tokenData)}`);
        return tokenData || null;
    }

    public async getByTokenAddress(tokenAddress: string): Promise<any> {
        const tokenData = await SequelizeModel.findOne({
            where: {
                tokenAddress: { [Op.iLike]: tokenAddress }
            },
            order: [
                [Sequelize.literal('market_cap IS NULL'), 'ASC'],
                ['marketCap', 'DESC'],
                ['participants', 'DESC']
            ],
            raw: true
        });
        Logger.debug(`GenesisLqaData::getByTokenAddress::tokenData:${JSON.stringify(tokenData)}`);
        return tokenData || null;
    }

    public async getSortedGenesisIds(limit: number, skip: number, search?: string, sortColumn: string | any[] = 'updatedAt', sortDirection: string | null = 'desc', statusFilter?: string[], watchlistedGenesisIds?: number[]): Promise<{
        genesisIds: string[],
        count: number
    }> {
        try {
            const whereCondition: any = {
                ...(search ? {
                    [Op.or]: [
                        { token: { [Op.iLike]: `%${search}%` } },
                        { name: { [Op.iLike]: `%${search}%` } },
                        { tokenAddress: { [Op.iLike]: `%${search}%` } }
                    ]
                } : {}),
                ...(statusFilter && statusFilter.length > 0 ? {
                    status: {
                        [Op.in]: statusFilter
                    }
                } : {})
            };

            // Add filtering by watchlisted genesis token IDs if provided
            if (watchlistedGenesisIds) {
                whereCondition.genesisId = { [Op.in]: watchlistedGenesisIds };
            }

            let orderOptions: any = [];
            if (Array.isArray(sortColumn)) {
                // For custom ordering with array of sort specifications

                for (const item of sortColumn) {
                    if (typeof item === 'string' && item.includes(' ')) {
                        // Handle 'column direction NULLS LAST' format
                        orderOptions.push(Sequelize.literal(item));
                    } else {
                        // Handle Sequelize.literal or other formats directly
                        orderOptions.push(item);
                    }
                }
            }
            else {
                // For regular sorting with column name and direction
                orderOptions = [
                    [sortColumn, sortDirection, Sequelize.literal(' NULLS LAST')],
                    ['updatedAt', 'DESC']
                ];
            }

            Logger.debug(`GenesisLqaData::getSortedGenesisIds::orderOptions:${JSON.stringify(orderOptions)}`);

            const genesisLqaData = await SequelizeModel.findAndCountAll({
                attributes: ['genesisId'],
                where: whereCondition,
                order: orderOptions,
                limit: limit,
                offset: skip,
                raw: true
            });

            const rows: any = genesisLqaData?.rows || [];
            const count = genesisLqaData?.count || 0;
            const genesisIds = rows.map((row: { genesisId: string; }) => row.genesisId);
            Logger.debug(`GenesisLqaData::getSortedGenesisIds::genesisIds:${JSON.stringify(genesisIds)}`);
            return { genesisIds, count };
        } catch (e: any) {
            Logger.error(`GenesisLqaData::getSortedGenesisIds::Error getting genesis ids. Exception ${JSON.stringify(e.message)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], {
                error: e.message,
            }));
        }
    }

    public async bulkUpsert(records: any[], updateOnDuplicate: string[]): Promise<SuccessResponse | ErrorResponse> {
        try {
            const savedResults = await SequelizeModel.bulkCreate(records, {
                updateOnDuplicate: [...updateOnDuplicate, 'updatedAt']
            });
            Logger.debug(`GenesisLqaData::bulkUpsert::Upserted ${savedResults.length} records`);
            return ResponseHelper.success({});
        } catch (e: any) {
            Logger.error(`GenesisLqaData::bulkUpsert::Error in bulk upsert. Exception ${JSON.stringify(e)}`);
            return Promise.reject(ResponseHelper.error(['generalError'], { error: e.message }));
        }
    }

    private getSchema() {
        return {
            token: {
                type: DataTypes.STRING(80),
                allowNull: false,
            },
            genesisId: {
                type: Sequelize.INTEGER,
                primaryKey: true,
                allowNull: false,
            },
            virtualsAppId: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            name: {
                type: DataTypes.STRING(200),
                allowNull: true,
            },
            genesisAddress: {
                type: DataTypes.STRING(80),
                allowNull: false,
                unique: true
            },
            tokenAddress: {
                type: DataTypes.STRING(80),
                allowNull: true,
                unique: true
            },
            tokenId: {
                type: DataTypes.STRING(80),
                allowNull: true,
                unique: true
            },
            chain: {
                type: DataTypes.SMALLINT,
                allowNull: true
            },
            image: {
                type: DataTypes.STRING(255),
                allowNull: true
            },
            status: {
                type: DataTypes.SMALLINT,
                allowNull: true
            },
            startDate: {
                type: DataTypes.DATE,
                allowNull: true
            },
            endDate: {
                type: DataTypes.DATE,
                allowNull: true
            },
            hasUnlocked: {
                type: DataTypes.BOOLEAN,
                allowNull: true
            },
            daysFromFirstUnlock: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            virtualsPriceOnLaunch: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            marketCap: {
                type: DataTypes.DECIMAL(20, 2),
                allowNull: true
            },
            participants: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            pointsPledged: {
                type: DataTypes.DECIMAL(25, 2),
                allowNull: true
            },
            committedVirtuals: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            committedPercentage: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            tpp: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            maxAllocation: {
                type: DataTypes.DECIMAL(25, 5),
                allowNull: true
            },
            roi: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            roiAth: {
                type: DataTypes.DECIMAL(20, 5),
                allowNull: true
            },
            isVerified: {
                type: DataTypes.BOOLEAN,
                allowNull: true
            },
            updatedAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            },
            createdAt: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            }
        }
    }

    private getIndexes() {
        return [
            {
                fields: ['token']
            }
        ];
    }
}
