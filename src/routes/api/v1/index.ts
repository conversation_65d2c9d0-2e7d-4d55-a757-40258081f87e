import express, { Request, Response } from 'express';
import ResponseHelper from '../../../lib/helper/ResponseHelper';
import APIName from '../../../lib/constant/APIName';
import Logger from '../../../lib/Logger';
import GetTokenMetainfo from '../../../services/apis/GetTokenMetainfo';
import GetTokens from '../../../services/apis/GetTokens';
import GetAskLokyResponse from '../../../services/apis/GetAskLokyResponse';
import GetCryptoMetainfo from '../../../services/apis/GetCryptoMetainfo';
import GetCryptoMarket from '../../../services/apis/GetCryptoMarket';
import GetCryptoTechnicalAnalysis from '../../../services/apis/GetCryptoTechnicalAnalysis';
import GetTrendingTokens from '../../../services/apis/GetTrendingTokens';
import GetBubbleMapService from '../../../services/GetBubbleMapService';
import GetTokenDetailsService from '../../../services/GetTokenDetailsService';
import GetMarketChart from '../../../services/apis/GetMarketChart';
import GetWalletSafetyScore from '../../../services/apis/GetWalletSafetyScore';

const router = express.Router();

/**
 * ⚠️ DEVELOPER NOTE:
 *
 * For every API route, you MUST attach `apiSignatureParams` to the request object.
 *
 * These signatures define required and optional parameters and are used for validation.
 *
 * ✅ Reference: `APIParamsSignature.ts`
 *
 * 🔍 Missing `apiSignatureParams` can cause validation to fail or allow bad data through.
 */

router.get('/agent-metainfo',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.agentMetainfo;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse: any = await new GetTokenMetainfo(requestQuery).perform();
        Logger.debug(`routes::api::v1::agent-metainfo::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateApiLog(serviceResponse);

        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/agents',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.agents;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse: any = await new GetTokens(requestQuery).perform();
        Logger.debug(`routes::api::v1::agents::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateMeta(serviceResponse);
        await ResponseHelper.updateApiLog(serviceResponse);

        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.post('/ask-loky',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.askLoky;
        const requestBody = req.body as any;
        Object.assign(requestBody, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestBody, { hostName: req.hostname });
        const serviceResponse: any = await new GetAskLokyResponse(requestBody).perform();
        Logger.debug(`routes::api::v1::ask-loky::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateApiLog(serviceResponse);

        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/crypto-metainfo',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.cryptoMetainfo;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse: any = await new GetCryptoMetainfo(requestQuery).perform();
        Logger.debug(`routes::api::v1::crypto-metainfo::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateApiLog(serviceResponse);

        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/crypto-market',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.cryptoMarket;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse: any = await new GetCryptoMarket(requestQuery).perform();
        Logger.debug(`routes::api::v1::crypto-market::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateMeta(serviceResponse);
        await ResponseHelper.updateApiLog(serviceResponse);

        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/token-ta',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.tokenTA;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse: any = await new GetCryptoTechnicalAnalysis(requestQuery).perform();
        Logger.debug(`routes::api::v1::token-ta::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateApiLog(serviceResponse);

        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/trending',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.trendingTokens;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse: any = await new GetTrendingTokens(requestQuery).perform();
        Logger.debug(`routes::api::v1::trending::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateApiLog(serviceResponse);
        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/bubble-map',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.bubbleMap;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse = await new GetBubbleMapService(requestQuery).perform();
        Logger.debug(`routes::api::v1::bubble-map::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/agent-metrics',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.agentMetrics;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse = await new GetTokenDetailsService(requestQuery).perform();
        Logger.debug(`routes::api::v1::agent-metrics::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/crypto-market-historical',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.cryptoMarketHistorical;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse: any = await new GetMarketChart(requestQuery).perform();
        Logger.debug(`routes::api::v1::crypto-market-historical::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateApiLog(serviceResponse);
        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

router.get('/wallet-safety-score',
    async (req: Request, res: Response) => {
        req.internalDecodedParams.apiName = APIName.walletSafetyScore;
        const requestQuery = req.query as any;
        Object.assign(requestQuery, { internalDecodedParams: req.internalDecodedParams });
        Object.assign(requestQuery, { hostName: req.hostname });
        const serviceResponse: any = await new GetWalletSafetyScore(requestQuery).perform();
        Logger.debug(`routes::api::v1::wallet-safety-score::serviceResponse: ${JSON.stringify(serviceResponse)}`);
        await ResponseHelper.updateApiLog(serviceResponse);
        const statusCode = serviceResponse.statusCode || 200;
        delete serviceResponse.statusCode;
        return ResponseHelper.renderWithStatus(serviceResponse, res, statusCode);
    }
)

export default router;
