import Logger from '../Logger';
import GeneralConstant from '../../config/GeneralConstant';

/**
 * Class for basic helper.
 *
 * @class BasicHelper
 */
class BasicHelper {
    /**
     * Log date format.
     *
     * @returns {string}
     */
    logDateFormat(): string {
        const date = new Date();

        return (
            `${date.getFullYear()
            }-${date.getMonth() + 1
            }-${date.getDate()
            } ${date.getHours()
            }:${date.getMinutes()
            }:${date.getSeconds()
            }.${date.getMilliseconds()}`
        );
    }

    /**
     * Get current timestamp in seconds.
     *
     * @return {number}
     */
    getCurrentTimestampInSeconds(): number {
        return Math.floor(new Date().getTime() / 1000);
    }

    /**
     * Get current timestamp in minutes.
     *
     * @return {number}
     */
    getCurrentTimestampInMinutes(): number {
        return Math.floor(new Date().getTime() / (60 * 1000));
    }

    toTimestamp(date: Date): number {
        return ((new Date(date)).getTime() / 1000);
    }

    /**
     * Check if environment is production.
     *
     * @return {boolean}
     */
    isProduction(): boolean {
        return GeneralConstant.environment === 'production';
    }

    /**
     * Check if environment is staging.
     *
     * @return {boolean}
     */
    isStaging(): boolean {
        return GeneralConstant.environment === 'staging';
    }

    /**
     * Check if environment is development.
     *
     * @return {boolean}
     */
    isDevelopment(): boolean {
        return GeneralConstant.environment === 'development';
    }

    /**
     * Convert a common separated string to array.
     *
     * @param {string} str
     *
     * @return {array}
     */
    commaSeparatedStrToArray(str: string): string[] {
        return str.split(',').map(ele => ele.trim());
    }

    /**
     * Gives random alphanumeric string
     *
     * @returns {string}
     */
    getRandomAlphaNumericString(): string {
        return (
            Date.now()
                .toString(36)
                .substring(2, 15)
            + Math.random()
                .toString(36)
                .substring(2, 15)
        );
    }

    /**
     * Sleep for particular time.
     *
     * @param {number} ms: time in ms
     *
     * @returns {Promise<any>}
     */
    sleep(ms: number): Promise<any> {
        // eslint-disable-next-line no-console
        Logger.info(`Basic::sleep:Sleeping for ${ms} ms.`);

        return new Promise(((resolve) => {
            setTimeout(resolve, ms);
        }));
    }

    /**
     * Capitalize first character of field name.
     *
     * @param {string} str: time in ms
     *
     * @returns {string}
     */
    capitalizeFirstLetter(str: string): string {
        return str[0].toUpperCase() + str.substr(1);
    }

    /**
     * Checks whether the object is empty or not.
     *
     * @param {object} obj
     *
     * @return {boolean}
     */
    isEmptyObject(obj: object) {
        for (const property in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, property)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Converts input string to a permalink.
     *
     * @param {string} str
     *
     * @return {string}
     */
    getPermalinkFromString(str: string) {
        let permalink = str.trim() // Remove surrounding whitespace.
            .toLowerCase() // Lowercase.
            .replace(/[^a-z0-9]+/g, '-') // Find everything that is not a lowercase letter or number, one or more times, globally, and replace it with a dash.
            .replace(/^-+/, '') // Remove all dashes from the beginning of the string.
            .replace(/-+$/, ''); // Remove all dashes from the end of the string.

        return permalink;
    }

    /**
     * Find and get most frequent element of array
     *
     * @param {any[]} arr
     *
     * @return {string}
     */
    getMostFrequentArrayElement(arr: any[]) {
        const hashmap = arr.reduce((acc, val) => {
            acc[val] = (acc[val] || 0) + 1;
            return acc;
        }, {});
        return Object.keys(hashmap).reduce((a, b) => hashmap[a] > hashmap[b] ? a : b);
    }

    /**
     * Remove null elements in array
     *
     * @param {any[]} arr
     *
     * @return {arr[]}
     */
    removeNullElementInArray(arr: any[]): any[] {
        const filteredArray = arr.filter((element) => {
            return element !== null;
        }) as any[];
        return filteredArray;
    }

    /**
     * Convert a common separated string to array.
     *
     * @param {string} str
     *
     * @return {array}
     */
    commaSeparatedStrToNumberArray(str: string): number[] {
        return str.split(',').map(ele => parseInt(ele.trim()));
    }
}

export default new BasicHelper();

