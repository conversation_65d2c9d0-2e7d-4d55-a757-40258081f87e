import VirtualTokenDetailsByTokenAddresses from "../cache/VirtualTokenDetailsByTokenAddresses";
import AIAgentConstant from "../constant/AIAgentConstant";
import SignalConstant from "../constant/SignalConstant";
import TokenConstant from "../constant/TokenConstants";
import Utils from "../Utils";
import Logger from "../Logger";
import { SignalModelAttributes, TelegramMessageAttributes } from "../Types";
import Signal from "../../models/Signal";

export default class GenesisSignalHelper {

    static async checkIfSignalExists(tokenData: any, signalModel: Signal, signalType: string): Promise<boolean> {
        const category = TokenConstant.invertedTokenCategoryIdToStrId[TokenConstant.virtualGenesisCategoryStrId];
        const subCategory = SignalConstant.invertedSubCategoryIdToStrId[signalType];
        const network = tokenData.chain;

        return await signalModel.doesSignalExist(
            `genesisId-${tokenData.genesisId.toString()}`,
            category,
            subCategory,
            network
        );
    }

    static createGenesisSignalAndTelegramObjects(tokenData: any, signalText: string, signalType: string, includeTokenAddress: boolean = false): { signalObject: SignalModelAttributes, telegramObject: TelegramMessageAttributes } {
        const category = TokenConstant.invertedTokenCategoryIdToStrId[TokenConstant.virtualGenesisCategoryStrId];
        const subCategory = SignalConstant.invertedSubCategoryIdToStrId[signalType];
        const network = tokenData.chain;

        const signalObject: SignalModelAttributes = {
            text: signalText,
            category,
            subCategory,
            tokenIds: [`genesisId-${tokenData.genesisId.toString()}`],
            network
        };

        const telegramObject: TelegramMessageAttributes = {
            tokenTicker: tokenData.token,
            telegramText: signalText,
            network: AIAgentConstant.networkMap[network],
            ...(includeTokenAddress && { tokenAddress: tokenData.tokenAddress || '' }),
            createdAt: new Date().toISOString(),
            virtualsAppId: tokenData.virtualsAppId,
            tokenImage: tokenData.image || '',
            addLokyDocsButton: true,
            addLokyXButton: true,
            addLokyHomePageButton: true
        };

        Logger.debug(`GenesisSignalHelper::createGenesisSignalAndTelegramObjects::${signalType}::signalObject: ${JSON.stringify(signalObject)}`);
        Logger.debug(`GenesisSignalHelper::createGenesisSignalAndTelegramObjects::${signalType}::telegramObject: ${JSON.stringify(telegramObject)}`);

        return { signalObject, telegramObject };
    }

    static async getMarketData(tokenAddress: string): Promise<any | null> {
        try {
            const tokenDetailsByTokenAddressesCache = await new VirtualTokenDetailsByTokenAddresses({
                tokenAddresses: [tokenAddress]
            }).fetch();

            const tokenDetails = tokenDetailsByTokenAddressesCache.data || {};
            const marketData = tokenDetails[tokenAddress.toLowerCase()];

            if (!marketData || Utils.isObjectEmpty(marketData)) {
                Logger.warn(`GenesisSignalHelper::getMarketData::No market data found for token ${tokenAddress}`);
                return null;
            }

            return marketData;
        } catch (error: any) {
            Logger.error(`GenesisSignalHelper::getMarketData::Error fetching market data for token ${tokenAddress}: ${error.message}`);
            return null;
        }
    }

    static async getParticipantAnalysis(tokenAddress: string): Promise<any | null> {
        try {
            const endpoint = AIAgentConstant.virtualsGenesisParticipantAnalysisByTokenAddressEndpointDL(tokenAddress);
            const result = await fetch(endpoint);
            const data = await result.json();

            if (!data || data.length === 0) {
                Logger.warn(`GenesisSignalHelper::getParticipantAnalysis::No participant analysis data found for token ${tokenAddress}`);
                return null;
            }

            return data[0];
        } catch (error: any) {
            Logger.error(`GenesisSignalHelper::getParticipantAnalysis::Error fetching participant analysis for token ${tokenAddress}: ${error.message}`);
            return null;
        }
    }

    static calculateDPP(tpp: number, usdPrice: number): string | null {
        if (!tpp || !usdPrice) return null;
        const dpp = tpp * usdPrice;
        return Utils.millify(dpp, Utils.getPrecision(dpp));
    }

    static isValidForPreLaunchAlert(tokenData: any): boolean {
        const requiredFields = ['participants', 'committedVirtuals', 'pointsPledged', 'committedPercentage', 'tpp', 'maxAllocation', 'token'];
        const missingFields = requiredFields.filter(field =>
            tokenData[field] === null ||
            tokenData[field] === undefined ||
            tokenData[field] === ''
        );

        if (missingFields.length > 0) {
            Logger.warn(`GenesisSignalHelper::isValidForPreLaunchAlert::Genesis id ${tokenData.genesisId} missing required fields: ${missingFields.join(', ')}, skipping`);
            return false;
        }

        if (!tokenData.committedPercentage || tokenData.committedPercentage <= 80) {
            Logger.info(`GenesisSignalHelper::isValidForPreLaunchAlert::Genesis id ${tokenData.genesisId} has only ${tokenData.committedPercentage}% commitment, skipping`);
            return false;
        }

        return true;
    }

    static isValidForPostLaunchAnalysis(genesisData: any, marketData: any, participantAnalysis: any): boolean {
        // Check genesisData fields
        const genesisDataFields = ['token', 'tpp', 'participants', 'committedVirtuals', 'committedPercentage', 'daysFromFirstUnlock', 'roiAth', 'roi'];
        for (const field of genesisDataFields) {
            if (genesisData[field] === null || genesisData[field] === undefined ||
                (typeof genesisData[field] === 'number' && isNaN(genesisData[field])) ||
                genesisData[field] === '') {
                Logger.warn(`GenesisSignalHelper::isValidForPostLaunchAnalysis::Invalid genesisData.${field}: ${genesisData[field]}`);
                return false;
            }
        }

        // Check marketData fields
        const marketDataFields = ['mcap'];
        for (const field of marketDataFields) {
            if (marketData[field] === null || marketData[field] === undefined ||
                isNaN(Number(marketData[field]))) {
                Logger.warn(`GenesisSignalHelper::isValidForPostLaunchAnalysis::Invalid marketData.${field}: ${marketData[field]}`);
                return false;
            }
        }

        // Check participantAnalysis fields
        const participantFields = ['soldAll', 'soldPartially', 'holdingInitialAllocation', 'stakedAmount', 'initialSupply', 'currentSupply', 'top25ParticipantsHolding'];
        for (const field of participantFields) {
            if (participantAnalysis[field] === null || participantAnalysis[field] === undefined ||
                isNaN(Number(participantAnalysis[field]))) {
                Logger.warn(`GenesisSignalHelper::isValidForPostLaunchAnalysis::Invalid participantAnalysis.${field}: ${participantAnalysis[field]}`);
                return false;
            }
        }

        return true;
    }

    static sanitizeTwitterText(text: string): string {
        return text.replace(/[\*`]/g, '');
    }
}
