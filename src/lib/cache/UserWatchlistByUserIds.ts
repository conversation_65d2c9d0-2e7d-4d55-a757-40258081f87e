import CacheBase from './CacheBase';
import CacheConstant from '../constant/Cache';
import ResponseHelper from '../helper/ResponseHelper';
import { SuccessResponse } from '../Types';
import Postgres from '../Postgres';
import Logger from '../Logger';

/**
 * Class for user watchlist by user ids cache.
 *
 * @class UserWatchlistByUserIds
 */
export default class UserWatchlistByUserIds extends CacheBase {
    private userIds: number[];

    /**
     * Init params in oThis.
     *
     * @param {object} params
     * @param {number[]} params.userIds
     *
     * @sets oThis.userIds
     */
    public initParams(params: { userIds: number[] }): void {
        const oThis = this;
        oThis.userIds = params.userIds
    }

    /**
     * Set cache type.
     *
     * @sets oThis.cacheType
     */
    setCacheType(): void {
        const oThis = this;
        oThis.cacheType = CacheConstant.memcached;
    }

    /**
     * Set cache keys.
     *
     * @sets oThis.cacheKeys
     *
     * @private
     */
    setCacheKeys(): void {
        const oThis = this;
        for (const userId of oThis.userIds) {
            const cacheKey = `${oThis.getCacheKeyPrefix()}_uwlbui_${userId}`;
            oThis.cacheKeys[cacheKey] = userId.toString();
        }
    }

    /**
     * Set cache expiry in oThis.cacheExpiry.
     *
     * @sets oThis.cacheExpiry
     *
     * @private
     */
    setCacheExpiry(): void {
        const oThis = this;
        oThis.cacheExpiry = CacheConstant.largeExpiryTimeInterval; // 1 day
    }

    /**
     * Fetch data from source for cache miss user ids
     *
     * @param cacheMissUserIds
     *
     * @return {Promise<SuccessResponse>}
     */
    async fetchDataFromSource(cacheMissUserIds: number[]): Promise<SuccessResponse> {
        const oThis = this;
        const userWatchlistModel = (await Postgres.getDbModels()).userWatchlist;
        const watchlistByUserIds = await userWatchlistModel.getByUserIds(cacheMissUserIds);
        Logger.debug(`UserWatchlistByUserIds::fetchDataFromSource::User watchlist response from cache: ${JSON.stringify(watchlistByUserIds)}`);
        return ResponseHelper.success(watchlistByUserIds);
    }
}
