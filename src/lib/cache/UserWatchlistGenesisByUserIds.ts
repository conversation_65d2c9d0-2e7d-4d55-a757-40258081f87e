import CacheBase from './CacheBase';
import CacheConstant from '../constant/Cache';
import ResponseHelper from '../helper/ResponseHelper';
import { SuccessResponse } from '../Types';
import Postgres from '../Postgres';
import Logger from '../Logger';

/**
 * Class for user watchlist genesis by user ids cache.
 *
 * @class UserWatchlistGenesisByUserIds
 */
export default class UserWatchlistGenesisByUserIds extends CacheBase {
    private userIds: number[];

    /**
     * Init params in oThis.
     *
     * @param {object} params
     * @param {number[]} params.userIds
     *
     * @sets oThis.userIds
     */
    public initParams(params: { userIds: number[] }): void {
        const oThis = this;
        oThis.userIds = params.userIds
    }

    /**
     * Set cache type.
     *
     * @sets oThis.cacheType
     */
    setCacheType(): void {
        const oThis = this;
        oThis.cacheType = CacheConstant.memcached;
    }

    /**
     * Set cache keys.
     *
     * @sets oThis.cacheKeys
     *
     * @private
     */
    setCacheKeys(): void {
        const oThis = this;
        for (const userId of oThis.userIds) {
            const cacheKey = `${oThis.getCacheKeyPrefix()}_uwlgbui_${userId}`;
            oThis.cacheKeys[cacheKey] = userId.toString();
        }
    }

    /**
     * Set cache expiry in oThis.cacheExpiry.
     *
     * @sets oThis.cacheExpiry
     *
     * @private
     */
    setCacheExpiry(): void {
        const oThis = this;
        oThis.cacheExpiry = CacheConstant.largeExpiryTimeInterval; // 1 day
    }

    /**
     * Fetch data from source for cache miss user ids
     *
     * @param cacheMissUserIds
     *
     * @return {Promise<SuccessResponse>}
     */
    async fetchDataFromSource(cacheMissUserIds: number[]): Promise<SuccessResponse> {
        const oThis = this;
        const userWatchlistGenesisModel = (await Postgres.getDbModels()).userWatchlistGenesis;
        const watchlistGenesisByUserIds = await userWatchlistGenesisModel.getByUserIds(cacheMissUserIds);
        Logger.debug(`UserWatchlistGenesisByUserIds::fetchDataFromSource::User Genesis watchlist response from cache: ${JSON.stringify(watchlistGenesisByUserIds)}`);
        return ResponseHelper.success(watchlistGenesisByUserIds);
    }
}
