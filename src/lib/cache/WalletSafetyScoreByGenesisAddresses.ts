import CacheBase from './CacheBase';
import CacheConstant from '../constant/Cache';
import ResponseHelper from '../helper/ResponseHelper';
import { SuccessResponse } from '../Types';
import Postgres from '../Postgres';
import Logger from '../Logger';

/**
 * Class for wallet safety score by genesis addresses cache.
 *
 * @class WalletSafetyScoreByGenesisAddresses
 */
export default class WalletSafetyScoreByGenesisAddresses extends CacheBase {
    private genesisContractAddresses: string[];

    /**
     * Init params in oThis.
     *
     * @param {object} params
     * @param {string[]} params.genesisContractAddresses
     *
     * @sets oThis.genesisContractAddresses
     */
    public initParams(params: { genesisContractAddresses: string[] }): void {
        const oThis = this;
        oThis.genesisContractAddresses = params.genesisContractAddresses
    }

    /**
     * Set cache type.
     *
     * @sets oThis.cacheType
     */
    setCacheType(): void {
        const oThis = this;
        oThis.cacheType = CacheConstant.memcached;
    }

    /**
     * Set cache keys.
     *
     * @sets oThis.cacheKeys
     *
     * @private
     */
    setCacheKeys(): void {
        const oThis = this;
        for (const genesisContractAddress of oThis.genesisContractAddresses) {
            const cacheKey = `${oThis.getCacheKeyPrefix()}_wssbga_${genesisContractAddress}`;
            oThis.cacheKeys[cacheKey] = genesisContractAddress;
        }
    }

    /**
     * Set cache expiry in oThis.cacheExpiry.
     *
     * @sets oThis.cacheExpiry
     *
     * @private
     */
    setCacheExpiry(): void {
        const oThis = this;
        oThis.cacheExpiry = CacheConstant.largeExpiryTimeInterval; // 1 day
    }

    /**
     * Fetch data from source for cache miss genesis contract addresses
     *
     * @param cacheMissGenesisContractAddresses
     *
     * @return {Promise<SuccessResponse>}
     */
    async fetchDataFromSource(cacheMissGenesisContractAddresses: string[]): Promise<SuccessResponse> {
        const oThis = this;
        const walletSafetyScoreModel = (await Postgres.getDbModels()).walletSafetyScore;
        const walletSafetyScoreByGenesisAddresses = await walletSafetyScoreModel.getByGenesisAddresses(cacheMissGenesisContractAddresses);
        Logger.debug(`WalletSafetyScoreByGenesisAddresses::fetchDataFromSource::Wallet safety score by genesis addresses response from cache: ${JSON.stringify(walletSafetyScoreByGenesisAddresses)}`);
        return ResponseHelper.success(walletSafetyScoreByGenesisAddresses);
    }
}
