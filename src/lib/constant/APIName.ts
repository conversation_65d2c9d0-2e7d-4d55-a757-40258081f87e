/**
 * Class for API names.
 *
 * @class APIName
 */
class APIName {

    /** ************ AI APIs ************* */

    get nql(): string {
        return 'nql';
    }

    /** ************ User APIs ************* */

    get registerUser(): string {
        return 'registerUser';
    }

    get walletLogin(): string {
        return 'walletLogin';
    }

    get getCurrentUser(): string {
        return 'getCurrentUser';
    }

    get logout(): string {
        return 'logout';
    }

    get addWaitlist(): string {
        return 'addWaitlist';
    }

    /** ************ Lokiverse APIs ************* */

    get getTrendingQuestions(): string {
        return 'trendingQuestions';
    }

    get ask(): string {
        return 'ask';
    }

    get submitImpression(): string {
        return 'submit-impression';
    }

    get shareAnswer(): string {
        return 'share';
    }

    get chatHistory(): string {
        return 'chat-history';
    }

    get submitFeedback(): string {
        return 'feedback';
    }

    get signalReadTime(): string {
        return 'signal-read-time';
    }

    get signals(): string {
        return 'signals';
    }

    get explorer(): string {
        return 'explorer';
    }

    get generateAPIKey(): string {
        return 'generate-api-key';
    }

    get userAPIKey(): string {
        return 'user-api-key';
    }

    get agents(): string {
        return 'agents';
    }

    get agentMetainfo(): string {
        return 'agent-metainfo';
    }

    get lokyDetails(): string {
        return 'loky-details';
    }

    get askLoky(): string {
        return 'ask-loky';
    }

    get cryptoMetainfo(): string {
        return 'crypto-metainfo';
    }

    get cryptoMarket(): string {
        return 'crypto-market';
    }

    get rugStatus(): string {
        return 'rug-status';
    }

    get addWatchlist(): string {
        return 'add-watchlist';
    }

    get removeWatchlist(): string {
        return 'remove-watchlist';
    }

    get rugScanner(): string {
        return 'rug-scanner';
    }

    get agentDegen(): string {
        return 'agent-degen';
    }

    get devBundleTransactions(): string {
        return 'dev-bundle-transactions';
    }

    get devWalletFunding(): string {
        return 'dev-wallet-funding';
    }

    get firstBuyers(): string {
        return 'first-buyers';
    }

    get tokenTA(): string {
        return 'token-ta';
    }

    get stakedDetails(): string {
        return 'staked-details';
    }

    get genesisToken(): string {
        return 'genesis-token';
    }

    get genesisTokenEAC(): string {
        return 'genesis-token-eac';
    }

    get trendingTokens(): string {
        return 'trending-tokens';
    }

    get bubbleMap(): string {
        return 'bubble-map';
    }

    get agentMetrics(): string {
        return 'agent-metrics';
    }

    get cryptoMarketHistorical(): string {
        return 'crypto-market-historical';
    }

    get genesisParticipantsAnalysis(): string {
        return 'genesis-participants-analysis';
    }

    get walletSafetyScore(): string {
        return 'wallet-safety-score';
    }
}

export default new APIName();
