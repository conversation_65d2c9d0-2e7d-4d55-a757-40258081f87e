import SignalConstant from "./SignalConstant";

export default class TelegramConstant {
    public static get telegramBotEnabled(): boolean {
        return process.env.TELEGRAM_BOT_ENABLED === 'true';
    }

    public static get telegramBotToken(): string {
        return process.env.TELEGRAM_BOT_TOKEN!;
    }

    public static get lokyTerminalChatId(): string {
        return process.env.LOKY_TELEGRAM_CHAT_ID!;
    }

    public static get lokyTerminalThreadId(): number {
        return parseInt(process.env.LOKY_TELEGRAM_THREAD_ID!);
    }

    public static get signalTextSuffix(): string {
        return `Visit Loky Terminal to access alpha signals, Chat with <PERSON><PERSON> and much more`;
    }

    public static redPilledSignalText(tokenName: string, tokenTicker: string, tokenAddress: string, network: string, devWallet: string, createdAt: string, devPurchaseAmount: any, devWalletBalance: any): string {
        return `💊 *${tokenName} [$${tokenTicker}]:* Just graduated on Virtuals!\nNetwork: \`${network.charAt(0).toUpperCase() + network.slice(1)}\`\nName: \`${tokenName}\`\nSymbol: \`$${tokenTicker}\`\nContract Address: \`${tokenAddress}\`\n${devPurchaseAmount ? `Dev Purchase Amount: *${devPurchaseAmount} USD*\n` : ''}${devWalletBalance ? `Dev Wallet Balance: *${devWalletBalance} ETH*\n` : ''}Dev Wallet: \`${devWallet}\`\nGraduated At: *${createdAt}*`;
    }

    public static newlyLaunchedSignalText(tokenName: string, tokenTicker: string, tokenAddress: string, network: string, devWallet: string, createdAt: string, devPurchaseAmount: any, devWalletBalance: any): string {
        return `🚀 *${tokenName} [$${tokenTicker}]:* Just launched on Virtuals!\nNetwork: \`${network.charAt(0).toUpperCase() + network.slice(1)}\`\nName: \`${tokenName}\`\nSymbol: \`$${tokenTicker}\`\nContract Address: \`${tokenAddress}\`\n${devPurchaseAmount ? `Dev Purchase Amount: *${devPurchaseAmount} USD*\n` : ''}${devWalletBalance ? `Dev Wallet Balance: *${devWalletBalance} ETH*\n` : ''}Dev Wallet: \`${devWallet}\`\nLaunched At: *${createdAt}*`
    }

    public static newlyLaunchedGenesisSignalText(tokenName: string, tokenTicker: string, tokenAddress: string, network: string, devWallet: string, createdAt: string, devPurchaseAmount: any, devWalletBalance: any): string {
        return `🚀 *${tokenName} [$${tokenTicker}]:* Genesis Agent just launched on Virtuals!\nNetwork: \`${network.charAt(0).toUpperCase() + network.slice(1)}\`\nName: \`${tokenName}\`\nSymbol: \`$${tokenTicker}\`\nContract Address: \`${tokenAddress}\`\n${devPurchaseAmount ? `Dev Purchase Amount: *${devPurchaseAmount} USD*\n` : ''}${devWalletBalance ? `Dev Wallet Balance: *${devWalletBalance} ETH*\n` : ''}Dev Wallet: \`${devWallet}\`\nLaunched At: *${createdAt}*`
    }

    public static rugStatusChangeSignalText(tokenName: string, tokenTicker: string, newRugStatus: string, devWalletSoldPercentage: string): string {
        return `🚨 *${tokenName} [$${tokenTicker}]:* Rug Risk Increased - Now *${newRugStatus}*!\n\nDeveloper wallet *sold ${devWalletSoldPercentage}%* of holdings.`;
    }

    public static genesisPreLaunchAlertText(
        signalDestination: 'X' | 'Telegram',
        signalData: {
            token: string,
            participants: string,
            committedVirtuals: string,
            pointsPledged: string,
            committedPercentage: string,
            tpp: string,
            maxAllocation: string,
            daysFromFirstUnlock: number,
            description: string | null
        }
    ): string {
        return `*🚨 $${signalData.token} is Launching in an hour!* Here's your early alpha

📈 *Subscribed:* \`${signalData.committedPercentage}%\`
🔥 *$VIRTUAL Committed:* \`${signalData.committedVirtuals}\`
💎 *Points Pledged:* \`${signalData.pointsPledged}\`
📊 *TPP:* \`${signalData.tpp}\`
📦 *Max Allocation:* \`${signalData.maxAllocation} VP\`
👥 *Participants:* \`${signalData.participants}\`
⏳ *Token Unlock:* ${signalData.daysFromFirstUnlock > 0 ? `\`${signalData.daysFromFirstUnlock} days\`` : '\`unlocked\`'}${signalData.description && signalData.description !== '' ? `\n\n*Why it matters: 🔍*\n${signalData.description}` : ''}\n
Full wallet + post-launch analysis dropping soon ${signalDestination === 'X' ? 'via @0xLoky_AI' : 'on Loky Terminal'}.`;
    }

    public static genesisPostLaunchAnalysisText(
        signalDestination: 'X' | 'Telegram',
        signalData: {
            token: string,
            marketCap: string,
            currentRoi: string,
            athRoi: string,
            unlockDays: number,
            tpp: string,
            dpp: string,
            subscriptionPct: string,
            soldAll: string,
            stakedAmount: string,
            jeetersPct: string,
            holdingPct: string,
            initialHoldingPct: string,
            currentHoldingPct: string,
            top25Pct: string,
        }
    ): string {
        return `*Post Launch Analysis for $${signalData.token} 👀*

📊 *Launch Stats*

*Market Cap:* \`$${signalData.marketCap}\`
*Current ROI:* \`${signalData.currentRoi}x\`
*ATH ROI:* \`${signalData.athRoi}x\`
*Token Unlock in:* ${signalData.unlockDays > 0 ? `\`${signalData.unlockDays} days\`` : '\`unlocked\`'}
*TPP/DPP:* \`${signalData.tpp} / $${signalData.dpp}\`
*Oversubscribed:* \`${signalData.subscriptionPct}%\`

👥 *Participants Behavior Analysis*

*Sold All:* \`${signalData.soldAll}\`
*Staked Amount:* \`${signalData.stakedAmount}\`
*JEETERS:* \`${signalData.jeetersPct}%\`
*Diamond Hands:* \`${signalData.holdingPct}%\`
*Initial Holding:* \`${signalData.initialHoldingPct}%\`
*Current Holdings:* \`${signalData.currentHoldingPct}%\`
*Top 25 Holders:* \`${signalData.top25Pct}%\`${signalDestination === 'X' ? '\n\nTracking continues on @0xLoky_AI.' : ''}`;
    }

    public static dexScannerUrl(network: string, tokenAddress: string): string {
        return `https://dexscreener.com/${network}/${tokenAddress}`;
    }

    public static basescanTokenUrl(tokenAddress: string): string {
        return `https://basescan.org/token/${tokenAddress}`;
    }

    public static basescanAddressUrl(address: string): string {
        return `https://basescan.org/address/${address}`;
    }

    public static get lokyTerminalUrl(): string {
        return 'https://0xloky.com/terminal/lqa';
    }

    public static get lokyXUrl(): string {
        return 'https://x.com/0xLoky_AI';
    }

    public static get lokyDocsUrl(): string {
        return 'https://docs.0xloky.com';
    }

    public static get lokyHomePageUrl(): string {
        return 'https://0xloky.com/home';
    }

    public static virtualsPrototypeUrl(tokenAddress: string): string {
        return `https://app.virtuals.io/prototypes/${tokenAddress}`;
    }

    public static virtualsSentientUrl(virtualId: number): string {
        return `https://app.virtuals.io/virtuals/${virtualId}`;
    }

    public static virtualsGenesisUrl(virtualsAppId: number): string {
        return `https://app.virtuals.io/geneses/${virtualsAppId}`;
    }

    public static xTokenSearchUrl(token: string): string {
        return `https://x.com/search?q=${token}&src=typed_query`;
    }

    public static sigma5TradingBotUrl(tokenAddress: string): string {
        return `https://t.me/SigmaTrading5_bot?start=x465226195-${tokenAddress}`
    }

    public static sigma10TradingBotUrl(tokenAddress: string): string {
        return `https://t.me/SigmaTrading10_bot?start=x465226195-${tokenAddress}`
    }

    public static xDevSearchUrl(devWallet: string): string {
        return `https://x.com/search?q=${devWallet}&src=typed_query`;
    }

    public static virtualMetaDataUrl(): string {
        return `https://api.virtuals.io/api/virtuals?filters[status][$in][0]=AVAILABLE&filters[status][$in][1]=ACTIVATING&filters[priority][$ne]=-1&sort[0]=createdAt%3Adesc&pagination[page]=1&pagination[pageSize]=100`;
    }

    public static smartMoneyMovementText(
        tokenName: string,
        tokenTicker: string,
        buyPrice: string,
        sellPrice: string,
        icon: string
    ): string {
        const prefix = `*${tokenName} [$${tokenTicker}]:* Smart Money Movement 🧠:`;
        if (buyPrice === "0") return `${prefix} $${sellPrice} tokens sold (${icon}) in last 30 minutes`;
        if (sellPrice === "0") return `${prefix} $${buyPrice} tokens bought (${icon}) in last 30 minutes`;
        return `${prefix} $${buyPrice} bought & $${sellPrice} sold (${icon}) in last 30 minutes`;
    }

    public static get signalSupportForTelegramCategories(): string[] {
        return [
            SignalConstant.redPilledStrId,
            SignalConstant.smartMoneyMovementStrId,
            SignalConstant.newAgentStrId,
            SignalConstant.rugStatusChangeStrId,
            SignalConstant.genesisNewAgentStrId,
            SignalConstant.genesisPreLaunchAlertStrId,
            SignalConstant.genesisPostLaunchAnalysisStrId
        ]
    }

    public static get redPillDefaultImage(): string {
        return 'https://d2yxqfr8upg55w.cloudfront.net/assets/img/redpill-signal.png';
    }

    public static get newlyLaunchedDefaultImage(): string {
        return 'https://d2yxqfr8upg55w.cloudfront.net/assets/unilink-img/new_agent_signal.png';
    }

    public static get rugStatusChangeDefaultImage(): string {
        return 'https://d2yxqfr8upg55w.cloudfront.net/assets/unilink-img/rug-status-banner.png';
    }
}
