import Utils from "../Utils";

class SignalConstant {
    private invertedSubCategoryMap: Record<string, number>;

    // ******************************************** TOKEN CATEGORIES MAPS ********************************************
    public get subCategoryDisplayName(): Record<number, string> {
        return {
            [this.invertedSubCategoryIdToStrId[this.priceTopGainer1HrStrId]]: 'Price Top Gainer in 1 hr',
            [this.invertedSubCategoryIdToStrId[this.priceTopLoser1HrStrId]]: 'Price Top Loser in 1 hr',
            [this.invertedSubCategoryIdToStrId[this.redPilledStrId]]: 'Red pill',
            [this.invertedSubCategoryIdToStrId[this.mcapTopLoser24HrStrId]]: 'MCap Top Loser in 24 hr',
            [this.invertedSubCategoryIdToStrId[this.mcapTopGainer24HrStrId]]: 'MCap Top Gainer in 24 hr',
            [this.invertedSubCategoryIdToStrId[this.coinGeckoListedStrId]]: 'Coin Gecko Listed',
            [this.invertedSubCategoryIdToStrId[this.smartMoneyMovementStrId]]: 'Smart Money Movement Alert',
            [this.invertedSubCategoryIdToStrId[this.holderCountChange24hStrId]]: 'Holder Count Change in 24 hr',
            [this.invertedSubCategoryIdToStrId[this.whaleWalletSignal15minsStrId]]: 'Whale Wallet Alert',
            [this.invertedSubCategoryIdToStrId[this.newAgentStrId]]: 'New Agent Launch',
            [this.invertedSubCategoryIdToStrId[this.rugStatusChangeStrId]]: 'Rug Status',
            [this.invertedSubCategoryIdToStrId[this.genesisNewAgentStrId]]: 'New Genesis Agent Launch'
        };
    }

    public get subCategoryIcon(): Record<number, string> {
        return {
            [this.invertedSubCategoryIdToStrId[this.priceTopGainer1HrStrId]]: 'trend-up',
            [this.invertedSubCategoryIdToStrId[this.priceTopLoser1HrStrId]]: 'trend-down',
            [this.invertedSubCategoryIdToStrId[this.redPilledStrId]]: 'red_pill',
            [this.invertedSubCategoryIdToStrId[this.mcapTopLoser24HrStrId]]: 'trend-down',
            [this.invertedSubCategoryIdToStrId[this.mcapTopGainer24HrStrId]]: 'trend-up',
            [this.invertedSubCategoryIdToStrId[this.coinGeckoListedStrId]]: 'coingecko_colored_logo',
            [this.invertedSubCategoryIdToStrId[this.smartMoneyMovementStrId]]: 'smart-money-icon',
            [this.invertedSubCategoryIdToStrId[this.whaleWalletSignal15minsStrId]]: 'whale-wallet',
            [this.invertedSubCategoryIdToStrId[this.holderCountChange24hStrId]]: 'holder-change',
            [this.invertedSubCategoryIdToStrId[this.newAgentStrId]]: 'new_agent',
            [this.invertedSubCategoryIdToStrId[this.rugStatusChangeStrId]]: 'rugstatus',
            [this.invertedSubCategoryIdToStrId[this.genesisNewAgentStrId]]: 'genesis-icon'
        };
    }

    public get subCategoryTitle(): Record<number, string> {
        return {
            [this.invertedSubCategoryIdToStrId[this.priceTopGainer1HrStrId]]: 'Gain in 1 hour',
            [this.invertedSubCategoryIdToStrId[this.priceTopLoser1HrStrId]]: 'Loss in 1 hour',
            [this.invertedSubCategoryIdToStrId[this.redPilledStrId]]: 'Red pilled token',
            [this.invertedSubCategoryIdToStrId[this.mcapTopLoser24HrStrId]]: 'Loss in 24 hours',
            [this.invertedSubCategoryIdToStrId[this.mcapTopGainer24HrStrId]]: 'Gain in 24 hours',
            [this.invertedSubCategoryIdToStrId[this.coinGeckoListedStrId]]: 'New listing',
            [this.invertedSubCategoryIdToStrId[this.smartMoneyMovementStrId]]: 'Smart money',
            [this.invertedSubCategoryIdToStrId[this.whaleWalletSignal15minsStrId]]: 'Whale Wallet',
            [this.invertedSubCategoryIdToStrId[this.holderCountChange24hStrId]]: 'Token Holder Change',
            [this.invertedSubCategoryIdToStrId[this.newAgentStrId]]: 'New Agent Launch',
            [this.invertedSubCategoryIdToStrId[this.rugStatusChangeStrId]]: 'Rug Status',
            [this.invertedSubCategoryIdToStrId[this.genesisNewAgentStrId]]: 'New Genesis Agent Launch'
        };
    }

    public get subCategoryIdToStrId(): Record<number, string> {
        return {
            1: this.priceTopGainer1HrStrId,
            2: this.priceTopLoser1HrStrId,
            3: this.redPilledStrId,
            4: this.mcapTopLoser24HrStrId,
            5: this.mcapTopGainer24HrStrId,
            6: this.coinGeckoListedStrId,
            7: this.smartMoneyMovementStrId,
            8: this.whaleWalletSignal15minsStrId,
            9: this.holderCountChange24hStrId,
            10: this.newAgentStrId,
            11: this.rugStatusChangeStrId,
            12: this.genesisNewAgentStrId,
            13: this.genesisPreLaunchAlertStrId,
            14: this.genesisPostLaunchAnalysisStrId
        };
    }

    public get signalFiltersTypeList(): any {
        return [
            { id: "10", label: "New Agent", icon: "new_agent" },
            // { id: "12", label: "New Genesis Agent", icon: "genesis-icon" }, // TODO: Unhide this later
            { id: "3", label: "Red-pilled", icon: "red_pill" },
            // { id: "8", label: "Whale Wallet", icon: "whale-wallet" },
            { id: "9", label: "Holder Change", icon: "holder-change" },
            { id: "7", label: "Smart Money", icon: "smart-money-icon" },
            { id: "6", label: "Listing", icon: "coingecko_colored_logo" },
            { id: "5", label: "Mcap Gain", icon: "trend-up" },
            { id: "4", label: "Mcap Loss", icon: "trend-down" },
            { id: "1", label: "Price Gain", icon: "trend-up" },
            { id: "2", label: "Price Loss", icon: "trend-down" },
            { id: "11", label: "Rug Status", icon: "rugstatus" }
        ];
    }

    public get signalCategoryFiltersList(): any {
        return [
            { id: "4", label: "Virtuals", icon: "virtuals-colored-icon" },
            // { id: "7", label: "Virtual Prototype", icon: "red_pill" },
            { id: "5", label: "Solana", icon: "solana-colored-icon" },
            { id: "8", label: "Base", icon: "base-colored-icon" }
        ];
    }

    public get defaultSelectedSignalIds(): string {
        return '3,9,5,6,7,10';
    }

    public get defaultSelectedSignalCategories(): string {
        return "4,5,8";
    }

    public get invertedSubCategoryIdToStrId(): Record<string, number> {
        const oThis = this;
        oThis.invertedSubCategoryMap = oThis.invertedSubCategoryMap || Utils.invert(oThis.subCategoryIdToStrId);
        return oThis.invertedSubCategoryMap;
    }

    public get priceTopGainer1HrStrId(): string {
        return '1hr_price_top_gainer';
    }

    public get priceTopLoser1HrStrId(): string {
        return '1hr_price_top_loser';
    }

    public get redPilledStrId(): string {
        return 'red_pilled';
    }

    public get newAgentStrId(): string {
        return 'new_agent';
    }

    public get genesisNewAgentStrId(): string {
        return 'new_genesis_agent';
    }

    public get rugStatusChangeStrId(): string {
        return 'rug_status_change';
    }

    public get mcapTopLoser24HrStrId(): string {
        return '24hr_mcap_top_loser';
    }

    public get mcapTopGainer24HrStrId(): string {
        return '24hr_mcap_top_gainer';
    }

    public get coinGeckoListedStrId(): string {
        return 'coin_gecko_listed';
    }

    public get smartMoneyMovementStrId(): string {
        return 'smart_money_movement';
    }

    public get holderCountChange24hStrId(): string {
        return 'holder_count_change_24h';
    }

    public get whaleWalletSignal15minsStrId(): string {
        return 'whale_wallet_15mins';
    }

    public get genesisPreLaunchAlertStrId(): string {
        return 'genesis_pre_launch_alert';
    }

    public get genesisPostLaunchAnalysisStrId(): string {
        return 'genesis_post_launch_analysis';
    }
}


export default new SignalConstant()
