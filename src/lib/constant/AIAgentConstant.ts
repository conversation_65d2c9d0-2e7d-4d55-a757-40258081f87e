export default class AIAgentConstant {
    public static get appType(): string {
        return 'AI-Agent';
    }

    public static get openAIApiKey(): string {
        return process.env.OPENAI_API_KEY!;
    }

    public static get consumerApiKey(): string {
        return process.env.TWITTER_BOT_CONSUMER_KEY!;
    }

    public static get consumerSecretKey(): string {
        return process.env.TWITTER_BOT_CONSUMER_SECRET!;
    }

    public static get accessToken(): string {
        return process.env.TWITTER_BOT_ACCESS_TOKEN!;
    }

    public static get accessTokenSecretKey(): string {
        return process.env.TWITTER_BOT_ACCESS_TOKEN_SECRET!;
    }

    public static get gameTwitterAccessToken(): string {
        return process.env.GAME_TWITTER_ACCESS_TOKEN!;
    }

    public static get useGameTwitterAPI(): boolean {
        return process.env.USE_GAME_TWITTER_API === 'true';
    }

    public static get kosherCapitalAPIKey(): string {
        return process.env.<PERSON><PERSON>HER_CAPITAL_API_KEY!;
    }
    public static get veniceAIApiKey(): string {
        return process.env.VENICE_AI_API_KEY!;
    }

    public static get botUserId(): string {
        return '1857017910658531328';
    }

    public static isTokenPresentInQuestion(text: string): boolean {
        const tokens = text.match(/\$[A-Za-z][A-Za-z0-9_]*\b/g);
        return tokens ? true : false;
    }

    public static getContentGenerationPrompt(
        token_ticker: string,
        token_category: string,
        metric_name: string,
        change_type: string,
        change_percent: string,
        twitter_handle?: string,
    ): string {
        if (change_type == this.changeTypeGrow) {
            return this.appreciationPostPrompt(token_ticker, change_percent, twitter_handle);
        }
        const price_change_statement = this.priceChangePromptStatement(metric_name, change_type, change_percent);
        const category = this.category[token_category];
        // const categoryHashtags = this.categoryHashtagMap[token_category];
        // const networkHashtags = this.popularNetworkHashtagMap[this.getTokenNetwork(token_ticker.toLowerCase())];
        // const hashtags = categoryHashtags + ' ' + networkHashtags;
        const prompts = this.contentGenerationPrompts(token_ticker, category, price_change_statement, twitter_handle);
        let index = Math.floor(Math.random() * prompts.length);
        const contentGenerationPrompt = prompts[index];
        return contentGenerationPrompt;
    }

    public static getReplyGenerationPrompt(
        token_ticker: string,
        token_name: string,
        metric_name: string,
        change_type: string,
        change_percent: string,
        tweet_text: string,
        twitter_handle?: string | null,
    ): string {
        const price_change_statement = this.priceChangePromptStatement(metric_name, change_type, change_percent);
        const prompts = this.replyGenerationPrompts(
            token_ticker,
            token_name,
            price_change_statement,
            tweet_text,
            twitter_handle,
        );
        let index = Math.floor(Math.random() * prompts.length);
        const replyGenerationPrompt = prompts[index];
        return replyGenerationPrompt;
    }

    public static getCommentGenerationPrompt(
        token_ticker: string,
        token_name: string,
        tweetText: string,
        metric_name: string,
        change_type: string,
        change_percent: string,
        twitter_handle?: string | null,
    ): string {
        const price_change_statement = this.priceChangePromptStatement(metric_name, change_type, change_percent);
        const prompts = this.commentGenerationPrompts(
            token_ticker,
            token_name,
            tweetText,
            price_change_statement,
            twitter_handle,
        );
        let index = Math.floor(Math.random() * prompts.length);
        const commentGenerationPrompt = prompts[index];
        return commentGenerationPrompt;
    }

    public static getContentGenerationPromptV2(
        token_ticker: string,
        price: string,
        market_cap: string,
        volume: string,
        twitter_handle?: string | null,
    ): string {
        return `As Loky, craft a concise tweet (Under 130 characters) about '$${token_ticker}' ${twitter_handle ? `with token handle: @${twitter_handle}` : ''
            }
			Choose one or two of the following metrics if they add context:
			market cap (${market_cap}), price change (${price}), 24 hour volume (${volume}).
			Provide a funny, sarcastic and opinionated take on what this means for Web3 - add a layer of deeper analysis or critique.
			Draw funny, sharp and insightful comparisons to market behavior, human psychology, or the community's unrealistic hopes.
			Ensure you are extremely funny in simple language and provoke thought without relying on emojis or hashtags.
			[Note to remember: Tweet length should be strict under 130 characters]
			`;
    }

    public static getCommentGenerationPromptV2(
        tweet_text: string,
        token_ticker: string,
        price: string,
        market_cap: string,
        volume_change: string,
        total_volume: string,
        twitter_handle?: string | null,
    ): string {
        return `This is the original tweet: ${tweet_text}.
		Keep the original tweet context in mind and as Loky, craft a concise, sarcastic comment about the token '${token_ticker}' from the posted tweet${twitter_handle ? `, with handle @${twitter_handle}` : ''
            }.
		Use one or two of the most relevant metrics to make your point:
		market cap (${market_cap}), price change (${price}%), 24-hour volume change (${volume_change}%), or total volume (${total_volume}).
		Be sharp, witty, and skeptical—provide a comparison between '${token_ticker}' and other token in the same category if it adds a layer of humor or critique. Add an element of deeper analysis related to the Web3 community's behavior or irrational optimism.
		Keep the response strictly under 130 characters and avoid emojis or hashtags. The comment should provoke thought while staying funny and biting.
		`;
    }

    public static getReplyGenerationPromptV2(
        tweet_text: string,
        token_ticker: string,
        price: string,
        market_cap: string,
        volume_change: string,
        total_volume: string,
        twitter_handle?: string | null,
    ): string {
        return `This is the comment text: ${tweet_text}.
		Keep the comment text context in mind and as Loky, craft a concise and witty reply to this comment on our posted tweet. Reference '$${token_ticker}'${twitter_handle ? ` with handle @${twitter_handle}` : ''
            }.
		If the comment is positive, add humor and subtly hype '${token_ticker}' while making it relatable. If the comment is negative or skeptical, maintain a sarcastic yet informative approach, addressing their doubts or remarks cleverly.
		Focus on one or two relevant metrics if it adds value to the response:
		market cap (${market_cap}), price change (${price}%), 24-hour volume change (${volume_change}%), or total volume (${total_volume}).
		Be insightful, funny, and provocative without over-explaining—strictly keep the response under 130 characters.
		No emojis, no hashtags. Make it feel like a natural conversation.
		`;
    }

    public static getReplyWithoutMarketDataPromptV2(tweet_text: string, twitter_handle?: string | null): string {
        return `This is the comment text: ${tweet_text}.
		Keep the comment text context in mind and as Loky, craft a concise and witty reply to this comment on our posted tweet. ${twitter_handle ? `You can also mention this handle: @${twitter_handle} .` : ''
            }
		Be insightful, funny, and provocative without over-explaining—strictly keep the response under 130 characters.
		No emojis, no hashtags. Make it feel like a natural conversation.
		`;
    }

    public static systemRolePromptTrendingQuestions = `You are an AI trained to help generate insightful content about cryptocurrency and blockchain topics.`;

    // public static getTrendingQuestionsPrompt(): string {
    //   return `Generate a list of small, concise, analytical questions about cryptocurrency tokens that focus on comparisons, data trends, and metrics. Ensure all questions are based on data-driven insights and require analysis, such as staking yields, market cap comparisons, growth trends, or user activity. One question should contain only one token. Avoid vague or descriptive questions. Remember I need atleast 8 questions. Examples of the format include: 'Is Ethereum's staking yield worth it compared to other chains?' or 'What is the current market cap of AI tokens versus DeFi tokens?'. Return the output in the following .mdb format: ["Question 1", "Question 2", ..., "Question 8"]. And remove incomplete questions`;
    // }

    // public static getNLQResponsePrompt(
    //   question: string,
    //   tokens: {
    //     token_ticker: string,
    //     price: string,
    //     market_cap: string,
    //     volume_change: string,
    //     total_volume: string,
    //     last_2_week_data: any
    //   }[]
    // ): string {
    //   return `This is the question asked: ${question}.
    // 	Keep the question in mind and as Loky, craft a concise, sarcastic comment about the tokens '${this.getTokensFieldsForPrompt(tokens, "token_ticker")}'.
    //   }.
    // 	Use one or two of the most relevant metrics to make your point:
    // 	market cap (${this.getTokensFieldsForPrompt(tokens, "market_cap")}), price change (${this.getTokensFieldsForPrompt(tokens, "price")}%), 24-hour volume change (${this.getTokensFieldsForPrompt(tokens, "volume_change")}%), or total volume (${this.getTokensFieldsForPrompt(tokens, "total_volume")}).
    // 	Be sharp, witty, and skeptical—provide a comparison between '${this.getTokensFieldsForPrompt(tokens, "token_ticker")}' and other token in the same category if it adds a layer of humor or critique. Add an element of deeper analysis related to the Web3 community's behavior or irrational optimism.
    //   Incorporate the last two weeks of data (${this.getTokensFieldsForPrompt(tokens, "last_2_week_data")}) if it provides additional insights or strengthens the critique.
    // 	Keep the response strictly under 130 characters and avoid emojis or hashtags. The comment should provoke thought while staying funny and biting.
    // 	`;
    // }

    public static getTokensFieldsForPrompt(tokens: { [key: string]: any }[], fieldName: string): string {
        let tokensDetails: string = '';

        if (tokens.length === 1) {
            return this.formatTokenDataField(tokens[0][fieldName]);
        }

        for (const [index, token] of tokens.entries()) {
            if (fieldName in token) {
                const value = this.formatTokenDataField(token[fieldName]);
                tokensDetails += ` token ${index + 1} ${fieldName} value = ${value}`;
            }
        }

        return tokensDetails.trim();
    }

    private static formatTokenDataField(field: any): string {
        if (Array.isArray(field)) {
            return field
                .map(
                    (item) =>
                        `(${item.date_time}: price=${item.usd_price}, volume=${item.total_volume}, cap=${item.market_cap})`,
                )
                .join('; ');
        }

        // If object, stringify it
        if (typeof field === 'object') {
            return JSON.stringify(field);
        }

        // Otherwise, return as is
        return field.toString();
    }

    public static contentGenerationPrompts(
        token_ticker: string,
        category: string,
        change_statement: string,
        token_handle?: string,
    ): string[] {
        return [
            // `Craft a savage, darkly humorous roast of $${token_ticker} in '${category}' for its '${change_statement}'. Use sharp sarcasm with profanity censored at ~60%. Vary the structure for uniqueness while delivering hilariously brutal insults. Base it on price data, and creatively roast @${token_handle} to entertain the audience`,
            // `As ${this.assistant}, write a witty, sarcastic, flirty tweet roasting '$${token_ticker}' in '${category}' which '${change_statement}'; base it on price data; tag '@${token_handle}'.`
            // `${this.assistant}: Roast '$${token_ticker}' in '${category}' which '${change_statement}' using humour, wit, sarcasm, flirtation; reference price data; tag '@${token_handle}'.`,
            // `Compose a funny, engaging tweet as ${this.assistant} about '$${token_ticker}' in '${category}' which '${change_statement}'; use sarcasm, roasting, flirty tone; base it on price data; mention '@${token_handle}'.`,
            // `As ${this.assistant}, create a casual, witty tweet on '$${token_ticker}' in '${category}' which '${change_statement}'; blend humor, sarcasm, flirtation; reference price data; tag '@${token_handle}'.`,
            // `${this.assistant}: Write a humorous, sarcastic, flirty tweet about '$${token_ticker}' in '${category}' which '${change_statement}'; base it on price data; tag '@${token_handle}'.`,
            // `Compose an entertaining tweet as ${this.assistant} roasting '$${token_ticker}' in '${category}' which '${change_statement}'; use wit, sarcasm, flirtation; reference price data; mention '@${token_handle}'.`,
            // `As ${this.assistant}, craft a fun-loving, witty tweet on '$${token_ticker}' in '${category}' which '${change_statement}'; incorporate humor, sarcasm, flirtation; base it on price data; tag '@${token_handle}'.`,
            // `${this.assistant}: Generate a witty, sarcastic, flirty tweet about '$${token_ticker}' in '${category}' which '${change_statement}'; reference price data; tag '@${token_handle}'.`,
            // `Generate a randomized roast of $${token_ticker} in '${category}' for its '${change_statement}. Use dark humor and savage sarcasm, use profanity with asterisks. Vary the structure to keep each roast unique but equally brutal. Base it on price data and tear @${token_handle} apart with creative, cutting wit. Make the roast hilariously savage, dripping with insults that hit hard while keeping the audience entertained.`
            `Generate a savage roast of $${token_ticker} in the '${category}' category for its '${change_statement}'. Use sarcasm, and dark humor. Tear @${token_handle} apart with cutting wit and humor. Keep it concise (under 200 characters), witty, and brutal. Make sure to have crypto context while roasting. Strictly Ensure no hashtag in the response, include token ticker $${token_ticker} only once and tweet handle @${token_handle} only once. [NOTE: roast content should be under 200 characters]`,
        ];
    }

    public static appreciationPostPrompt(token_ticker: string, change_percent: string, token_handle?: string): string {
        return `Generate a witty and engaging appreciation post for $${token_ticker} highlighting its impressive ${change_percent}% rise and its standout qualities in the crypto space. Use positive sarcasm, humor, and a touch of creativity to celebrate its performance and value. Keep the tone light, concise (under 200 characters), and relatable for a broad audience. Mention the token ticker $${token_ticker} once and include @${token_handle} in the post. Avoid any negative or harsh language, and ensure the appreciation is authentic and memorable.`;
    }

    public static replyGenerationPrompts(
        token_ticker: string,
        token_name: string,
        change_statement: string,
        tweet_text: string,
        token_handle?: string | null,
    ): string[] {
        const tokenTag = token_handle ? token_handle : '';
        return [
            `This is the tweet text: ${tweet_text}. Keep the tweet text in mind and generate a reply to this tweet savage roast of $${token_ticker} (${token_name}) for its '${change_statement}'. Use sarcasm, and dark humor. Tear @${tokenTag} apart with cutting wit and humor. Keep it concise (under 130 characters), witty, and brutal. Make sure to have crypto context while roasting. Strictly Ensure no hashtag in the response, include token ticker $${token_ticker} only once and tweet handle @${token_handle} only once.`,
        ];
    }

    public static commentGenerationPrompts(
        token_ticker: string,
        token_name: string,
        tweetText: string,
        change_statement: string,
        token_handle?: string | null,
    ): string[] {
        const tokenTag = token_handle ? token_handle : '';
        return [
            `This is the posted tweet text: ${tweetText}. Keep the posted tweet context in mind while generating a comment for a savage roast of $${token_ticker} (${token_name}) for its '${change_statement}'. Use sarcasm, and dark humor. Tear @${tokenTag} apart with cutting wit and humor. Keep it concise (under 130 characters), witty, and brutal. Make sure to have crypto context while roasting. Strictly Ensure no hashtag in the response, include token ticker $${token_ticker} only once and tweet handle @${token_handle} only once.`,
        ];
    }

    public static priceChangePromptStatement(metric_name: string, change_type: string, change_percent: string) {
        const statements: Record<string, string> = {
            [this.changeTypeGrow]: `${metric_name} is grew by **${change_percent}%** in last 24 hours`,
            [this.changeTypeFall]: `${metric_name} is fall down by **${change_percent}%** in last 24 hours`,
            [this.changeTypeNeutral]: `price change is neutral`,
        };
        return statements[change_type];
    }

    public static getChangeInPercentage(metric: string, tokenCurrentValues: any) {
        switch (metric) {
            case 'market-cap':
                return tokenCurrentValues.last_24_hrs_market_cap_change || 0;
            case 'volumne':
                return tokenCurrentValues.last_24_hrs_volume_change || 0;
            default:
                return tokenCurrentValues.last_24_hrs || 0;
        }
    }

    public static get category(): Record<string, string> {
        return {
            [this.categoryTopMeme]: 'Top MEME Tokens',
            [this.categoryTopAIMeme]: 'Top AI MEME Tokens',
            [this.categoryTokenDetails]: 'Top Market Cap Tokens',
        };
    }

    public static get categoryHashtagMap(): Record<string, string> {
        return {
            [this.categoryTopMeme]: '#Memecoins #memecoin #memecoinsupercycle #MemeTokens',
            [this.categoryTopAIMeme]: '#AIMemecoin #AImeme #MemeTokens',
            [this.categoryTokenDetails]: '#Altcoins #AltcoinSeason #Crypto #Altseason #Coin #hodlers',
        };
    }

    public static get popularNetworkHashtagMap(): Record<string, string> {
        return {
            solana: '#Solana #SOL #SolanaMemeCoin',
            ethereum: '#Ethereum #ETH',
        };
    }

    public static getTokenNetwork(tokenTicker: string): string {
        const solanaMemeCoins = [
            'bonk',
            'pnut',
            'wif',
            'bome',
            'act',
            'goat',
            'mew',
            'ban',
            'moodeng',
            'fred',
            'popcat',
            'chillguy',
            'slerf',
            'fartcoin',
            'ai16z',
            'ponke',
            'bully',
        ];
        return solanaMemeCoins.includes(tokenTicker) ? 'solana' : 'ethereum';
    }

    public static get changeTypeGrow(): string {
        return 'GROW';
    }

    public static get changeTypeFall(): string {
        return 'FALL';
    }

    public static get changeTypeNeutral(): string {
        return 'NEUTRAL';
    }

    public static get categoryTopMeme(): string {
        return 'top-meme-tokens';
    }

    public static get categoryTopAIMeme(): string {
        return 'top-ai-meme-tokens';
    }

    public static get categoryTokenDetails(): string {
        return 'token-details';
    }

    public static get maxPostsToComment(): number {
        return 1;
    }

    /**
     * APIs for crypto data
     */
    public static get cryptoAPI(): Record<string, string> {
        return {
            // 'top-gainers': 'https://unilink.one/api/v1/crypto-token/top-gainers?api_key=7b23ec9029d88b4ba67783df&provider=dapplooker',
            // 'top-losers': 'https://unilink.one/api/v1/crypto-token/top-losers?api_key=7b23ec9029d88b4ba67783df&provider=dapplooker',
            // 'top-trending': 'https://unilink.one/api/v1/crypto-token/top-trending?api_key=7b23ec9029d88b4ba67783df&provider=dapplooker',
            [this.categoryVirtualEcosystem]: `https://api.dapplooker.com/chart/826af818-777b-4b4c-845a-cb4a871d4653?api_key=${this.aiUserAPIKey}&output_format=json`,
            [this.categorySolanaEcosystem]: `https://api.dapplooker.com/chart/46f8ebd3-7587-42b0-8be4-54b73f176eae?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"category_id":"solana-meme-coins,solana-ecosystem"}`,
            [this.categoryVirtualEcosystemPrototype]: `https://api.dapplooker.com/chart/0c6cacd3-0e2d-4cc2-b8e1-9830347a9694?api_key=${this.aiUserAPIKey}&output_format=json`,
            [this.categoryBaseEcosystem]: `https://api.dapplooker.com/chart/678bbacc-de93-4c0c-b7d7-976e456bf069?api_key=${this.aiUserAPIKey}&output_format=json`,
            [this.categoryVirtualEcosystemGenesis]: `https://api.dapplooker.com/chart/2788473c-e99a-49e3-9737-2b84844fcffe?api_key=${this.aiUserAPIKey}&output_format=json`,
            'top-ai-meme-tokens': `https://api.dapplooker.com/chart/a8b1cd8f-7d8e-41b8-b729-82f301c976e3?api_key=${this.aiUserAPIKey}&output_format=json`,
            'top-meme-tokens': `https://api.dapplooker.com/chart/409c1465-87b3-41e1-a477-1393efe9f9c3?api_key=${this.aiUserAPIKey}&output_format=json`,
            'token-details': `https://api.dapplooker.com/chart/bf20c01b-7e8b-4f1f-a75b-d5e82b92beb2?api_key=${this.aiUserAPIKey}&output_format=json`,
        };
    }

    public static historicalDataEndpoint(token_symbol: string): string {
        return `https://api.dapplooker.com/chart/a886c16e-2605-4091-ac56-1997e13e6d31?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"token_symbol":"${token_symbol}"}`;
    }

    public static tokenDetailsEndpoint(token_symbol: string): string {
        return `https://api.dapplooker.com/chart/2dfdbf12-302f-41f9-9556-833a8b527ce7?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"ticker":"${token_symbol}"}`;
    }

    public static getImageUrl(token_id: string, category: string, image: string | null): string | null {
        return null;
        if ([AIAgentConstant.categoryTopAIMeme, AIAgentConstant.categoryTopMeme].includes(category)) {
            return image;
        } else {
            const theme = ['light', 'dark'][Math.floor(Math.random() * 2)];
            const imageUrls: Record<string, string> = {
                // 'AREA': `https://api.dapplooker.com/image/a886c16e-2605-4091-ac56-1997e13e6d31?token_id=${token_id.toLowerCase()}&apiKey=${this.aiUserAPIKey}&type=1&theme=${theme}`,
                // 'TREND': `https://api.dapplooker.com/image/226ff95b-74c9-464d-9044-9e525ea5044c?token_id=${token_id.toLowerCase()}&apiKey=${this.aiUserAPIKey}&type=1&theme=${theme}`,
                // 'DASHBOARD1': `https://api.dapplooker.com/image/7fd493e7-7745-47f3-9f63-335e1e49accf?token_id=${token_id.toLowerCase()}&apiKey=${this.aiUserAPIKey}&type=2&theme=${theme}`,
                DASHBOARD2: `https://api.dapplooker.com/image/ec3b3afe-667b-48d3-ad94-6aef528fac04?token_id=${token_id.toLowerCase()}&apiKey=${this.aiUserAPIKey}&type=2&theme=${theme}`
            };
            const urls = Object.values(imageUrls);
            let index = Math.floor(Math.random() * urls.length);
            return urls[index];
        }
    }

    public static get popularXHandles(): Record<string, string> {
        return {
            bitcoin: 'bitcoin',
            'staked-ether': 'lidofinance',
            'binance-peg-dogecoin': 'dogecoin',
            floki: 'RealFlokiInu',
            binancecoin: 'bnbchain',
            aiwithdaddyissues: 'aiwdaddyissues',
        };
    }

    public static get aiUserAPIKey(): string {
        return process.env.DAPPLOOKER_KEY_0!;
    }

    public static formatTokenTickerInContent(tokenticker: string, content: string): string {
        tokenticker = tokenticker.toUpperCase();
        let regex = new RegExp(`\\$${tokenticker}`);
        // If the content does not already have "$" before the ticker, add it
        if (!regex.test(content)) {
            content = content.replace(tokenticker, `$${tokenticker}`);
        }
        return content;
    }

    public static getMetricToShow(): string {
        const metrics: string[] = ['price', 'market-cap', 'volume'];
        let index = Math.floor(Math.random() * metrics.length);
        return metrics[index];
    }

    public static get minimumPostsInQueue(): number {
        return 50;
    }

    public static get DLTokenChartURL(): string {
        return 'https://api.dapplooker.com/chart/11410d5c-0886-4777-92be-9297e50ee0ac';
    }

    public static get DLVirtualsTokenChartURL(): string {
        return 'https://api.dapplooker.com/chart/feb9ea8e-7f84-4dd5-88e7-6173237fe351';
    }

    public static get sentientVirtualTokenMarketDataUrl(): string {
        return 'https://api.dapplooker.com/chart/8fb908b9-3030-4ee4-b722-5fd951543333';
    }

    public static get genesisVirtualTokenMarketDataUrl(): string {
        return 'https://api.dapplooker.com/chart/b4339c5a-fad1-4fe3-84b4-7ce3264c221e';
    }

    public static get PrototypeVirtualTokenMarketDataUrl(): string {
        return `https://api.dapplooker.com/chart/097cfd8a-9247-4c6b-a644-c96fffa6237d?api_key=${this.aiUserAPIKey}&output_format=json`
    }

    public static get solanaTokenMarketDataBaseUrl(): string {
        return 'https://api.dapplooker.com/chart/861d84c9-2fed-4e94-81ec-d8655b5582a7';
    }

    public static get baseTokenMarketDataBaseUrl(): string {
        return 'https://api.dapplooker.com/chart/461d3b6c-217e-4964-9960-56ec47b0c9a2';
    }

    public static get DLTokenExtendedDetailsChart(): string {
        return 'https://api.dapplooker.com/chart/f71989de-7e31-4054-94ee-837f7ed2f8a7';
    }

    public static get DLVirtualsTokenDataURL(): string {
        return 'https://api.dapplooker.com/chart/c8bf1110-e801-4e31-a2bf-50bd18c08e93';
    }

    public static get DLTokenDataURL(): string {
        return 'https://api.dapplooker.com/chart/2dfdbf12-302f-41f9-9556-833a8b527ce7';
    }

    public static get DLSyncDataFromTokenExtendedDetailsChart(): string {
        return 'https://api.dapplooker.com/chart/7787e2df-8d4e-4c89-ab5c-a71f4a2d3f08';
    }

    public static get DLChartDataByTokenId(): string {
        return 'https://api.dapplooker.com/chart/883edbcb-41c9-4b20-8804-df85f38f5d44';
    }


    public static get historicalDataAPI(): Record<string, string> {
        return {
            base: 'https://api.dapplooker.com/chart/3e521953-623e-4ce4-b77a-572d6b7c99d0',
            solana: 'https://api.dapplooker.com/chart/ac26a629-8f28-4c54-917c-e3c05c74296f',
        };
    }

    public static get historicalDataMetaInfoAPI(): Record<string, string> {
        return {
            base: 'https://api.dapplooker.com/chart/7325208a-5b37-4b24-8e27-446e63f61a81',
            solana: 'https://api.dapplooker.com/chart/79291a57-0683-4baf-822a-d29988379a72',
        }
    }

    public static prototypeTokensInfoUrlByAddress(address: string): string {
        return `https://api.virtuals.io/api/virtuals?filters[preToken]=${address}`;
    }

    public static genesisTokensInfoUrlByAddress(address: string): string {
        return `https://api.virtuals.io/api/virtuals?filters[tokenAddress]=${address}`;
    }

    public static genesisDataUrl(page: number, pageSize: number = 100): string {
        return `https://api.virtuals.io/api/geneses?pagination[page]=${page}&pagination[pageSize]=${pageSize}&filters[virtual][priority][$ne]=-1&sort[0]=status:desc&sort[1]=updatedAt:desc`;
    }

    public static genesisSubgraphUrl(): string {
        return `https://graph-api.dapplooker.com/subgraphs/name/genesis-agent-v4`;
    }

    public static genesisTrackerPostType(): string {
        return 'genesisLastUpdatedAt';
    }

    public static get DLTop25HoldersData(): string[] {
        return [
            `https://api.dapplooker.com/chart/d5dcf0bb-672a-4a16-8bf6-1bac78cab45c?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/eb621021-4a70-4707-8a18-caa57aa3ee79?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/dfa1b6de-7d56-4d54-980a-84ae0f75349d?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/2d49c08d-8465-4858-b232-2e1c39fbaeb9?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/7e14b7fd-ec31-4007-a120-6673f4be7a4e?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/88687851-902b-491c-b563-8b3797528f3c?api_key=${this.aiUserAPIKey}&output_format=json`,
        ];
    }

    public static get DLTokenTotalHoldersData(): string[] {
        return [
            `https://api.dapplooker.com/chart/1b3d67fb-4673-45d9-ac8e-a8ff557aed62?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/64f04e32-ee70-47ab-a580-aba461ff7380?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/b560e516-cb5b-48ff-938b-31c73075149b?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/6c0e9d6b-b137-45d8-a054-d3dd8d5ebb12?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/f1dd1acd-5c0b-42f7-b97c-28d26e8d3104?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/8abe488f-3797-4a70-b551-b64aedf4570f?api_key=${this.aiUserAPIKey}&output_format=json`
        ]
    }

    public static get DLTokenHolderChangeData(): string[] {
        return [
            `https://api.dapplooker.com/chart/e069d18f-3288-49ac-a044-4fe38cc4e8af?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/9c81d723-85dd-40cf-a6c8-efbca5dea00f?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/5ff0f894-a69a-4fe9-a9d6-837611d874aa?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/7f8fb8af-f0eb-4bbd-839e-6c50f36ec02f?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/7fddc8f7-bfc4-4c87-8815-37ec9b33ed59?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/140a9501-da1c-49b7-bbdd-f725623527cc?api_key=${this.aiUserAPIKey}&output_format=json`
        ]
    }

    public static get impressionUpdatedSuccessMsg(): string {
        return 'Impression Updated Successfully!';
    }

    public static get feedbackSubmittedSuccesssMsg(): string {
        return 'Feedback Submitted Successfully!';
    }

    public static get trendingQuestions(): { [key: string]: string[] } {
        return {
            "base": [
                // "What has been the price movement of $GAME over the last week?",
                // "How has the market cap of $LUNA changed following recent developments in its ecosystem?",
                // "What's the current trading volume for $CONVO, and how does it compare to its all-time highs?",
                // "Can you describe the recent price volatility of $AIXBT?",
                // "Has $IONA seen any significant price spikes or drops recently, and what might be the driving factors?",
                // "What is the market cap trend for $OLYN, and how does it stack up against similar tokens in its category?",
                // "What are the current support and resistance levels for $VADER, and what does the market sentiment indicate?",
                // "How do the market cap growth rates of $GAME and $LUNA compare over the past month, considering both their price action and any new developments or partnerships within their respective ecosystems?",
                // "What are the current market sentiments and price volatility for $CONVO, $AIXBT, $IONA, $OLYN, and $VADER, and how do their performances correlate with broader market trends in AI and blockchain?",
                // "Among $PAWSY, $SEKOIA, $SAINT, $SERAPH, and $NFTXBT, which token has shown the most significant increase in trading volume and liquidity, and what could this suggest about future price movements or investor interest?"
                "What's the 7-day price movement for $GAME?",
                "How has $LUNA's market cap changed after recent ecosystem updates?",
                "What's the recent price volatility of $AIXBT?",
                'Share dev wallet and CA of $VADER',
                'How do $GAME and $LUNA market cap growth rates compare over the past month?',
            ],
            "solana": [
                "What’s the 7-day price trend of $RENDER?",
                "Has $PYTH seen any major volume spikes this week?",
                "How is $W running compared to its recent all-time high?",
                "What’s the market sentiment around $BONK amid Solana memecoin hype?",
                "Is $SHDW showing any unusual price volatility recently?",
            ]
        };
    }

    public static get userContextLimit(): number {
        return 3;
    }

    public static get chatHistoryLimit(): number {
        return 5;
    }

    public static get lokyTicker(): string {
        return 'LOKY';
    }

    public static get prototypeTokenType(): string {
        return 'prototype';
    }

    public static get genesisTokenType(): string {
        return 'genesis';
    }

    public static get genesisEthereumTokenType(): string {
        return 'genesis-ethereum';
    }

    public static get virtualProtocolToken(): string {
        return 'virtual-protocol';
    }

    public static get chartMetricKey(): Record<string, number> {
        return {
            MARKET_CAP: 1,
            PRICE: 2,
            VOLUME: 3,
        };
    }

    public static get chartMetricMap(): Record<number, string> {
        return {
            [this.chartMetricKey.MARKET_CAP]: 'market_cap',
            [this.chartMetricKey.PRICE]: 'price',
            [this.chartMetricKey.VOLUME]: 'volume',
        };
    }

    public static get noTickersFoundTerminalResponse(): string {
        return "The model is currently being fine-tuned and upgraded. Please provide the token ticker for the most accurate response, For example: $LOKY, $AIXBT.";
    }

    public static get whitelistedType(): Record<string, number> {
        return {
            NOT_WHITELISTED: 0,
            INTERNAL_TEAM_WHITELISTED: 1,
            EXTERNAL_USER_WHITELISTED: 2,
        };
    }

    public static get poolType(): Record<string, string> {
        return {
            VIRTUAL: 'VIRTUAL',
        };
    }

    public static get signalSubCategory(): Record<string, string> {
        return {
            RED_PILLED: 'red_pilled',
            ONE_HOUR_PRICE_TOP_GAINER: '1hr_price_top_gainer',
            ONE_HOUR_PRICE_TOP_LOSER: '1hr_price_top_loser',
            ONE_DAY_MCAP_TOP_GAINER: '24hr_mcap_top_gainer',
            ONE_DAY_MCAP_TOP_LOSER: '24hr_mcap_top_loser',
            COIN_GECKO_LISTED: 'coin_gecko_listed'
        }
    }

    public static redPilledSignalText(tokenName: string, tokenTicker: string): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Just graduated on Virtuals!`;
    }

    public static newlyLaunchedSignalText(tokenName: string, tokenTicker: string): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Just launched on Virtuals!`;
    }

    public static newlyLaunchedGenesisSignalText(tokenName: string, tokenTicker: string): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Genesis Agent just launched on Virtuals!`;
    }

    public static rugStatusChangeSignalText(tokenName: string, tokenTicker: string, newRugStatus: string, devWalletSoldPercentage: string): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Rug Risk Increased - Now **${newRugStatus}**! Developer wallet **sold ${devWalletSoldPercentage}%** of holdings.`;
    }

    public static smartMoneyMovementText(
        tokenName: string,
        tokenTicker: string,
        buyPrice: string,
        sellPrice: string,
        icon: string
    ): string {
        const prefix = `**${tokenName} [\`$${tokenTicker}\`]:** Smart Money Movement:`;
        if (buyPrice === "0") return `${prefix} $${sellPrice} tokens sold (${icon}) in last 30 minutes`;
        if (sellPrice === "0") return `${prefix} $${buyPrice} tokens bought (${icon}) in last 30 minutes`;
        return `${prefix} $${buyPrice} bought & $${sellPrice} sold (${icon}) in last 30 minutes`;
    }

    public static oneHourPriceTopGainerSignalText(
        tokenName: string,
        tokenTicker: string,
        changePercentage: number,
    ): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Price spiked by [**+${changePercentage}%**] in the last hour.`;
    }

    public static oneHourPriceTopLoserSignalText(
        tokenName: string,
        tokenTicker: string,
        changePercentage: number,
    ): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Price dropped by [**${changePercentage}%**] in the last hour.`;
    }

    public static oneDayMcapTopGainerSignalText(
        tokenName: string,
        tokenTicker: string,
        changePercentage: number,
    ): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Market Cap up by [**+${changePercentage}%**] in the last 24 hours.`;
    }

    public static oneDayMcapTopLoserSignalText(
        tokenName: string,
        tokenTicker: string,
        changePercentage: number,
    ): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Market Cap dropped by [**${changePercentage}%**] in the last 24 hours.`;
    }

    public static coinGeckoListedSignalText(tokenName: string, tokenTicker: string): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Listed on CoinGecko!`;
    }

    public static holderCountChange24hText(
        tokenName: string,
        tokenTicker: string,
        changePercentage: number
    ): string {
        return `**${tokenName} [\`$${tokenTicker}\`]:** Holder count up by [**+${changePercentage}%**] in the last 24 hours`;
    }

    public static whaleWalletAlert15minsText(
        tokenName: string,
        tokenTicker: string,
        buyAmount: string,
        sellAmount: string
    ): string {
        const prefix = `**${tokenName} [\`$${tokenTicker}\`]:** Whale Wallet Alert:`;
        if (buyAmount === "0") return `${prefix} $${sellAmount} tokens sold in last 15 minutes`;
        if (sellAmount === "0") return `${prefix} $${buyAmount} tokens bought in last 15 minutes`;
        return `${prefix} $${buyAmount} bought & $${sellAmount} sold in last 15 minutes`;
    }

    public static get newTokenCutoffDate(): Date {
        return new Date('2024-12-28');
    }

    public static get engineOpenAI(): string {
        return 'OpenAI';
    }

    public static get engineAzureAI(): string {
        return 'AzureAI';
    }

    public static get engineNearAI(): string {
        return 'NearAI';
    }

    public static get bearerToken(): string {
        return process.env.TWITTER_BOT_BEARER_TOKEN!;
    }

    public static get lokyXUserId(): string {
        return '1821266156654571520';
    }

    public static get agentTesting24XUserId(): string {
        return "1876213227165474816"
    }

    public static get agentTesting24XUserName(): string {
        return "agenttesting24"
    }

    public static get lokyXUsername(): string {
        return "0xloky_ai";
    }

    public static get lokyTokenSymbol(): string {
        return "loky";
    }

    public static get lokyTag(): string {
        return "@0xloky_ai";
    }

    public static get maxTweetsToScrapePerHandle(): number {
        return 5;
    }

    public static get conversationHistoryLimit(): number {
        return 1;
    }

    public static get maxRepliesPerUser(): number {
        return 1;
    }

    public static get tokensType(): Record<string, string> {
        return {
            QUESTION: 'question',
            ANSWER: 'answer',
        };
    }

    public static generateXHandleLink(handle: string): string {
        if (!handle) {
            return "";
        }
        return `https://x.com/${handle}`;
    }

    public static get platform(): Record<string, number> {
        return {
            x: 1,
        };
    }

    public static get lastReplyPostType(): Record<string, string> {
        return {
            REPLY: 'ReplyOnLokyPost',
        };
    }

    public static get repliedToPostType(): Record<string, string> {
        return {
            REPLY: 'reply',
            MENTION: 'mention',
            QUOTE: 'quote',
        };
    }

    public static skipUserPostForMentionAndComment(): string[] {
        return [
            "4889380699",//abhinav
            "60344721", //abhay
            "1562766301767946241",//mannan
        ];
    }

    public static get getCreatedPostLogTestId(): string {
        return 'Env_name-TestId'
    }

    public static get technicalIndicatorsPeriod(): Record<string, number> {
        return {
            RSI: 14,
            SMA: 50,
            SUPPORT_RESISTANCE: 50
        };
    }

    public static get technicalIndicatorsBatchSize(): number {
        return 150;
    }

    public static get newVirtualTokensFDEndpoints() {
        return {
            'fifty_percentage_holding_wallet_count': `https://api.dapplooker.com/chart/8c4bbd96-9395-4e9f-8186-ec346ce4146d?api_key=${this.aiUserAPIKey}&output_format=json`,
            'total_holder_count': `https://api.dapplooker.com/chart/1b3d67fb-4673-45d9-ac8e-a8ff557aed62?api_key=${this.aiUserAPIKey}&output_format=json`,
            'holder_count_change': `https://api.dapplooker.com/chart/e069d18f-3288-49ac-a044-4fe38cc4e8af?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_outflow': `https://api.dapplooker.com/chart/2080b231-e66e-436b-b53b-083c5774728d?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_metics': `https://api.dapplooker.com/chart/aa8704d5-c145-4d05-9ec4-fda6cfbceaf5?api_key=${this.aiUserAPIKey}&output_format=json`,
            'virtual_token_data': `https://api.dapplooker.com/chart/2c837726-3aac-4879-99a1-873fcf8d79a4?api_key=${this.aiUserAPIKey}&output_format=json`,
        }
    }

    public static get oldVirtualTokensFDEndpoints() {
        return {
            'fifty_percentage_holding_wallet_count': `https://api.dapplooker.com/chart/d81f8334-4359-4e79-8b8d-4638a93076af?api_key=${this.aiUserAPIKey}&output_format=json`,
            'total_holder_count': `https://api.dapplooker.com/chart/b560e516-cb5b-48ff-938b-31c73075149b?api_key=${this.aiUserAPIKey}&output_format=json`,
            'holder_count_change': `https://api.dapplooker.com/chart/5ff0f894-a69a-4fe9-a9d6-837611d874aa?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_outflow': `https://api.dapplooker.com/chart/9814a565-ac50-4db0-b236-f72f41af0a30?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_metics': `https://api.dapplooker.com/chart/6b7f8006-25f4-4c7e-a0a8-f86d61030377?api_key=${this.aiUserAPIKey}&output_format=json`
        }
    }

    public static get migratedVirtualTokensFDEndpoints() {
        return {
            'fifty_percentage_holding_wallet_count': `https://api.dapplooker.com/chart/660a2e8b-c018-40da-93c3-5e5844ef1247?api_key=${this.aiUserAPIKey}&output_format=json`,
            'token_holder_count': `https://api.dapplooker.com/chart/64f04e32-ee70-47ab-a580-aba461ff7380?api_key=${this.aiUserAPIKey}&output_format=json`,
            'holder_count_change': `https://api.dapplooker.com/chart/9c81d723-85dd-40cf-a6c8-efbca5dea00f?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_outflow': `https://api.dapplooker.com/chart/332e49a2-7bd6-4506-bea4-6a9009a37303?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_metics': `https://api.dapplooker.com/chart/8bd1987c-c6e9-43ac-bce9-52c3ccd61e1c?api_key=${this.aiUserAPIKey}&output_format=json`,
        };
    }

    public static get prototypeVirtualTokensFDEndpoints() {
        return {
            'fifty_percentage_holding_wallet_count': `https://api.dapplooker.com/chart/087b414a-0965-4968-b1b1-bbefc567b19f?api_key=${this.aiUserAPIKey}&output_format=json`,
            'top_25_holder_buy_sell': `https://api.dapplooker.com/chart/87ca3e1c-3aa7-4967-9b1d-5b0fdad17b0f?api_key=${this.aiUserAPIKey}&output_format=json`,
            'token_holder_count': `https://api.dapplooker.com/chart/6c0e9d6b-b137-45d8-a054-d3dd8d5ebb12?api_key=${this.aiUserAPIKey}&output_format=json`,
            'holder_count_change': `https://api.dapplooker.com/chart/7f8fb8af-f0eb-4bbd-839e-6c50f36ec02f?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_outflow': `https://api.dapplooker.com/chart/0c7e4632-4e67-4591-a73f-ebfbc67fb122?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_metics': `https://api.dapplooker.com/chart/804f7084-f947-4c6d-be77-883163c00edf?api_key=${this.aiUserAPIKey}&output_format=json`,
        };
    }

    public static get genesisVirtualTokensFDEndpoints() {
        return {
            'fifty_percentage_holding_wallet_count': `https://api.dapplooker.com/chart/b08c27c3-0291-4373-a9cc-d9d39d560cb9?api_key=${this.aiUserAPIKey}&output_format=json`,
            'token_holder_count': `https://api.dapplooker.com/chart/f1dd1acd-5c0b-42f7-b97c-28d26e8d3104?api_key=${this.aiUserAPIKey}&output_format=json`,
            'holder_count_change': `https://api.dapplooker.com/chart/7fddc8f7-bfc4-4c87-8815-37ec9b33ed59?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_outflow': `https://api.dapplooker.com/chart/eff323b0-59fe-426d-a8a9-43c35a2ffd0b?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_metics': `https://api.dapplooker.com/chart/bd6c0013-d2e5-4968-a414-5fe7b5f28b21?api_key=${this.aiUserAPIKey}&output_format=json`,
            'mcap_price_change': `https://api.dapplooker.com/chart/e2da94ff-6d14-41ca-9090-64eeca85038e?api_key=${this.aiUserAPIKey}&output_format=json`, // For genesis, all endpoint will not return this data
            'mcap_ath': `https://api.dapplooker.com/chart/4e5e9d65-960e-4745-a692-4540e6817b64?api_key=${this.aiUserAPIKey}&output_format=json`,
        };
    }

    public static get ethereumVirtualTokensFDEndpoints() {
        return {
            'fifty_percentage_holding_wallet_count': `https://api.dapplooker.com/chart/d11a0b38-e644-4b8b-b39b-b2dff04fc656?api_key=${this.aiUserAPIKey}&output_format=json`,
            'token_holder_count': `https://api.dapplooker.com/chart/8abe488f-3797-4a70-b551-b64aedf4570f?api_key=${this.aiUserAPIKey}&output_format=json`,
            'holder_count_change': `https://api.dapplooker.com/chart/140a9501-da1c-49b7-bbdd-f725623527cc?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_outflow': `https://api.dapplooker.com/chart/a920217a-1cb8-4566-9230-9af304bc17d1?api_key=${this.aiUserAPIKey}&output_format=json`,
            'dev_wallet_metics': `https://api.dapplooker.com/chart/86258723-a605-4f7d-981a-f58418748239?api_key=${this.aiUserAPIKey}&output_format=json`,
            'mcap_price_change': `https://api.dapplooker.com/chart/e2da94ff-6d14-41ca-9090-64eeca85038e?api_key=${this.aiUserAPIKey}&output_format=json`, // For genesis, all endpoint will not return this data
            'mcap_ath': `https://api.dapplooker.com/chart/4e5e9d65-960e-4745-a692-4540e6817b64?api_key=${this.aiUserAPIKey}&output_format=json`,
        };
    }

    // Endpoints contains all prototype/sentient/genesis tokens paginated data
    public static get allTokensDataEndpoints() {
        return {
            'top_25_holder_buy_sell': `https://api.dapplooker.com/chart/b2b29d86-c7a4-4de9-874b-6e1646dc88de?api_key=${this.aiUserAPIKey}&output_format=json`,
            'mcap_price_change': `https://api.dapplooker.com/chart/e38404a5-c168-45b6-93ff-ded787a87d65?api_key=${this.aiUserAPIKey}&output_format=json`,
            'first_100_token_buyers': `https://api.dapplooker.com/chart/bfb3eb6c-b5cd-4f63-b075-93cc680e1582?api_key=${this.aiUserAPIKey}&output_format=json`,
            'top_holders_balances': `https://api.dapplooker.com/chart/c3bdc4fe-d230-4b29-8867-cac186edd78e?api_key=${this.aiUserAPIKey}&output_format=json`,
        }
    }

    public static get bubbleMapEndpoints() {
        return {
            devWalletsData: `https://api.dapplooker.com/chart/30e90ee1-6a41-4c2f-8cee-a120c539373b?api_key=${this.aiUserAPIKey}&output_format=json`,
            top25HoldersData: `https://api.dapplooker.com/chart/6e24f11c-daf8-456d-a660-6763a015c303?api_key=${this.aiUserAPIKey}&output_format=json`,
        }
    }

    public static virtualsWhaleWalletEndpoints(): string[] {
        return [
            `https://api.dapplooker.com/chart/1cdd4a13-40cd-4fe3-a713-830dc066f000?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/054954d7-6d0c-4c6f-a4b5-0f5ff843e220?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/781ce102-7f88-4a57-9162-e88cfd071a2b?api_key=${this.aiUserAPIKey}&output_format=json`,
            `https://api.dapplooker.com/chart/9739dfbe-fc9d-495c-96ca-eaca02f9903b?api_key=${this.aiUserAPIKey}&output_format=json`,
        ];
    }

    public static virtualsFirstBuyersByTokenAddressEndpointDL(tokenAddress: string): string {
        return `https://api.dapplooker.com/chart/827184d9-8c6b-4aa0-ade0-24ca082ca959?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"token_address":"${tokenAddress}"}`;
    }

    public static virtualsGenesisParticipantAnalysisByTokenAddressEndpointDL(tokenAddress: string): string {
        return `https://api.dapplooker.com/chart/b611dc2f-1cfb-43ff-93ea-26e646baeab1?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"token_address":"${tokenAddress}"}`;
    }

    public static get geckoTerminalNetworkBaseUrl(): string {
        return 'https://api.geckoterminal.com/api/v2/networks/base';
    }

    public static get populateDataSourceMap(): Record<string, number> {
        return {
            DAPPLOOKER: 1,
            COINGECKO: 2
        }
    }

    public static virtualsLink(virtualId: number): string {
        return `https://app.virtuals.io/virtuals/${virtualId}`;
    }

    public static get getReplyContentGenerationTokenLimit(): number {
        return 300;
    }

    public static get smartMoneyMovement(): string {
        return `SmartMoneyMovementAlert`;
    }

    public static postDescription(): Record<string, string> {
        return {
            [this.smartMoneyMovement]: "🚨 Smart Money Signal, Top 25 Holders, Last 30 Min 🚨\n\n📊 Here's what the big players are doing:\n"
        }
    }

    public static get getPerDayLokyReplyLimit(): number {
        return 60;
    }

    public static get lokyTokenId(): string {
        return 'loky-by-virtuals'
    }

    public static get networkBase() {
        return 'BASE';
    }

    public static get networkSolana() {
        return 'SOLANA';
    }

    public static get allNetworks() {
        return 'ALL';
    }

    public static get networkEthereum() {
        return 'ETH';
    }

    public static get categoryVsNetworkMap(): Record<string, string> {
        return {
            [this.categoryVirtualEcosystem]: this.networkBase,
            [this.categoryVirtualEcosystemPrototype]: this.networkBase,
            [this.categorySolanaEcosystem]: this.networkSolana,
            [this.categoryBaseEcosystem]: this.networkBase,
            [this.categoryVirtualEcosystemGenesis]: this.networkBase
        }
    }

    public static get network(): Record<string, number> {
        return {
            [this.networkBase]: 1,
            [this.networkSolana]: 2,
            [this.networkEthereum]: 3
        }
    }

    public static get networkMap(): Record<number, string> {
        return {
            1: 'base',
            2: 'solana',
            3: 'eth'
        };
    }

    public static get categoryVirtualEcosystem(): string {
        return 'virtuals-protocol-ecosystem';
    }

    public static get categoryVirtualEcosystemPrototype(): string {
        return 'virtuals-protocol-ecosystem-prototype';
    }

    public static get categoryVirtualEcosystemGenesis(): string {
        return 'virtuals-protocol-ecosystem-genesis';
    }

    public static get categorySolanaEcosystem(): string {
        return 'solana-ecosystem';
    }

    public static get categorySolanaMeme(): string {
        return 'solana-meme-coins';
    }

    public static get categoryBaseEcosystem(): string {
        return 'base-ecosystem';
    }

    public static get supportedCategories(): string[] {
        return [
            this.categoryVirtualEcosystem,
            this.categoryVirtualEcosystemPrototype,
            this.categorySolanaEcosystem,
            this.categorySolanaMeme,
            this.categoryBaseEcosystem,
            this.categoryVirtualEcosystemGenesis
        ];
    }

    public static get supportedChainsForLokyAPIs(): string[] {
        return [
            this.networkBase,
            this.networkSolana,
        ];
    }

    public static get supportedEcosystemsForLokyAPIs(): string[] {
        return [
            this.ecosystemVirtuals,
            this.ecosystemBase,
            this.ecosystemSolana
        ];
    }

    public static get kosherClient(): string {
        return 'kosher';
    }

    public static get supportedClients(): string[] {
        return [this.kosherClient];
    }

    public static get subCategoryIdMap(): Record<string, number> {
        return {
            [this.subCategoryVirtualSentient]: 1,
            [this.subCategoryVirtualPrototype]: 2,
            [this.subCategoryVirtualGenesis]: 3
        }
    }

    public static get subCategoryVirtualSentient(): string {
        return 'sentient';
    }

    public static get subCategoryVirtualGenesis(): string {
        return 'genesis';
    }

    public static get subCategoryVirtualPrototype(): string {
        return 'prototype';
    }

    public static get categorySolana(): string {
        return 'solana';
    }

    public static getCandleDataCategoriesByNetwork(network: number): string[] {
        if (network == this.network.BASE) {
            return [this.categoryVirtualEcosystem, this.categoryBaseEcosystem, this.categoryVirtualEcosystemGenesis];
        }
        if (network == this.network.SOLANA) {
            return [this.categorySolanaEcosystem, this.categorySolanaMeme];
        }
        if (network == this.network.ETH) {
            return [this.categoryVirtualEcosystemGenesis];
        }
        throw new Error(`AIAgentConstant::getCandleDataCategoriesByNetwork::Invalid network: ${network}`);
    }

    public static getPoolNamePatternByCategory(category: string): string | null {
        if (category === this.categoryVirtualEcosystem || category === this.categoryVirtualEcosystemGenesis) {
            return '/ VIRTUAL';
        }

        if (category === this.categoryBaseEcosystem) {
            return null; // We will use Most Popular pool available for this category
        }

        if ([this.categorySolanaEcosystem, this.categorySolanaMeme].includes(category)) {
            return '/ USDC';
        }
        throw new Error(`AIAgentConstant::getPoolNamePatternByCategory::Invalid category: ${category}`);
    }

    public static getFormattedAddress(address: string, network: number): string {
        return (network == AIAgentConstant.network[AIAgentConstant.networkSolana]) ? address : address?.toLowerCase();
    }

    public static get categoryMap(): Record<string, number> {
        return {
            [this.categoryVirtualEcosystem]: 1,
            [this.categorySolanaEcosystem]: 2,
            [this.categoryVirtualEcosystemPrototype]: 3,
            [this.categoryBaseEcosystem]: 4,
            [this.categoryVirtualEcosystemGenesis]: 5,
            all: 6
        }
    }

    public static get categoryMapInvert(): Record<number, string> {
        return Object.entries(this.categoryMap).reduce((acc: Record<number, string>, [key, value]) => {
            acc[value] = key;
            return acc;
        }, {});
    }

    public static get questionSource(): Record<string, number> {
        return {
            TERMINAL: 1,
            API: 2,
            API_BY_CLIENT: 3
        }
    }

    public static get answerFromDBTimeLimitInMinutes(): number {
        return 45;
    }

    public static get askAPIEcosystemToCategoryMap(): Record<string, string> {
        return {
            [this.ecosystemVirtuals]: this.categoryVirtualEcosystem,
            [this.ecosystemSolana]: this.categorySolanaEcosystem,
        }
    }


    public static get networkCategoryMap(): Record<string, string> {
        return {
            [this.networkBase]: this.categoryBaseEcosystem,
            [this.networkSolana]: this.categorySolanaEcosystem,
            [this.allNetworks]: 'all'
        }
    }

    public static get categoryNetworkMap(): Record<string, string> {
        return {
            [this.categoryBaseEcosystem]: this.networkBase,
            [this.categorySolanaEcosystem]: this.networkSolana,
            all: this.allNetworks
        }
    }

    public static get categoryEcosystemMap(): Record<string, string> {
        return {
            [this.categoryVirtualEcosystem]: this.ecosystemVirtuals,
            [this.categoryVirtualEcosystemPrototype]: this.ecosystemVirtuals,
            [this.categorySolanaEcosystem]: this.ecosystemSolana,
            [this.categorySolanaMeme]: this.ecosystemSolana,
            [this.categoryBaseEcosystem]: this.ecosystemBase,
            [this.categoryVirtualEcosystemGenesis]: this.ecosystemVirtuals,
        }
    }

    public static get ecosystemCategoriesMap(): Record<string, string[]> {
        return {
            [this.categoryVirtualEcosystem]: [this.categoryVirtualEcosystem, this.categoryVirtualEcosystemPrototype],
            [this.categorySolanaEcosystem]: [this.categorySolanaEcosystem, this.categorySolanaMeme],
            [this.categoryBaseEcosystem]: [this.categoryBaseEcosystem, this.categoryVirtualEcosystem, this.categoryVirtualEcosystemPrototype, this.categoryVirtualEcosystemGenesis],
            all: [
                this.categoryVirtualEcosystem,
                this.categoryVirtualEcosystemGenesis,
                this.categoryVirtualEcosystemPrototype,
                this.categorySolanaEcosystem,
                this.categorySolanaMeme,
                this.categoryBaseEcosystem
            ]
        }
    }

    public static get chainCategoriesMap(): Record<string, string[]> {
        return {
            [this.networkBase]: [this.categoryBaseEcosystem, this.categoryVirtualEcosystem, this.categoryVirtualEcosystemPrototype, this.categoryVirtualEcosystemGenesis],
            [this.networkSolana]: [this.categorySolanaEcosystem, this.categorySolanaMeme],
            ALL: [
                this.categoryVirtualEcosystem,
                this.categoryVirtualEcosystemGenesis,
                this.categoryVirtualEcosystemPrototype,
                this.categorySolanaEcosystem,
                this.categorySolanaMeme,
                this.categoryBaseEcosystem
            ]
        }
    }

    public static get chainEcosystemCategoryMap(): Record<string, string> {
        return {
            [this.networkBase]: this.categoryBaseEcosystem,
            [this.networkSolana]: this.categorySolanaEcosystem
        }
    }

    public static get ecosystemVirtuals(): string {
        return 'virtuals';
    }

    public static get ecosystemSolana(): string {
        return 'solana';
    }

    public static get ecosystemBase(): string {
        return 'base';
    }

    public static get publicEcosystemToCategoriesMap(): Record<string, string[]> {
        return {
            [this.ecosystemVirtuals]: [this.categoryVirtualEcosystem, this.categoryVirtualEcosystemPrototype, this.categoryVirtualEcosystemGenesis],
            [this.ecosystemSolana]: [this.categorySolanaEcosystem, this.categorySolanaMeme],
            [this.ecosystemBase]: [this.categoryBaseEcosystem, this.categoryVirtualEcosystem, this.categoryVirtualEcosystemGenesis],
            all: [
                this.categoryVirtualEcosystem,
                this.categoryVirtualEcosystemGenesis,
                this.categoryVirtualEcosystemPrototype,
                this.categorySolanaEcosystem,
                this.categorySolanaMeme,
                this.categoryBaseEcosystem
            ]
        }
    }

    public static get publicSubCategoryToCategoriesMap(): Record<string, string[]> {
        return {
            [this.subCategoryVirtualSentient]: [this.categoryVirtualEcosystem, this.categoryVirtualEcosystemGenesis],
            [this.subCategoryVirtualPrototype]: [this.categoryVirtualEcosystemPrototype],
            [this.subCategoryVirtualGenesis]: [this.categoryVirtualEcosystemGenesis]
        }
    }

    public static get cookieFunChainIdToNetworkMap(): Record<number, string> {
        return {
            [-2]: this.networkSolana,
            8453: this.networkBase,
            1: this.networkEthereum
        }
    }

    public static get cookieFunNetworkToChainIdMap(): Record<string, number> {
        return {
            [this.networkBase]: 8453
        }
    }

    public static get mindshareDataTimeFrames(): Record<string, string> {
        return {
            SEVEN_DAYS: '7d',
            THREE_DAYS: '3d'
        }
    }

    public static get mindshareDataPeriods(): Record<string, string> {
        return {
            [this.mindshareDataTimeFrames.SEVEN_DAYS]: '_7DaysAgo',
            [this.mindshareDataTimeFrames.THREE_DAYS]: '_3DaysAgo'
        }
    }

    public static get entityType(): Record<string, number> {
        return {
            VIRTUAL_ECOSYSTEM_TOTAL_MARKET_CAP: 0,
            TOP_TEN_AGENTS_BY_MINDSHARE: 1,
            TOP_TEN_AGENTS_BY_MARKET_CAP: 2
        }
    }

    public static get topTenAgentsMetrics(): Record<string, string> {
        return {
            MINDSHARE: 'Mindshare',
            MARKET_CAP: 'MarketCap'
        }
    }

    public static get rugStatusMap(): Record<string, number> {
        return {
            SAFE: 0,
            CAUTION: 1,
            HIGH_RISK: 2,
            RUGGED: 3
        }
    }

    public static get rugStatus(): Record<number, string> {
        return {
            0: 'SAFE',
            1: 'CAUTION',
            2: 'HIGH_RISK',
            3: 'RUGGED'
        }
    }

    public static get rugStatusDisplayMap(): Record<number, string> {
        return {
            0: 'Safe',
            1: 'Caution',
            2: 'High Risk',
            3: 'Rugged'
        }
    }

    public static devBundleTransactionsEndpointDL(devWalletAddresses: string, page: number, from?: string, to?: string): string {
        return `https://api.dapplooker.com/chart/aaf7ab44-bf17-414d-aa89-1ecfe0b392af?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"dev_wallet_address":"${devWalletAddresses}", "page": "${page}"${from ? `,"from":"${from}"` : ''}${to ? `,"to":"${to}"` : ''}}`;
    }

    public static devBundleTransactionsTotalPagesEndpointDL(devWalletAddresses: string, from?: string, to?: string): string {
        return `https://api.dapplooker.com/chart/49b10534-3526-4e8f-b64c-01d0476b8bd2?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"dev_wallet_address":"${devWalletAddresses}"${from ? `,"from":"${from}"` : ''}${to ? `,"to":"${to}"` : ''}}`;
    }

    public static devFundingsEndpointDL(tokenAddress: string, devWalletAddresses: string, page: number, from?: string): string {
        return `https://api.dapplooker.com/chart/b0cca06b-bf0b-4dbf-a5a4-2780a995b626?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"page":"${page}","token_address":"${tokenAddress}","dev_wallet_address":"${devWalletAddresses}"${from ? `,"from":"${from}"` : ''}}`;
    }

    public static devFundingsTotalPagesEndpointDL(tokenAddress: string, devWalletAddresses: string, from?: string): string {
        return `https://api.dapplooker.com/chart/35f3b7a5-89f0-45d3-b415-af5b9b2603ef?api_key=${this.aiUserAPIKey}&output_format=json&filterParams={"token_address":"${tokenAddress}","dev_wallet_address":"${devWalletAddresses}"${from ? `,"from":"${from}"` : ''}}`;
    }

    public static devWalletTypeMap(): Record<string, number> {
        return {
            'TOP_25_HOLDER': -1,
            'DEV_WALLET': 0,
            'SPREAD_WALLET': 1,
            'SPREAD_WALLET_LVL2': 2
        }
    }

    public static get top25Holder(): string {
        return 'TOP_25_HOLDER';
    }

    public static get devWallet(): string {
        return 'DEV_WALLET';
    }

    public static get spreadWallet(): string {
        return 'SPREAD_WALLET';
    }

    public static get spreadWalletLvl2(): string {
        return 'SPREAD_WALLET_LVL2';
    }

    public static get devBundleTransactionType(): Record<string, number> {
        return {
            IN: 0,
            OUT: 1
        };
    }

    public static get apiType(): Record<string, number> {
        return {
            'agents': 0,
            'crypto-market': 1,
            'ask-loky': 2,
            'agent-metainfo': 3,
            'crypto-metainfo': 4,
            'token-ta': 5,
            'trending-tokens': 6,
            'crypto-market-historical': 7,
            'wallet-safety-score': 8
        }
    }

    public static get apiResponseStatusCode(): Record<string, number> {
        const oThis = this;
        return {
            [oThis.APIStatusSuccess]: 1,
            [oThis.APIStatusPending]: 2,
            [oThis.APIStatusFailed]: 3
        }
    }

    public static get APIStatusSuccess(): string {
        return "SUCCESS";
    }

    public static get APIStatusPending(): string {
        return "PENDING";
    }

    public static get APIStatusFailed(): string {
        return "FAILED";
    }

    public static apiUrlMap(): Record<string, string> {
        return {
            'agents': `/api/v1/agents`,
            'crypto-market': `/api/v1/crypto-market`,
            'ask-loky': `/api/v1/ask-loky`,
            'token-ta': `/api/v1/token-ta`,
            'trending-tokens': `/api/v1/trending`,
            'crypto-market-historical': '/api/v1/crypto-market-historical'
        };
    }

    public static get minimumLiquidityForExtendedTAData(): number {
        return 50000;
    }

    public static get genesisPublicSaleSupplyPercentage(): number {
        return 37.5;
    }

    public static get genesisMaxCommitableVirtualsForAgentByUser(): number {
        return 566;
    }

    public static get genesisMaxCommitableVirtualsForAgent(): number {
        return 42425;
    }

    public static get genesisFixFDVInVirtuals(): number {
        return 112000;
    }

    public static get tokenLimitForFetchMindshareData(): number {
        return 500;
    }

    public static get genesisTokenStatus(): Record<string, string> {
        return {
            INITIALIZED: 'INITIALIZED',
            STARTED: 'STARTED',
            FINALIZED: 'FINALIZED',
            FAILED: 'FAILED',
            CANCELLED: 'CANCELLED',
        }
    }

    public static get genesisTokenStatusMap(): Record<string, number> {
        return {
            INITIALIZED: 0,
            STARTED: 1,
            FINALIZED: 2,
            FAILED: 3,
            CANCELLED: 4,
            PREPROCESSING: 1, // Temporary status
            PROCESSING: 1 // Temporary status
        }
    }

    public static get genesisTokenStatusInvert(): Record<number, string> {
        return {
            0: 'INITIALIZED',
            1: 'STARTED',
            2: 'FINALIZED',
            3: 'FAILED',
            4: 'CANCELLED',
        }
    }

    public static getTerminalChatTokenCategory(category: string): string {
        const categoryMap: Record<string, string> = {
            [AIAgentConstant.categoryBaseEcosystem]: 'base ecosystem',
            [AIAgentConstant.categoryVirtualEcosystem]: 'sentient',
            [AIAgentConstant.categoryVirtualEcosystemPrototype]: 'prototype',
            [AIAgentConstant.categoryVirtualEcosystemGenesis]: 'genesis'
        }
        return categoryMap[category];
    }
}
