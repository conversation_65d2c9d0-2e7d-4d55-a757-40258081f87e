'use strict';

module.exports = {
    up: async (queryInterface: any, Sequelize: any) => {
        await queryInterface.createTable(
            'wallet_safety_score',
            {
                id: {
                    type: Sequelize.INTEGER,
                    autoIncrement: true,
                    primaryKey: true,
                },
                genesis_id: {
                    type: Sequelize.INTEGER,
                    allowNull: false,
                },
                genesis_address: {
                    type: Sequelize.STRING(100),
                    allowNull: false,
                },
                member_wallet_address: {
                    type: Sequelize.STRING(100),
                    allowNull: false,
                },
                wallet_title: {
                    type: Sequelize.STRING,
                    allowNull: true,
                },
                safety_score: {
                    type: Sequelize.SMALLINT,
                    allowNull: true,
                },
                wallet_age_points: {
                    type: Sequelize.SMALLINT,
                    allowNull: true,
                },
                wallet_tx_activity_points: {
                    type: Sequelize.SMALLINT,
                    allowNull: true,
                },
                created_at: {
                    type: Sequelize.DATE
                },
                updated_at: {
                    type: Sequelize.DATE
                },
            }
        );

        await queryInterface.addIndex('wallet_safety_score', {
            unique: true,
            fields: ["genesis_address", "member_wallet_address"],
        });

        await queryInterface.addIndex('wallet_safety_score', {
            unique: false,
            fields: ["genesis_id"],
        });
    },

    down: async (queryInterface: any, Sequelize: any) => {
        await queryInterface.dropTable('wallet_safety_score');
    },
};
