'use strict';

module.exports = {
  up: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.createTable(
      'user_watchlist_genesis',
      {
        id: {
          type: Sequelize.INTEGER,
          autoIncrement: true,
          primaryKey: true,
        },
        user_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE'
        },
        genesis_id: {
          type: Sequelize.INTEGER,
          allowNull: false,
          references: {
            model: 'genesis_lqa_data',
            key: 'genesis_id',
          },
          onDelete: "CASCADE",
          onUpdate: "CASCADE"
        },
        token_id: {
          type: Sequelize.STRING,
          allowNull: true,
        },
        created_at: {
          type: Sequelize.DATE,
          defaultValue: new Date()
        }
      }
    );

    await queryInterface.addConstraint("user_watchlist_genesis", {
      fields: ["user_id", "genesis_id"],
      type: "unique",
      name: "user_watchlist_genesis_user_id_genesis_id"
    });

    await queryInterface.addIndex("user_watchlist_genesis", {
      fields: ["user_id"]
    });

    await queryInterface.addIndex("user_watchlist_genesis", {
      fields: ["genesis_id"]
    });

    await queryInterface.addIndex("user_watchlist_genesis", {
      fields: ["token_id"]
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.dropTable('user_watchlist_genesis');
  },
};
