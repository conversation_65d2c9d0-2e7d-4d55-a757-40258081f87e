"use strict";

module.exports = {
  up: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.createTable("ethereum_candle_data", {
      id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true,
      },
      token_id: {
        type: Sequelize.STRING(80),
        allowNull: false,
      },
      timestamp: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      open: {
        type: Sequelize.DECIMAL(25, 5),
        allowNull: false,
      },
      high: {
        type: Sequelize.DECIMAL(25, 5),
        allowNull: false,
      },
      low: {
        type: Sequelize.DECIMAL(25, 5),
        allowNull: false,
      },
      close: {
        type: Sequelize.DECIMAL(25, 5),
        allowNull: false,
      },
      volume: {
        type: Sequelize.DECIMAL(25, 2),
        allowNull: false,
      },
    });

    await queryInterface.addConstraint("ethereum_candle_data", {
      fields: ["token_id", "timestamp"],
      type: "unique",
      name: "ethereum_candle_data_token_id_timestamp"
    });
  },

  down: async (queryInterface: any, Sequelize: any) => {
    await queryInterface.dropTable("ethereum_candle_data");
  },
};
