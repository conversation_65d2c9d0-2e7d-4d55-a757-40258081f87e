import MarketStatistics from "../models/MarketStatistics";
import PostLog from "../models/PostLog";
import TokenDetailsModal from "../models/TokenDetails"
import LastPostTracker from "../models/LastPostTracker";
import PostReplyTracker from "../models/PostReplyTracker";
import Signal from "../models/Signal";
import FailedReplyQueue from "../models/FailedReplyQueue";
import LQAHistoricalMetric from "../models/LQAHistoricalMetric";
import DevBundleAndTopHolders from "../models/DevBundleAndTopHolders";
import TokenPreGraduateAddressMap from "../models/TokenPreGraduateAddressMap";
import UserWaitlist from "../models/UserWaitlist";
import RugWhitelistedAgent from "../models/RugWhitelistedAgent";
import StakedTokenMetrics from "../models/StakedTokenMetrics";

export interface IndexSignatures {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
}

export interface DBConfig {
    user: string;
    host: string;
    password: string;
    port: string;
    database: string;
}

export interface InternalDecodedParamsInterface {
    currentUser?: UserModelAttributes;
    apiName?: string;
    redirectToCookieValue?: string;
    isAddEmailSource?: boolean;
    isTerminalAccess?: boolean;
}

export interface UserModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number | undefined;
    name: string;
    email?: string;
    password?: string;
    walletAddress?: string;
    walletType?: number;
    verifiedEmail: boolean;
    enabledNotification: boolean;
    status: number;
    authToken?: string;
    encryptionSalt?: string;
    avatarIdentifier?: string;
    whitelisted: number;
    network: number;
    createdAt?: Date;
    updatedAt?: Date;
    apiKey?: string;
    lokyBalance?: number;
    lokyBalanceUpdatedAt?: Date;
}

export interface UserWaitlistModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number | undefined;
    name: string;
    email: string;
    tgUsername?: string;
    walletAddress?: string;
    monthlyRequests: string;
    rateLimit: string;
    monthlyBudget: number;
    message: string | null;
    createdAt?: Date;
}

export interface LoginParamsInterface {
    email: string;
    password: string;
    internalDecodedParams: InternalDecodedParamsInterface;
    isWalletLoginSource?: boolean,
}

export interface RegisterUserParamsInterface {
    walletAddress?: string;
    walletType?: string;
    email?: string;
    password?: string;
    name?: string;
    tnc: boolean;
    source?: string;
    network: number;
    internalDecodedParams: InternalDecodedParamsInterface;
}

export interface AddWaitlistParamsInterface {
    name: string;
    email: string;
    tgUsername?: string;
    userWalletAddress: string;
    rateLimit: string;
    monthlyRequests: string;
    monthlyBudget: number;
    message?: string;
    internalDecodedParams: InternalDecodedParamsInterface;
}

export interface SuccessResponse {
    success: boolean;
    data: any;
    statusCode?: number;
}

export interface ErrorResponse {
    success: boolean;
    errorData: Record<string, string>;
    debugOptions: any;
    statusCode?: number;
}

export interface ErrorParamsInterface {
    parameter: string;
    message: string;
}

export interface CurrentUserParamsInterface {
    internalDecodedParams: InternalDecodedParamsInterface;
}

export interface FormattedUserAttributes {
    id: number;
    name: string;
    email: string;
    walletAddress?: string;
    walletType?: string;
    status: string;
    avatarURL: string;
    verifiedEmail: boolean;
    enabledNotification?: boolean;
    whitelisted: number;
    network: number;
    createdAt: Date;
    updatedAt: Date;
    password?: string;
    userXP?: number;
    apiKey?: string;
    lokyBalance?: number;
    lokyBalanceUpdatedAt?: Date;
}

export interface UserLoginCookieAuthParams {
    userCookieValue: string;
    cookieExpiry: number;
}

export interface ErrorLogModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number | undefined;
    appType: number;
    severity: number;
    status: number;
    machineIp: string;
    data: string;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface FormattedErrorLogModelAttributes {
    id: number;
    appType: string;
    severity: string;
    status: string;
    machineIp: string;
    data: string;
    createdAt: Date;
    updatedAt: Date;
}

export interface QueryLogModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id: number | undefined;
    userId: number;
    queryId: string;
    dataProviderId: string;
    responseTime: number;
    status: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface FormattedQueryLogModelAttributes {
    id: number;
    userId: number;
    queryId: string;
    dataProviderId: string;
    responseTime: number;
    status: number;
    createdAt: Date;
    updatedAt: Date;
}

export interface ApiParamsInterface {
    apiName: string;
    internalParams: Record<string, any>;
    externalParams: Record<string, any>;
}

export interface APIParamValidatorInterface {
    parameter: string;
    kind?: string;
    validatorMethods: Record<string, string | null>[];
}

export interface ApiParamSignatureInterface {
    mandatory?: APIParamValidatorInterface[];
    optional?: APIParamValidatorInterface[];
}

export interface WalletLoginParamsInterface {
    walletAddress: string;
    walletType: string;
    network: number;
    internalDecodedParams: InternalDecodedParamsInterface;
}

export interface LogoutParamsInterface {
    internalDecodedParams: InternalDecodedParamsInterface;
    network: number;
}

export interface AiAgentTerminalResponseType {
    text: string;
    chartData?: any;
    tokenDetails?: any[];
    questionId: string;
    answerId: string;
    chartMetric: string;
}
export interface VirtualEcosystemTokenDetails {
    token_category: string;
    token_ticker: any;
    token_name: any;
    token_id: any;
    current_usd_price: any;
    last_24_hrs_price_change_percentage: any;
    market_cap: any;
    last_24_hrs_market_cap_change_percentage: any;
    last_24_hrs_volume: any;
    last_7_day_price_change_percentage: any;
    last_7_day_volume_change_percentage: any;
    last_7_day_market_cap_change_percentage: any;
    last_30_day_price_change_percentage: any;
    last_30_day_volume_change_percentage: any;
    last_30_day_market_cap_change_percentage: any;
    last_24_hrs_price_high: any;
    price_ath: any;
    circulating_supply: any;
    total_supply: any;
    launch_date?: any;
    contract_address: any;
    token_twitter_handle: any;
    token_description?: any;
    support?: any;
    resistance?: any;
    rsi?: any;
    "sma_50h"?: any;
    total_liquidity?: any;
    total_holder_count?: any;
    holder_count_change_percentage_24h?: any;
    dev_wallet?: string;
    dev_wallet_balance?: any;
    dev_wallet_total_holding_percentage?: any;
    dev_wallet_outflow_txs_count_24h?: any;
    dev_wallet_outflow_amount_24h?: any;
    dev_wallet_inflow_amount_24h?: any;
    top_25_holder_buy_24h?: any;
    top_25_holder_sold_24h?: any;
    fifty_percentage_holding_wallet_count?: any;
    virtuals_link?: any;

}

export interface SolanaEcosystemTokenDetails {
    token_ticker: any;
    token_name: any;
    token_id: any;
    current_usd_price: any;
    last_24_hrs_price_change_percentage: any;
    market_cap: any;
    last_24_hrs_market_cap_change_percentage: any;
    last_24_hrs_volume: any;
    last_7_day_price_change_percentage: any;
    last_7_day_volume_change_percentage: any;
    last_7_day_market_cap_change_percentage: any;
    last_30_day_price_change_percentage: any;
    last_30_day_volume_change_percentage: any;
    last_30_day_market_cap_change_percentage: any;
    last_24_hrs_price_high: any;
    price_ath: any;
    circulating_supply: any;
    total_supply: any;
    contract_address: any;
    support?: any;
    resistance?: any;
    rsi?: any;
    "sma_50h"?: any;
}

export interface WhitelistedUsersAttribute {
    address: string;
    type: number;
    user_id: number;
}

export interface Models {
    postLog: PostLog
    tokenDetails: TokenDetailsModal
    lastPostTracker: LastPostTracker
    postReplyTracker: PostReplyTracker
    marketStatistics: MarketStatistics
    signal: Signal
    failedReplyQueue: FailedReplyQueue
    lqaHistoricalMetric: LQAHistoricalMetric
    devBundleAndTopHolders: DevBundleAndTopHolders
    tokenPreGraduateAddressMap: TokenPreGraduateAddressMap
    userWaitlist: UserWaitlist
    stakedTokenMetrics: StakedTokenMetrics
}

export interface ContainerInterface {
    models: Models;
}

export interface CreatePostLogAttribute {
    id: number | undefined
    post_id: string
    token_id: string
    platform: number
}

export interface PublishTokenSummaryPost {
    tokenToProcess: any[];
    promptForSummaryPost: string;
    postCategory: string;
    allTokenData: Record<string, string>[];
    changeAvg: number;
    formattedTokens: any[],
    formattedTokensTg: any[]
}

export interface TokenDetailsObject {
    token: string;
    token_id: string;
    token_name: string;
    category: string;
    handle: string;
    image: string;
    sent: boolean;
    description: string;
    type: number;
};

export interface TokenPoolsObject {
    tokenId: string;
    poolAddress: string;
    poolName: string;
    network: number;
}

export interface TokenCandleDataObject {
    tokenId: string;
    timestamp: Date;
    open: number;
    high: number;
    low: number;
    close: number;
    volume: number;
}

export interface TokenCategoryMapObject {
    tokenId: string;
    category: string;
}

export interface TokenAddressMapObject {
    tokenId: string;
    baseAddress?: string;
    solanaAddress?: string;
    ethereumAddress?: string;
    polygonAddress?: string;
    binanceChainAddress?: string;
    moonriverAddress?: string;
    fantomAddress?: string;
    xdaiAddress?: string;
    arbitrumOneAddress?: string;
    avalancheAddress?: string;
    optimismAddress?: string;
    nearAddress?: string;
    moonbeamAddress?: string;
    celoAddress?: string;
}

export interface ExtendedPoolDetailsObject {
    tokenId: string;
    network: number;
    poolAddress: string;
    poolId: string;
    pairId: string;
}

export interface UserAPIKeyModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    userId: number;
    key: string;
    isDeleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface UserAPIStatsModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    userId: number;
    apiKeyId: number;
    date: Date;
    usageCount: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface LQAHistoricalMetricModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    tokenId: string;
    confidence_15m: number;
    confidence_1h: number;
    confidence_6h: number;
    confidence_24h: number;
    confidence_change_15m: number;
    confidence_change_1h: number;
    confidence_change_6h: number;
    confidence_change_24h: number;
    reasoningSummary: string;
    createdAt?: Date;
}

export interface DevBundleAndTopHoldersModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    tokenId: string;
    tokenAddress?: string;
    walletAddress: string;
    holdingPercentage: number | undefined;
    balance?: number;
    type: number;
    createdAt?: Date;
    updatedAt?: Date;
}

export interface TokenPreGraduateAddressMapModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number;
    token_id: string;
    current_token_address: string;
    pre_graduate_address: string;
    created_at?: Date;
    updated_at?: Date;
}

export interface RugWhitelistedAgentModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number;
    tokenId: string;
    rugStatus: number;
    updatedAt?: Date;
}

export interface StakedTokenMetricsModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    token_id: string;
    token_address: string;
    total_staked_amount: number;
    staked_user_count: number;
    created_at?: Date;
    updated_at?: Date;
}

export interface FormattedApiUsageStatsAttributes {
    date: string;
    count: string;
}

export interface VirtualEcosystemTokenDetailsForXReplyAgent {
    token_ticker: any;
    token_name: any;
    token_id: any;
    contract_address: any,
    current_usd_price: any;
    last_24_hrs_price_change_percentage: any;
    market_cap: any;
    last_24_hrs_market_cap_change_percentage: any;
    last_24_hrs_volume: any;
    last_7_day_price_change_percentage: any;
    last_7_day_volume_change_percentage: any;
    last_7_day_market_cap_change_percentage: any;
    last_30_day_price_change_percentage: any;
    last_30_day_volume_change_percentage: any;
    last_30_day_market_cap_change_percentage: any;
    last_24_hrs_price_high: any;
    price_ath: any;
    circulating_supply: any;
    total_supply: any;
    token_twitter_handle: any;
    token_description?: any;
    support?: any;
    resistance?: any;
    rsi?: any;
    sma?: any;
    total_liquidity?: any;
    total_holder_count?: any;
    holder_count_change_percentage_24h?: any;
    dev_wallet?: string;
    dev_wallet_balance?: any;
    dev_wallet_total_holding_percentage?: any;
    dev_wallet_outflow_txs_count_24h?: any;
    dev_wallet_outflow_amount_24h?: any;
    dev_wallet_inflow_amount_24h?: any;
    top_25_holder_buy_24h?: any;
    top_25_holder_sold_24h?: any;
    fifty_percentage_holding_wallet_count?: any;
}

export interface SignalModelAttributes {
    [key: string]: any;
    [key: symbol]: any;
    text: string,
    category: number,
    subCategory: number,
    tokenIds: string[],
    network: number
}

export interface TelegramMessageAttributes {
    tokenTicker: string,
    telegramText: string,
    network: string,
    tokenAddress?: string,
    devWallet?: string,
    createdAt?: string,
    virtualId?: number,
    tokenImage?: string,
    virtualsAppId?: number,
    addLokyDocsButton?: boolean,
    addLokyXButton?: boolean,
    addLokyHomePageButton?: boolean
}

export interface LokyTerminalBaseParamsInterface {
    internalDecodedParams: {
        currentUser?: any;
    };
    question: string;
    category: string;
    source: number;
    userId?: number;
}

export interface PrototypeTokenInfo {
    token_address?: string;
    creator?: string;
    name?: string;
    ticker?: string;
    pair?: string;
    agent_token?: string;
    supply?: Number;
    usd_price?: Number;
    market_cap_usd?: Number;
    liquidity_usd?: Number;
    volume_usd?: Number;
    volume_24h_usd?: Number;
    last_updated_at?: Date;
    description?: string;
    image?: string;
    socials: {
        twitter?: string;
        telegram?: string;
        youtube?: string;
        website?: string;
    };
}

export interface APILogsModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    id?: number | undefined;
    userId: number;
    apiKeyId: number;
    apiType: number;
    hostname: string;
    status: number;
    createdAt?: Date;
}

export interface WalletSafetyScoreModelAttributes {
    [key: string]: any;
    [key: number]: any;
    [key: symbol]: any;
    genesisId: number;
    genesisAddress: string;
    memberWalletAddress: string;
    walletTitle: string;
    safetyScore: number;
    walletAgePoints: number;
    walletTxActivityPoints: number;
    reasoning: string | null;
    createdAt?: Date;
    updatedAt?: Date;
}
