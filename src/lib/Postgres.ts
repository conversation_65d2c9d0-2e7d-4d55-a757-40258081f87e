import { InitOptions, Sequelize } from 'sequelize';
import GeneralConstant from '../config/GeneralConstant';
import fs from 'fs';
import User from '../models/User';
import ErrorLog from '../models/ErrorLog';
import UserQuestion from '../models/UserQuestion';
import Answer from '../models/Answer';
import AnswerExtraData from '../models/AnswerExtraData';
import TokenDetails from '../models/TokenDetails';
import Userfeedback from '../models/UserFeedback';
import Signal from '../models/Signal';
import UserSignal from '../models/UserSignal';
import WhitelistedUsers from '../models/WhitelistedUsers';
import PostLog from '../models/PostLog';
import PostReplyTracker from '../models/PostReplyTracker';
import LastPostTracker from '../models/LastPostTracker';
import PostQueueTracker from '../models/PostQueueTracker';
import PostCommentTracker from '../models/PostCommentTracker';
import MarketStatistics from '../models/MarketStatistics';
import TokenCandleData from '../models/TokenCandleData';
import TokenPools from '../models/TokenPools';
import UserAPIKey from '../models/UserAPIKey';
import UserAPIStats from '../models/UserAPIStats';
import FailedReplyQueue from '../models/FailedReplyQueue';
import TokenAddressMap from '../models/TokenAddressMap';
import TokenCategoryMap from '../models/TokenCategoryMap';
import SolanaCandleData from '../models/SolanaCandleData';
import LokyAPIs from '../models/LokyAPIs';
import MindshareData from '../models/MindshareData';
import RugScanner from "../models/RugScanner";
import UserWatchlist from '../models/UserWatchlist';
import LQAHistoricalMetric from '../models/LQAHistoricalMetric';
import DevBundleAndTopHolders from '../models/DevBundleAndTopHolders';
import TokenPreGraduateAddressMap from '../models/TokenPreGraduateAddressMap';
import APILog from '../models/ApiLog';
import UserWaitlist from '../models/UserWaitlist';
import ExtendedPoolDetails from '../models/ExtentedPoolDetails';
import ExtendedTAData from '../models/ExtendedTAData';
import RugWhitelistedAgent from '../models/RugWhitelistedAgent';
import GenesisRawData from '../models/GenesisRawData';
import GenesisLqaData from '../models/GenesisLqaData';
import StakedTokenMetrics from '../models/StakedTokenMetrics';
import EthereumCandleData from '../models/EthereumCandleData';
import UserWatchlistGenesis from '../models/UserWatchListGenesis';
import WalletSafetyScore from '../models/WalletSafetyScore';
import QuestionExtraData from '../models/QuestionExtraData';

export default class Postgres {
    public sequelize: Sequelize;

    public user: User;

    public errorLog: ErrorLog;

    public userQuestion: UserQuestion;

    public answer: Answer;

    public answerExtraData: AnswerExtraData;

    public tokenDetails: TokenDetails;

    public userFeedback: Userfeedback;

    public signal: Signal;

    public userSignal: UserSignal;

    public whitelistedUsers: WhitelistedUsers;

    public postLog: PostLog;

    public postReplyTracker: PostReplyTracker;

    public lastPostTracker: LastPostTracker;

    public postQueueTracker: PostQueueTracker;

    public postCommentTracker: PostCommentTracker;

    public marketStatistics: MarketStatistics;

    public tokenCandleData: TokenCandleData

    public tokenPools: TokenPools

    public userAPIKey: UserAPIKey;

    public userAPIStats: UserAPIStats;

    public failedReplyQueue: FailedReplyQueue;

    public tokenAddressMap: TokenAddressMap;

    public tokenCategoryMap: TokenCategoryMap;

    public solanaCandleData: SolanaCandleData;

    public lokyAPIs: LokyAPIs;

    public mindshareData: MindshareData;

    public rugScanner: RugScanner;

    public userWatchlist: UserWatchlist;

    public lqaHistoricalMetric: LQAHistoricalMetric;

    public devBundleAndTopHolders: DevBundleAndTopHolders;

    public tokenPreGraduateAddressMap: TokenPreGraduateAddressMap;

    public apiLog: APILog;

    public userWaitlist: UserWaitlist;

    public extendedPoolDetails: ExtendedPoolDetails;

    public extendedTAData: ExtendedTAData;

    public rugWhitelistedAgent: RugWhitelistedAgent;

    public genesisRawData: GenesisRawData;

    public genesisLqaData: GenesisLqaData;

    public stakedTokenMetrics: StakedTokenMetrics;

    public ethereumCandleData: EthereumCandleData;

    public userWatchlistGenesis: UserWatchlistGenesis;

    public walletSafetyScore: WalletSafetyScore;

    public questionExtraData: QuestionExtraData;

    private constructor(initOptions: InitOptions) {
        const oThis = this;
        oThis.sequelize = initOptions.sequelize;
        oThis.user = new User(initOptions);
        oThis.errorLog = new ErrorLog(initOptions);
        oThis.userQuestion = new UserQuestion(initOptions);
        oThis.answer = new Answer(initOptions);
        oThis.answerExtraData = new AnswerExtraData(initOptions);
        oThis.tokenDetails = new TokenDetails(initOptions);
        oThis.userFeedback = new Userfeedback(initOptions);
        oThis.signal = new Signal(initOptions);
        oThis.userSignal = new UserSignal(initOptions);
        oThis.whitelistedUsers = new WhitelistedUsers(initOptions);
        oThis.postLog = new PostLog(initOptions);
        oThis.postReplyTracker = new PostReplyTracker(initOptions);
        oThis.lastPostTracker = new LastPostTracker(initOptions);
        oThis.postQueueTracker = new PostQueueTracker(initOptions)
        oThis.postCommentTracker = new PostCommentTracker(initOptions)
        oThis.marketStatistics = new MarketStatistics(initOptions)
        oThis.tokenCandleData = new TokenCandleData(initOptions);
        oThis.tokenPools = new TokenPools(initOptions);
        oThis.userAPIKey = new UserAPIKey(initOptions);
        oThis.userAPIStats = new UserAPIStats(initOptions);
        oThis.failedReplyQueue = new FailedReplyQueue(initOptions);
        oThis.tokenAddressMap = new TokenAddressMap(initOptions);
        oThis.tokenCategoryMap = new TokenCategoryMap(initOptions);
        oThis.solanaCandleData = new SolanaCandleData(initOptions);
        oThis.lokyAPIs = new LokyAPIs(initOptions);
        oThis.rugScanner = new RugScanner(initOptions);
        oThis.mindshareData = new MindshareData(initOptions);
        oThis.userWatchlist = new UserWatchlist(initOptions);
        oThis.lqaHistoricalMetric = new LQAHistoricalMetric(initOptions);
        oThis.devBundleAndTopHolders = new DevBundleAndTopHolders(initOptions);
        oThis.tokenPreGraduateAddressMap = new TokenPreGraduateAddressMap(initOptions);
        oThis.apiLog = new APILog(initOptions);
        oThis.userWaitlist = new UserWaitlist(initOptions);
        oThis.extendedPoolDetails = new ExtendedPoolDetails(initOptions);
        oThis.extendedTAData = new ExtendedTAData(initOptions);
        oThis.rugWhitelistedAgent = new RugWhitelistedAgent(initOptions);
        oThis.genesisRawData = new GenesisRawData(initOptions);
        oThis.genesisLqaData = new GenesisLqaData(initOptions);
        oThis.stakedTokenMetrics = new StakedTokenMetrics(initOptions);
        oThis.ethereumCandleData = new EthereumCandleData(initOptions);
        oThis.userWatchlistGenesis = new UserWatchlistGenesis(initOptions);
        oThis.walletSafetyScore = new WalletSafetyScore(initOptions);
        oThis.questionExtraData = new QuestionExtraData(initOptions);
    }

    public static getSequelizeObject(url?: string): Sequelize {
        let connectionUrl = url ? url! : GeneralConstant.connectionUrl;
        const sequelize: Sequelize = new Sequelize(connectionUrl, Postgres.getDBConnectionConfig());
        return sequelize;
    }

    public static async getDbModels(): Promise<Postgres> {
        const sequelize: Sequelize = Postgres.getSequelizeObject();
        const initOptions: InitOptions = {
            sequelize,
            underscored: true,
            timestamps: true,
            freezeTableName: true,
        };
        const db = new Postgres(initOptions);
        return db;
    }

    public static async checkDBConnection(): Promise<boolean> {
        const sequelize: Sequelize = Postgres.getSequelizeObject();
        try {
            await sequelize.authenticate();
        } catch {
            return false;
        }
        return true;
    }

    static getDBConnectionConfig(checkSSL: boolean = true): any {
        let config: any = {
            logging: console.log,
            typeValidation: true,
            pool: {
                max: 3,
                min: 0,
                acquire: 30000,
                idle: 10000,
            },
        };
        if ((GeneralConstant.isProduction || GeneralConstant.isStaging) && checkSSL) {
            config.dialect = 'postgres';
            config.dialectOptions = {
                ssl: Postgres.getSSLConfigValue(),
            };
        }
        return config;
    }

    static getSSLConfigValue(): any {
        let sslConfig: any = {
            rejectUnauthorized: true,
            ca: fs.readFileSync(GeneralConstant.caCertPermissionFilePath),
        };
        return sslConfig;
    }
}
